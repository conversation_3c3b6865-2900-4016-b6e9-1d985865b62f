const fs = require('fs');
const tencentcloud = require('tencentcloud-sdk-nodejs');

// 替换为您的腾讯云 API 密钥
const SECRET_ID = 'AKID9Sl7ToAYyS0ZDj5aPMNJ999hC7CUg0og';
const SECRET_KEY = '1Dd9c7Meu8FHC5upjaQBqX9hDgAd7a2V';

// 文件路径
const INPUT_FILE = './todo.json'; // 替换为您的输入文件路径
const OUTPUT_FILE = './result.json';

// 初始化腾讯云翻译客户端
const TmtClient = tencentcloud.tmt.v20180321.Client;
const clientConfig = {
  credential: {
    secretId: SECRET_ID,
    secretKey: SECRET_KEY,
  },
  region: 'ap-guangzhou', // 可根据需要调整
  profile: {
    httpProfile: {
      endpoint: 'tmt.tencentcloudapi.com',
    },
  },
};
const client = new TmtClient(clientConfig);

// 翻译函数
const translateText = async (text) => {
  const params = {
    SourceText: text,
    Source: 'zh',
    Target: 'en',
    ProjectId: 0,
  };
  await new Promise((resolve) => {
    setTimeout(() => {
      resolve();
    }, 300);
  });
  try {
    const data = await client.TextTranslate(params);
    return data.TargetText;
  } catch (error) {
    console.error(`Error translating "${text}":`, error);
    return text; // 如果翻译失败，返回原文本
  }
};

// 主函数
const main = async () => {
  try {
    // 读取 JSON 文件
    const rawData = fs.readFileSync(INPUT_FILE, 'utf-8');
    const data = JSON.parse(rawData);

    const translatedData = {};
    for (const [key, value] of Object.entries(data)) {
      if (key && !value) {
        console.log(`Translating: ${key}`);
        translatedData[key] = await translateText(key);
      } else {
        translatedData[key] = value;
      }
    }

    // 保存翻译后的 JSON 文件
    fs.writeFileSync(OUTPUT_FILE, JSON.stringify(translatedData, null, 4), 'utf-8');
    console.log(`Translation completed. Output saved to ${OUTPUT_FILE}`);
  } catch (error) {
    console.error('Error processing file:', error);
  }
};

// 运行主函数
main();
