/**
 * 将代码中的I18N.t("")生成对应的资源文件；
 *
 * 调用方式 node ./generator.js targetPath
 *
 * @param targetPath 默认扫描../../web-src/js/components/下的文件。有需要可以输入路径；
 *
 */

const fs = require('fs');
const esm = require('esm')(module);
const path = require('path');
const traverse = require('@babel/traverse').default;
const { parse } = require('@babel/parser');

const { inExclude, workWithFile } = require('./utils');
const { exec } = require('child_process');

const dirs = ['../config/', '../src/'];

// 语法解析可视化 https://astexplorer.net/
const reg = /[\u4E00-\u9FA5]/;
const todo_path = `./todo.json`;
const exist_json = esm('./locales/en.hack.ts').default || {};
const todo_json = {};

async function transform(filepath) {
  let file = fs.readFileSync(filepath, 'utf8');
  const hasI18N = file.includes('I18N.t');

  if (hasI18N) {
    const ast = parse(file, {
      sourceType: 'module',
      plugins: ['jsx', 'typescript', 'classProperties'],
    });

    traverse(ast, {
      //将I18N.t里的中文抽取成key；
      CallExpression: {
        enter(pathNode) {
          const { node } = pathNode;
          const { arguments } = node;
          const value = pathNode.toString();
          if (value.startsWith('I18N.t(') && reg.test(value)) {
            let targetValue, targetKey;
            arguments.map((argsNode) => {
              if (argsNode.type === 'StringLiteral') {
                targetValue = argsNode.value;
              }
            });
            //没有指定的key则以value为key；
            targetKey = targetKey || targetValue;
            if (targetKey) {
              //当前资源或已存在的或待翻译就不再添加
              if (!exist_json[targetKey]) {
                console.log('new key:' + targetKey);
                todo_json[targetKey] = '';
              }
            }
          }
        },
      },
    });
  }
}

function work(filePath) {
  const stats = fs.statSync(filePath);
  const isFile = stats.isFile(); // 是文件
  const isDir = stats.isDirectory(); // 是文件夹
  if (isDir) {
    fs.readdirSync(filePath).forEach((fileName) => {
      if (!fileName.startsWith('.umi')) {
        // 遍历读取到的文件列表
        // 获取当前文件的绝对路径
        const file = path.join(filePath, fileName);
        // 根据文件路径获取文件信息，返回一个fs.Stats对象
        work(file);
      }
    });
  } else if (isFile && !inExclude(filePath) && workWithFile(filePath)) {
    transform(filePath);
  }
}
dirs.forEach((dir) => {
  const _path = path.resolve(__dirname, dir);
  work(_path);
});
fs.writeFileSync(todo_path, JSON.stringify(todo_json), 'utf-8');
exec(`prettier -w --config ../.prettierrc.js ${todo_path}`, (error, stdout, stderr) => {
  if (error) {
    console.error(error);
  } else {
    console.log('prettier success ', todo_path);
  }
});
