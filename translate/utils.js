const fs = require('fs');
const path = require('path');

module.exports = {
  parseFilePath(target) {
    target = path.resolve(__dirname, target);
    const pathArray = target.split('/');
    const dirArray = pathArray.slice(0, -1);
    const fileName = pathArray[pathArray.length - 1];
    const fileNameArray = fileName.split('.');
    const nameArray = fileNameArray.slice(0, -1);
    return {
      dir: dirArray.join('/'),
      path: target,
      fullName: fileName,
      name: nameArray.join('.').replace('.react', ''),
      type: fileNameArray[fileNameArray.length - 1],
    };
  },

  /**
   * 在某些特定文件夹下
   * @param fileName
   */
  inExclude(fileName) {
    return /i18n/i.test(fileName);
  },

  writeFile(filepath, string) {
    fs.writeFileSync(filepath, string, 'utf-8');
  },

  workWithFile(fileName) {
    return /(\.js|\.jsx|\.ts|\.tsx)$/g.test(fileName);
  },
};
