{"name": "ant-design-pro", "version": "5.0.0-beta.2", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 umi build", "build": "cross-env IS_PRODUCTION=true umi build", "build:client": "cross-env UMI_ENV=client umi build", "build:browser": "cross-env UMI_ENV=browser umi build", "build:development": "cross-env NODE_OPTIONS=\"--max-old-space-size=9000\" IS_PRODUCTION=false umi build", "deploy": "npm run site && npm run gh-pages", "dev": "npm run start:dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "umi g tmp", "lint": "umi g tmp && npm run lint:js && npm run lint:style && npm run lint:prettier", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier --check \"src/**/*\" --end-of-line auto", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "openapi": "umi open<PERSON>i", "precommit": "lint-staged", "prettier": "prettier -c --write \"src/**/*\"", "start": "cross-env NODE_OPTIONS=\"--max-old-space-size=8000 --openssl-legacy-provider\" PORT=8005 UMI_ENV=dev ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION=site umi dev", "start:fast": "cross-env NODE_OPTIONS=\"--max-old-space-size=8000 --openssl-legacy-provider\" PORT=8005 UMI_ENV=dev ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION=site FAST_DEV=true umi dev", "dev:optimize": "node scripts/dev-optimize.js", "dev:clean": "node scripts/dev-optimize.js && npm run start:fast", "pretest": "node ./tests/beforeTest", "test": "umi test", "test:all": "node ./tests/run-tests.js", "test:component": "umi test ./src/components", "tsc": "tsc --noEmit", "downloadApiJsonFromSwagger": "node ./tools/downloadApiJsonFromSwagger.js && umi openapi && echo '使用以下命令提交代码：git add src/services/ config/api*'"}, "lint-staged": {"**/*.less": "stylelint --syntax less", "**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "main": "electron/main.js", "dependencies": {"@ant-design/charts": "^1.4.2", "@ant-design/pro-form": "^2.3.3", "@ant-design/pro-layout": "^6.38.22", "@ant-design/pro-list": "^2.5.51", "@ant-design/pro-table": "^3.1.9", "@dnd-kit/core": "^6.0.8", "@dnd-kit/modifiers": "^6.0.1", "@dnd-kit/sortable": "^7.0.2", "@gsap/react": "^2.1.0", "@react-spring/web": "^9.5.4", "@umijs/openapi": "^1.1.14", "@umijs/plugin-openapi": "^1.2.0", "@umijs/route-utils": "^1.0.36", "@use-gesture/react": "^10.2.20", "ahooks": "^2.10.14", "ali-oss": "^6.17.0", "android-versions": "^2.1.0", "antd": "^4.24.3", "axios": "^0.27.2", "bowser": "^2.11.0", "bytes": "^3.1.2", "classnames": "^2.2.6", "copy-to-clipboard": "^3.3.1", "crypto-js": "^4.2.0", "dompurify": "^2.4.3", "file-saver": "^2.0.5", "form-data": "^4.0.0", "gsap": "^3.12.5", "highlight.js": "^11.11.1", "howler": "^2.2.3", "http-parser-js": "^0.5.6", "i18n-iso-countries": "^7.10.0", "iconv-lite": "^0.4.24", "isomorphic-form-data": "^2.0.0", "js-cookie": "^3.0.1", "js-yaml": "^4.1.0", "keymirror": "^0.1.1", "lodash": "^4.17.11", "lottie-react": "^2.3.1", "marked": "^4.2.12", "moment": "^2.25.3", "moment-timezone": "^0.5.33", "mrmime": "^1.0.1", "node-machine-id": "^1.1.12", "omit.js": "^2.0.2", "p-min-delay": "^4.0.1", "phone": "^3.1.44", "pretty-ms": "^7.0.1", "qrcode.react": "^3.1.0", "quill": "^2.0.3", "rc-overflow": "^1.2.8", "re-resizable": "^6.10.1", "react": "^17.0.0", "react-activation": "^0.12.2", "react-contexify": "^6.0.0", "react-cropper": "^2.1.8", "react-dev-inspector": "^1.1.1", "react-dom": "^17.0.0", "react-draggable": "^4.4.3", "react-error-catcher": "^1.2.0", "react-fast-marquee": "^1.2.1", "react-helmet-async": "^1.0.4", "react-infinite-scroll-component": "^6.1.0", "react-script-hook": "^1.5.0", "socket.io-client": "^4.5.2", "styled-components": "^6.1.11", "swagger-typescript-api": "^9.1.2", "tiny-pinyin": "^1.3.2", "umi": "^3.5.0", "umi-request": "^1.0.8", "video.js": "^8.23.3", "virtualizedtableforantd4": "^1.2.2", "vm2": "^3.9.10", "zustand": "3"}, "devDependencies": {"@ant-design/pro-cli": "^2.0.2", "@babel/plugin-transform-runtime": "^7.15.0", "@koa/router": "^10.0.0", "@tsconfig/create-react-app": "^1.0.2", "@tsconfig/node14": "^1.0.1", "@types/ali-oss": "^6.0.10", "@types/android-versions": "^1.8.3", "@types/bytes": "^3.1.1", "@types/classnames": "^2.2.7", "@types/crypto-js": "^4.0.2", "@types/express": "^4.17.0", "@types/ffi-napi": "^4.0.4", "@types/file-saver": "^2.0.5", "@types/history": "^4.7.2", "@types/howler": "^2.2.7", "@types/http-proxy": "^1.17.6", "@types/jest": "^26.0.0", "@types/js-cookie": "^3.0.2", "@types/js-yaml": "^4.0.5", "@types/jszip": "^3.4.1", "@types/keymirror": "^0.1.1", "@types/koa": "^2.13.4", "@types/koa-bodyparser": "^4.3.5", "@types/koa-static": "^4.0.2", "@types/koa__router": "^8.0.7", "@types/lodash": "^4.14.144", "@types/lowdb": "^1.0.10", "@types/papaparse": "^5.3.2", "@types/prismjs": "^1.26.0", "@types/qrcode.react": "^1.0.1", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@types/react-helmet": "^6.1.0", "@types/react-infinite-scroll-component": "^5.0.0", "@types/react-lottie": "^1.2.6", "@types/ref-napi": "^3.0.4", "@types/ref-struct-di": "^1.1.5", "@types/ssh2": "^1.11.6", "@types/ua-parser-js": "^0.7.36", "@types/ws": "^7.4.7", "@umijs/fabric": "^2.5.2", "@umijs/plugin-blocks": "^2.0.5", "@umijs/plugin-esbuild": "^1.0.1", "@umijs/preset-ant-design-pro": "^1.3.3", "@umijs/preset-dumi": "^1.1.7", "@umijs/preset-react": "^2.1.6", "@umijs/yorkie": "^2.0.3", "autoprefixer": "^10.3.3", "babel-loader": "^8.2.2", "carlo": "^0.9.46", "chokidar": "^3.5.3", "clean-webpack-plugin": "^4.0.0", "concurrently": "^6.2.0", "copy-webpack-plugin": "^9.0.1", "cross-env": "^7.0.0", "cross-node-rdp": "1.0.9", "cross-port-killer": "^1.1.1", "css-loader": "^6.2.0", "detect-installer": "^1.0.1", "enzyme": "^3.11.0", "eslint": "^7.1.0", "express": "^4.17.1", "extract-zip": "^2.0.1", "file-loader": "^6.2.0", "fs-extra": "^10.0.0", "gh-pages": "^3.0.0", "html-webpack-plugin": "^5.3.2", "jsdom-global": "^3.0.2", "koa": "^2.13.1", "koa-bodyparser": "^4.3.0", "koa-static": "^5.0.0", "lint-staged": "^10.0.0", "lowdb": "^1.0.0", "mini-css-extract-plugin": "^2.2.2", "mockjs": "^1.0.1-beta3", "nodejs-file-downloader": "^4.7.4", "nugget": "^2.0.1", "papaparse": "^5.3.2", "postcss": "^8.3.6", "postcss-loader": "^6.1.1", "prettier": "^2.0.1", "ref-struct-di": "^1.1.1", "resolve-url-loader": "^4.0.0", "socks-proxy-agent": "^7.0.0", "stylelint": "^13.0.0", "ts-loader": "^9.2.3", "ts-node": "^10.9.1", "typescript": "^4.3.5", "ua-parser-js": "^0.7.28", "url-loader": "^4.1.1", "webpack": "^5.45.1", "webpack-cli": "^4.7.2", "webpack-node-externals": "^3.0.0", "wext-manifest-loader": "^2.3.0", "wext-manifest-webpack-plugin": "^1.2.1", "wildcard-match": "^5.1.2", "ws": "^7.5.2"}, "engines": {"node": ">=10.0.0"}, "resolutions": {"@types/react": "17.0.75", "@types/react-dom": "17.0.25"}}