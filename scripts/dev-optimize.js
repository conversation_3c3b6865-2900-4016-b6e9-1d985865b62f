#!/usr/bin/env node

/**
 * 开发环境优化脚本
 * 用于清理缓存、优化开发体验
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const projectRoot = path.resolve(__dirname, '..');

// 需要清理的缓存目录
const cacheDirs = [
  '.umi',
  '.umi-production', 
  '.umi-cache',
  'node_modules/.cache',
  '.tsbuildinfo'
];

// 清理缓存函数
function clearCache() {
  console.log('🧹 清理开发缓存...');
  
  cacheDirs.forEach(dir => {
    const fullPath = path.join(projectRoot, dir);
    if (fs.existsSync(fullPath)) {
      try {
        if (fs.statSync(fullPath).isDirectory()) {
          execSync(`rm -rf "${fullPath}"`, { stdio: 'inherit' });
        } else {
          fs.unlinkSync(fullPath);
        }
        console.log(`✅ 已清理: ${dir}`);
      } catch (error) {
        console.log(`❌ 清理失败: ${dir} - ${error.message}`);
      }
    }
  });
}

// 检查 Node.js 版本
function checkNodeVersion() {
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion < 14) {
    console.warn('⚠️  建议使用 Node.js 14+ 以获得更好的开发体验');
  } else {
    console.log(`✅ Node.js 版本: ${nodeVersion}`);
  }
}

// 检查内存使用
function checkMemoryUsage() {
  const used = process.memoryUsage();
  const totalMB = Math.round(used.heapTotal / 1024 / 1024);
  const usedMB = Math.round(used.heapUsed / 1024 / 1024);
  
  console.log(`📊 内存使用: ${usedMB}MB / ${totalMB}MB`);
  
  if (usedMB > 1000) {
    console.warn('⚠️  内存使用较高，建议重启开发服务器');
  }
}

// 优化建议
function showOptimizationTips() {
  console.log('\n💡 开发环境优化建议:');
  console.log('1. 使用 npm run start:fast 启动快速开发模式');
  console.log('2. 定期运行 node scripts/dev-optimize.js 清理缓存');
  console.log('3. 关闭不必要的浏览器标签页和应用程序');
  console.log('4. 使用 SSD 硬盘可显著提升性能');
  console.log('5. 考虑增加系统内存到 16GB+');
}

// 主函数
function main() {
  console.log('🚀 开发环境优化工具\n');
  
  checkNodeVersion();
  checkMemoryUsage();
  clearCache();
  showOptimizationTips();
  
  console.log('\n✨ 优化完成！');
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  clearCache,
  checkNodeVersion,
  checkMemoryUsage,
  showOptimizationTips
};
