/**
 * 分身用户偏好设置
 */
import useCurrentUser from '@/hooks/useCurrentUser';
import type { Key } from 'react';
import { useMemo, useState } from 'react';
import useCurrentTeam from '@/hooks/useCurrentTeam';
import _ from 'lodash';
import { useRequest } from 'umi';
import { shopFieldConfigGet } from '@/services/api-ShopAPI/ShopController';
import I18N from '@/i18n/I18N';

type ColumnType =
  | 'shop'
  | 'ip'
  | 'fingerprint'
  | 'fpInsList'
  | 'rpaTaskList'
  | 'ipPool'
  | 'ipp-ip-list'
  | 'ip_platform'
  | 'ipGo';
const DEFAULT_SHOP_COLUMNS = [
  'markCode',
  'name',
  'platform',
  'area',
  'fingerprint',
  'ip',
  'tags',
  'speechGroup',
  'option',
];
const DEFAULT_IP_COLUMNS = [
  'name',
  'outboundIp',
  'type',
  'valid_end_date',
  'testing_time',
  I18N.isCn() ? 'locationCn' : 'locationEn',
  'shops',
  'tags',
  'option',
];
const DEFAULT_IP_PLATFORM_COLUMNS = [
  'name',
  'outboundIp',
  'type',
  'period_unit',
  'auto_renew',
  'create_time',
  'valid_end_date',
  'shops',
  'option',
];
const DEFAULT_FP_COLUMNS = ['md5sum', 'duplicateStatus', 'spec', 'shop', 'opt'];
const DEFAULT_FP_INS_COLUMNS = [
  'md5sum',
  'duplicateStatus',
  'createType',
  'origin',
  'platform',
  'browser',
  'shop',
  'opt',
];
const DEFAULT_RPA_TASK_COLUMNS = [
  'name',
  'flowName',
  'accountList',
  'createTime',
  'executeEndTime',
  'deviceName',
  'creatorId',
  'status',
  'opt',
];
const DEFAULT_IP_POOL_COLUMNS = [
  'name',
  'provider',
  'lifetime',
  'location',
  'createTime',
  'shops',
  'tagsId',
  'option',
];
const DEFAULT_IP_POOL_IP_LIST_COLUMNS = [
  'outboundIp',
  'port',
  'proxyType',
  'status',
  'traffic',
  'shops',
  'createTime',
  'expireTime',
  'option',
];
const DEFAULT_IP_GO_LIST_COLUMNS = [
  'host_name',
  'spec',
  'ips',
  'create_time',
  'update_time',
  'status',
  'option',
];
function getColumns(type: ColumnType) {
  if (type === 'shop') return DEFAULT_SHOP_COLUMNS;
  if (type === 'ip') return DEFAULT_IP_COLUMNS;
  if (type === 'ip_platform') return DEFAULT_IP_PLATFORM_COLUMNS;
  if (type === 'fingerprint') return DEFAULT_FP_COLUMNS;
  if (type === 'fpInsList') return DEFAULT_FP_INS_COLUMNS;
  if (type === 'rpaTaskList') return DEFAULT_RPA_TASK_COLUMNS;
  if (type === 'ipPool') return DEFAULT_IP_POOL_COLUMNS;
  if (type === 'ipp-ip-list') return DEFAULT_IP_POOL_IP_LIST_COLUMNS;
  if (type === 'ipGo') return DEFAULT_IP_GO_LIST_COLUMNS;
  return [];
}

/**
 * 当前用户在当前团队 默认的表格columns配置key
 * @param type
 */
export function useDefaultColumns(type: ColumnType) {
  const user = useCurrentUser();
  const team = useCurrentTeam();
  return useMemo(() => {
    const itemKey = `${user?.id}-${team?.id}-${type}-columns`;
    const defaultColumns = getColumns(type);
    return {
      get() {
        const value = localStorage.getItem(itemKey);
        let columns: string[] = [];
        try {
          if (typeof value === 'string') {
            columns = JSON.parse(value);
          } else {
            columns = defaultColumns;
          }
        } catch (e) {
          columns = defaultColumns;
        }
        if (type === 'ip') {
          // fix 客户端IP名称没有的问题
          columns.unshift('name');
        }
        columns = _.uniq(columns);
        return columns;
      },
      set(value: Key[]) {
        return localStorage.setItem(itemKey, JSON.stringify(value));
      },
    };
  }, [team?.id, type, user?.id]);
}

export function useShopColumns() {
  const user = useCurrentUser();
  const team = useCurrentTeam();
  const itemKey = useMemo(() => `${user?.id}-${team?.id}-browser-columns`, [user, team]);
  const [columns, setColumns] = useState(DEFAULT_SHOP_COLUMNS);
  const [globalSync, setGlobalSync] = useState(true);
  const { run } = useRequest(
    async () => {
      return shopFieldConfigGet();
    },
    {
      formatResult(res) {
        setGlobalSync(!!res.data?.globalField);
        if (!res.data?.globalField) {
          const value = localStorage.getItem(itemKey);
          let _columns: string[] = [];
          try {
            if (typeof value === 'string') {
              _columns = JSON.parse(value);
            } else {
              _columns = DEFAULT_SHOP_COLUMNS;
            }
          } catch (e) {
            _columns = DEFAULT_SHOP_COLUMNS;
          }
          _columns = _.uniq(_columns);
          setColumns(_columns);
          return _columns;
        }
        return setColumns(res.data?.fields! || DEFAULT_SHOP_COLUMNS);
      },
    },
  );

  return {
    columns,
    set(value: string[]) {
      localStorage.setItem(itemKey, JSON.stringify(value));
      run();
    },
    globalSync,
  };
}
