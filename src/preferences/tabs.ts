import useCurrentUser from '@/hooks/useCurrentUser';
import useCurrentTeam from '@/hooks/useCurrentTeam';
import { useMemo } from 'react';

/**
 *
 * @param module
 */
export function useDefaultTab(module: string) {
  const user = useCurrentUser();
  const team = useCurrentTeam();
  return useMemo(() => {
    const moduleName = `${user?.id}-${team?.id}-${module}-tab`;
    return {
      set(value: string) {
        localStorage.setItem(moduleName, value);
      },
      get() {
        return localStorage.getItem(moduleName) || '';
      },
    };
  }, [user?.id, team?.id, module]);
}
