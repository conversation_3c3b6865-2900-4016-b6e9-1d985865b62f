import I18N from '@/i18n';
import React, { useEffect } from 'react';
import ErrorCatcher from 'react-error-catcher';
import { UseRequestProvider } from 'umi';
import useSysBroadcast from '@/hooks/useSysBroadcast';
import ErrorBlock from '../Common/ErrorBlock';
import { initTz } from '@/utils/utils';
import { DMContextProvider } from '@/components/Common/Modal/DMModal';

initTz();
const Index: React.FC = (props) => {
  useSysBroadcast();
  useEffect(() => {
    console.log('Error Mounted');
    return () => {
      console.log('Error Unmounted');
    };
  }, []);

  return (
    <ErrorCatcher
      errorRender={<ErrorBlock error={new Error(I18N.t('出错了'))} />}
      onCatch={(errors) => {
        console.error(errors);
      }}
    >
      <UseRequestProvider
        value={{
          throttleInterval: 500,
        }}
      >
        <DMContextProvider>{props.children}</DMContextProvider>
      </UseRequestProvider>
    </ErrorCatcher>
  );
};

export default Index;
