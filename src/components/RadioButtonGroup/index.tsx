import { But<PERSON>, Col, Row } from 'antd';
import { useState } from 'react';
import { ProFormText } from '@ant-design/pro-form';
import styles from './index.less';
import type { ProFormFieldItemProps } from '@ant-design/pro-form/lib/interface';
import classNames from 'classnames';

interface Item {
  key: any;
  label: any;
  disabled?: boolean;
}
interface Props extends ProFormFieldItemProps {
  options: Item[];
  value: any;
  disabled?: boolean;
  onChange?: (value: any) => void;
}
const RadioButtonGroup = (props: Props) => {
  const { options, disabled, onChange, ...otherProps } = props;
  const [value, setValue] = useState(props.value);
  return (
    <Row gutter={8} className={classNames(styles.group, 'radio-button-group')}>
      {options.map((item) => {
        const active = value === item.key;
        return (
          <Col span={24 / options.length} key={item.key}>
            <Button
              className={styles.btn}
              disabled={item.disabled || disabled}
              onClick={() => {
                setValue(item.key);
                if (onChange) {
                  onChange(item.key);
                }
              }}
              type={active ? 'primary' : 'default'}
            >
              {item.label}
            </Button>
          </Col>
        );
      })}
      <ProFormText hidden {...otherProps} />
    </Row>
  );
};

export default RadioButtonGroup;
