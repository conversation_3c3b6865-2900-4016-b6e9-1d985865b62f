import React from 'react';
import DMFormItem from '@/components/Common/DMFormItem';
import { InputNumber, Radio, Space, Tooltip, Typography } from 'antd';
import I18N from '@/i18n';
import IconFontIcon from '@/components/Common/IconFontIcon';

export const WindowSizeCon: React.FC<{ display: Display; style?: React.CSSProperties }> = (
  props,
) => {
  const { display, style } = props;

  return (
    <DMFormItem name="sizeMethod" initialValue="auto" style={style}>
      <Radio.Group>
        <Space direction="vertical" size={16}>
          <Radio value="auto" style={{ lineHeight: '30px' }}>
            {I18N.t('自动调整窗口大小')}
            <span style={{ color: '#999' }}>{I18N.t('（确保摆放全部窗口）')}</span>
          </Radio>
          <Radio value="custom" style={{ lineHeight: '30px' }}>
            {I18N.t('指定窗口大小：')}

            <Space>
              <span>{I18N.t('宽')}</span>
              <DMFormItem noStyle shouldUpdate>
                {(f) => {
                  const disabled = f.getFieldValue('sizeMethod') !== 'custom';
                  return (
                    <DMFormItem
                      name={'width'}
                      initialValue={800}
                      noStyle
                      rules={[
                        {
                          required: !disabled,
                          message: I18N.t('请输入窗口大小'),
                        },
                      ]}
                    >
                      <InputNumber
                        disabled={disabled}
                        min={500}
                        step={100}
                        max={display?.workAreaSize?.width}
                      />
                    </DMFormItem>
                  );
                }}
              </DMFormItem>
              <DMFormItem noStyle shouldUpdate>
                {(f) => {
                  const disabled = f.getFieldValue('sizeMethod') !== 'custom';
                  return (
                    <DMFormItem
                      name={'height'}
                      initialValue={600}
                      noStyle
                      rules={[
                        {
                          required: !disabled,
                          message: I18N.t('请输入窗口大小'),
                        },
                      ]}
                    >
                      <InputNumber
                        disabled={disabled}
                        min={375}
                        step={100}
                        max={display?.workAreaSize?.height}
                      />
                    </DMFormItem>
                  );
                }}
              </DMFormItem>
              <span>{I18N.t('高')}</span>
            </Space>
          </Radio>
        </Space>
      </Radio.Group>
    </DMFormItem>
  );
};

/**
 * 并发数
 * @constructor
 */
export function ConcurrentCon({ value, onChange, form, disabled = false }: any) {
  let tooltipCon = I18N.t(
    '并发数量指的是同时打开的浏览器数量，该数量和运行设备的性能紧密相关，一般而言，4核的CPU请不要超过10，8核的CPU请不要超过20',
  );

  if (form?.getFieldValue('provider')) {
    tooltipCon = I18N.t('云端执行时，并发数量为1且不可调整，意味着每个分身依次串行');
  }
  return (
    <Space>
      <InputNumber value={value} disabled={disabled} onChange={onChange} min={1} />
      <span>
        {I18N.t('个')}

        <Tooltip title={tooltipCon}>
          <Typography.Link>
            <IconFontIcon iconName="bangzhu_24" style={{ marginLeft: 20 }} />
          </Typography.Link>
        </Tooltip>
      </span>
    </Space>
  );
}

/**
 * 并发间隔
 * @param value
 * @param onChange
 * @param disabled
 * @constructor
 */
export function ConcurrentDelayCon({ value, onChange, disabled = false }: any) {
  return (
    <Space>
      <InputNumber value={value} disabled={disabled} onChange={onChange} min={5} />
      <span>
        {I18N.t('秒')}
        <Tooltip
          title={I18N.t(
            '并发间隔指的是打开一个新的浏览器的间隔时长，并发间隔过短极易造成运行设备的瞬间负载过高并导致失败，最小值为5秒，最大值为3600秒',
          )}
        >
          <Typography.Link>
            <IconFontIcon iconName="bangzhu_24" style={{ marginLeft: 20 }} />
          </Typography.Link>
        </Tooltip>
      </span>
    </Space>
  );
}
type Bounds = {
  x: number;
  y: number;
  width: number;
  height: number;
};
export interface Display {
  id: number;
  label: string;
  bounds: Bounds;
  workArea: Bounds;
  size: Pick<Bounds, 'width' | 'height'>;
  workAreaSize: Pick<Bounds, 'width' | 'height'>;
}
