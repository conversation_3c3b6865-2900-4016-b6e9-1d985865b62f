import React, { useCallback, useEffect } from 'react';
import { useModalCaller } from '@/mixins/modal';
import DMModal from '@/components/Common/Modal/DMModal';
import I18N from '@/i18n';
import GridLayoutForm from './GridLayoutForm';
import { Form } from 'antd';
import DMFormItem from '@/components/Common/DMFormItem';
import { ConcurrentCon, ConcurrentDelayCon } from '@/components/WindowSync/util';

type Props = {
  initialValues: Record<string, any>;
  onFinish: (values: any) => void;
  onClose: () => void;
};

/**
 *
 * @param props
 * @constructor
 */
const GridLayoutFormModal: React.FC<Props> = (props) => {
  const { initialValues, onFinish, onClose } = props;
  const [extendForm] = Form.useForm();
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue({
      ...initialValues,
      vs: initialValues.vs ?? 0,
      hs: initialValues.hs ?? 0,
    });
  }, [form, initialValues]);

  return (
    <DMModal
      visible
      title={I18N.t('窗口布局设置')}
      onCancel={onClose}
      onOk={async () => {
        await form.validateFields();
        const extendFormValues = extendForm.getFieldsValue();
        const values = form.getFieldsValue();
        onFinish({
          ...extendFormValues,
          ...values,
        });
        onClose();
      }}
    >
      <Form form={extendForm}>
        <DMFormItem
          label={I18N.t('并发数量')}
          name="concurrent"
          initialValue={initialValues?.concurrent ?? 9}
          rules={[
            {
              validator: (rule, value) => {
                if (value < 1 || value > 100) {
                  return Promise.reject(new Error(I18N.t('并发数量在1～100之间')));
                }
                return Promise.resolve();
              },
            },
          ]}
          validateTrigger="onChange"
        >
          <ConcurrentCon />
        </DMFormItem>
        <DMFormItem
          label={I18N.t('并发间隔')}
          name="concurrentDelay"
          initialValue={initialValues?.concurrentDelay ?? 5}
          rules={[
            {
              validator: (rule, value) => {
                if (value < 5 || value > 3600) {
                  return Promise.reject(new Error(I18N.t('并发间隔在5～3600秒之间')));
                }
                return Promise.resolve();
              },
            },
          ]}
          validateTrigger="onChange"
        >
          <ConcurrentDelayCon />
        </DMFormItem>
      </Form>
      <GridLayoutForm form={form} />
    </DMModal>
  );
};

export default GridLayoutFormModal;

export function useGridLayoutFormModal() {
  const modalCaller = useModalCaller(true);
  return useCallback(
    (props: Omit<Props, 'onClose'>) => {
      modalCaller({
        component: ({ close }) => <GridLayoutFormModal {...props} onClose={close} />,
      });
    },
    [modalCaller],
  );
}
