import styled from 'styled-components';
import type { CSSProperties } from 'react';
import { useEffect, useMemo, useState } from 'react';
import { Image as AntImage, Space } from 'antd';
import IconFontIcon from '@/components/Common/IconFontIcon';
import I18N from '@/i18n';

const StyleImage = styled.div<{ loaded: boolean }>`
  display: inline-flex;
  vertical-align: top;
  align-items: center;
  justify-content: center;
  border: 1px solid #dddddd;
  box-sizing: content-box;
  overflow: hidden;
  .ant-image {
    display: block;
    width: 100%;
    height: 100%;
  }
  img {
    display: block;
    width: 100%;
    height: 100%;
    ${(props) => {
      return `transform:scale(${props.loaded ? '1' : '0.66'})`;
    }}
  }
`;
const FallbackImage = (props: {
  src?: string;
  size?: [number, number];
  style?: CSSProperties;
  boxShadow?: boolean;
  onPreview?: () => void;
}) => {
  const { src, size = [32, 32], boxShadow, style, onPreview, ...rest } = props;
  const showPreviewIcon = useMemo(() => {
    return !!onPreview;
  }, [onPreview]);
  const [loaded, setLoaded] = useState(false);
  const _style: CSSProperties = useMemo(() => {
    return {
      width: size[0],
      height: size[1],
      boxShadow: boxShadow && loaded ? '0 0 5px 0 rgba(0,0,0.05)' : 'none',
      ...(style || {}),
    };
  }, [boxShadow, loaded, size, style]);
  useEffect(() => {
    let img: HTMLImageElement | null = null;
    img = new Image();
    img.onload = () => {
      setLoaded(true);
    };
    img.onerror = () => {
      setLoaded(false);
    };
    img.src = src || '';
    return () => {
      if (img) {
        img = null;
      }
    };
  }, [src]);
  const _previewConfig = useMemo(() => {
    if (showPreviewIcon) {
      return {
        mask: (
          <Space size={4}>
            <IconFontIcon iconName={'Eye-Open_24'} />
            {size[0] >= 64 && <span>{I18N.t('详情')}</span>}
          </Space>
        ),
        visible: false,
      };
    }
    return false;
  }, [showPreviewIcon, size]);
  return (
    <StyleImage
      loaded={loaded}
      className={'fallback-image'}
      style={_style}
      onClick={(e) => {
        e.stopPropagation();
        onPreview?.();
      }}
      {...rest}
    >
      <AntImage preview={_previewConfig} src={loaded ? src : '/404.svg'} />
    </StyleImage>
  );
};
export default FallbackImage;
