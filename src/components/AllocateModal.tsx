import I18N from '@/i18n';
import React, { useState } from 'react';
import type { GhostModalWrapperComponentProps } from '@/mixins/modal';
import { GhostModalCaller } from '@/mixins/modal';
import DMModal from '@/components/Common/Modal/DMModal';
import { Alert, Radio, Space, Typography } from 'antd';
import UserList from '@/components/Common/AuthModal/UserList';
import { useRequest } from '@@/plugin-request/request';

type Props = GhostModalWrapperComponentProps & {
  title?: string;
  tips?: string;
  onSubmit: (
    action: 'allocate' | 'cancel',
    data: { userId: number; tag?: string },
  ) => Promise<void>;
};

/**
 * 选择团队成员
 * @param props
 * @constructor
 */
const AllocateModal: React.FC<Props> = (props) => {
  const { title, tips, onSubmit, modalProps } = props;
  const [selectedUserId, setSelectedUserId] = useState<number>();
  const [visible, setVisible] = useState(true);
  const [action, setAction] = useState<'allocate' | 'cancel'>('allocate');
  const { run: submit, loading } = useRequest(
    async () => {
      await onSubmit(action, {
        userId: selectedUserId!,
        tag: undefined,
      });
      setVisible(false);
    },
    {
      manual: true,
    },
  );
  return (
    <DMModal
      open={visible}
      width={640}
      title={title}
      confirmLoading={loading}
      okButtonProps={{ disabled: action === 'allocate' && !selectedUserId }}
      onOk={submit}
      onCancel={() => {
        setVisible(false);
      }}
      {...modalProps}
    >
      {tips && <Alert style={{ marginBottom: 16 }} showIcon type={'info'} message={tips} />}
      <Radio
        checked={action === 'allocate'}
        onClick={() => {
          setAction('allocate');
        }}
      >
        <Space>
          <Typography.Text>{I18N.t('将所选达人分配给下属员工（暨BD）')}</Typography.Text>
          <Typography.Text type={'secondary'}>
            {I18N.t('（员工可在“分配给我的”达人中查看）')}
          </Typography.Text>
        </Space>
      </Radio>
      <div style={{ paddingLeft: 24, paddingTop: 16 }}>
        <div
          style={{
            display: 'flex',
            height: 300,
            flexDirection: 'column',
            overflow: 'auto',
            paddingLeft: 12,
          }}
        >
          <UserList
            type={'radio'}
            mode={action === 'allocate' ? 'change' : 'disable'}
            selected={selectedUserId ? [selectedUserId] : []}
            onChange={(ids) => {
              setSelectedUserId(ids[0]);
            }}
            onRowClick={(user) => {
              setSelectedUserId(user.id);
            }}
          />
        </div>
      </div>
      <div style={{ paddingTop: 12 }}>
        <Radio
          checked={action === 'cancel'}
          onClick={() => {
            setAction('cancel');
          }}
        >
          <Space>
            <Typography.Text>{I18N.t('将所选达人取消分配')}</Typography.Text>
            <Typography.Text type={'secondary'}>
              {I18N.t('（达人将回归至团队达人库）')}
            </Typography.Text>
          </Space>
        </Radio>
      </div>
    </DMModal>
  );
};

export default AllocateModal;

export function openAllocateModal(onSubmit: Props['onSubmit']) {
  GhostModalCaller(
    <AllocateModal
      onSubmit={onSubmit}
      title={I18N.t('分配达人')}
      tips={I18N.t(
        '将达人分配给团队成员（暨BD），团队成员可在“分配给我的达人”中对这些达人进行管理，如果已有认领人则覆盖',
      )}
    />,
  );
}
