import I18N from '@/i18n';
import { Button, Form, Input, Modal, Space, Typography } from 'antd';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { trimValues } from '@/utils/utils';
import { teamByTeamIdPut } from '@/services/api-Account/TeamController';
import IconFontIcon from '@/components/Common/IconFontIcon';
import styles from './index.less';
import { useForm } from 'antd/es/form/Form';
import useCurrentTeam, { fetchCurrentTeam } from '@/hooks/useCurrentTeam';
import { fetchTeams } from '@/hooks/useTeams';
import _ from 'lodash';

interface Props {
  onUpdate: () => void;
}
const TodoList = [
  I18N.t('正在为您导入浏览器指纹模板'),
  I18N.t('正在为您创建浏览器指纹实例'),
  I18N.t('正在为您导入TKShop流程定义'),
];

const DoneList = [
  I18N.t('浏览器指纹模板已成功导入'),
  I18N.t('浏览器指纹实例已成功创建'),
  I18N.t('TKShop流程定义已成功导入'),
];

const InitializeModal: React.FC<Props> = (props) => {
  const { onUpdate } = props;
  const team = useCurrentTeam();
  const [visible, changeVisible] = useState(true);
  const [step, changeStep] = useState(0);
  const [statusMap, changeStatusMap] = useState({});
  const timer = useRef();
  const onClose = useCallback(async () => {
    changeVisible(false);
    if (step === 1) {
      fetchTeams();
      fetchCurrentTeam();
      onUpdate();
    }
  }, [onUpdate, step]);

  const [form] = useForm();
  const [errorInfo, setErrorInfo] = useState<string | null>(null);
  const beginLoading = useCallback(() => {
    if (_.size(statusMap) < _.size(DoneList)) {
      timer.current = setTimeout(() => {
        changeStatusMap((prevState) => {
          const newState = { ...(prevState || {}) };
          const index = _.size(newState);
          if (index < _.size(DoneList)) {
            beginLoading();
          } else {
            clearTimeout(timer.current);
            return prevState;
          }
          return {
            ...newState,
            [index]: true,
          };
        });
      }, 2000);
    } else {
      clearTimeout(timer.current);
    }
  }, [statusMap]);
  const onSubmit = useCallback(async () => {
    form
      .validateFields()
      .then(async () => {
        const values = form.getFieldsValue();
        const { teamName } = trimValues(values);
        await teamByTeamIdPut({
          teamId: team.id!,
          teamName: _.trim(teamName),
        });
        changeStep(1);
        beginLoading();
      })
      .catch((newErrorInfo) => {
        setErrorInfo(newErrorInfo?.errorFields.map((o) => o.errors.join('')).join(''));
      });
  }, [beginLoading, form, team?.id]);
  const list = useMemo(() => {
    return (
      <Space direction={'vertical'} size={'large'}>
        {TodoList.map((text, index) => {
          if (statusMap?.[index]) {
            // 已清理
            return (
              <Space key={text}>
                <Typography.Text type={'success'}>
                  <IconFontIcon iconName={'Check-Circle_24'} />
                </Typography.Text>
                {DoneList[index]}
              </Space>
            );
          }
          // 清理中
          return (
            <Space key={text}>
              <IconFontIcon iconName={'loading_24'} spin />
              {text}
            </Space>
          );
        })}
      </Space>
    );
  }, [statusMap]);

  const steps = useMemo(() => {
    if (step === 0) {
      return (
        <Form
          onValuesChange={() => {
            setErrorInfo(null);
          }}
          form={form}
          className={styles.createTeamForm}
          initialValues={{
            teamName: team.name,
          }}
        >
          <Form.Item
            help={
              errorInfo ? (
                <span className="form-error">{errorInfo}</span>
              ) : (
                I18N.t('好的名称，有利于增强您团队的辨识度')
              )
            }
            rules={[
              {
                required: true,
                message: I18N.t('请输入团队名称！'),
              },
              {
                min: 1,
                max: 100,
                message: I18N.t('团队名称长度为1-100个字符！'),
              },
            ]}
            name="teamName"
          >
            <Input
              autoFocus
              placeholder={I18N.t('请输入您的团队名称')}
              size={'large'}
              prefix={<IconFontIcon iconName="chuangjianxintuandui_24" />}
            />
          </Form.Item>
        </Form>
      );
    }
    return list;
  }, [errorInfo, form, list, step, team?.name]);
  const footer = useMemo(() => {
    if (step === 0) {
      return (
        <Button type={'primary'} block size={'large'} onClick={onSubmit}>
          {I18N.t('下一步')}
        </Button>
      );
    }
    if (_.size(statusMap) < DoneList.length) {
      return (
        <Button type={'primary'} size={'large'} block loading>
          {I18N.t('交付中')}
        </Button>
      );
    }
    return (
      <Button type={'primary'} size={'large'} block onClick={onClose}>
        {I18N.t('关闭')}
      </Button>
    );
  }, [onClose, onSubmit, statusMap, step]);

  /* 这里的弹出框是定制的界面，不能使用DMModal */
  return (
    <Modal
      open={visible}
      footer={null}
      centered
      keyboard={false}
      maskClosable={false}
      destroyOnClose={true}
      onCancel={onClose}
      bodyStyle={{ padding: 0 }}
      width={560}
      wrapClassName={styles.createTeamWarp}
    >
      <div className={styles.createTeamBanner}>{I18N.t('TKShop套餐交付')}</div>
      <div className={styles.modalContent}>
        <div className={styles.content}>{steps}</div>
        <div className={styles.footer}>{footer}</div>
      </div>
    </Modal>
  );
};
export default InitializeModal;
