import { TeamStatusDescription } from '@/pages/Setting/components/TeamStatusCard';
import { Button, Col, Result, Row, Typography } from 'antd';
import styles from './index.less';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { useEffect, useMemo, useRef } from 'react';
import { useRenewModal } from '@/pages/Setting/components/RenewModal';
import { useSessionStorageState } from 'ahooks';
import { getCurrentTeamId } from '@/hooks/useCurrentTeam';
import EventEmitter from 'events';
import useTkTeamStatusRequest from '@/hooks/useTkShopTeam';
import { GhostModalCaller } from '@/mixins/modal';
import UpgradeModal from '@/pages/Setting/components/UpgradeModal';

const ICON_SIZE = 72;

const event = new EventEmitter();

export function useSessionDue() {
  const [state, setState] = useSessionStorageState<string | number>(
    `${getCurrentTeamId()}-expiring-not_remind`,
    '',
  );
  useEffect(() => {
    event.on('update', setState);
    return () => {
      event.off('update', setState);
    };
  }, [setState]);
  return {
    noRemindAgain: state,
    noRemindDuringSession() {
      event.emit('update', Date.now());
    },
  };
}

const TeamInvalidWrapper = () => {
  const { data, loading, refresh } = useTkTeamStatusRequest();
  const { noRemindDuringSession } = useSessionDue();
  const { run: openRenewModal } = useRenewModal();
  const timer = useRef<any>();
  const icon = useMemo(() => {
    if (data?.status === 'Expired' || data?.status === 'Overload') {
      return (
        <Typography.Text type={'warning'}>
          <IconFontIcon iconName="jinggao_24" size={ICON_SIZE} />
        </Typography.Text>
      );
    }
    return (
      <Typography.Link>
        <IconFontIcon iconName="info_241" size={ICON_SIZE} />
      </Typography.Link>
    );
  }, [data?.status]);
  const title = useMemo(() => {
    if (data?.status === 'Expired') {
      return '团队套餐已过期';
    }
    if (data?.status === 'Overload') {
      return '店铺数量超过最大配额';
    }
    return `团队套餐即将过期`;
  }, [data?.status]);
  const subTitle = useMemo(() => {
    if (data?.status === 'Expiring') {
      return `当前团队所购套餐即将在${data.remain}天后过期，为了保证能够正常使用，请您及时续费`;
    }
    if (data?.status === 'Overload') {
      return '请和管理员联系，在花漾客户端中删除多余的店铺后即可正常使用';
    }
    return 'TK套餐已过期，您可以续费继续使用，也可以暂不续费，继续使用花漾灵动客户端';
  }, [data]);
  const buttons = useMemo(() => {
    if (data?.status === 'Expired') {
      return (
        <Row gutter={[16, 16]} wrap={false} style={{ marginTop: 12 }}>
          <Col span={24}>
            <Button onClick={openRenewModal} loading={loading} type="primary" block>
              续费恢复套餐
            </Button>
          </Col>
        </Row>
      );
    }
    if (data?.status === 'Overload') {
      return (
        <Row gutter={[16, 16]} wrap={false} style={{ marginTop: 12 }}>
          <Col span={24}>
            <Button
              onClick={() => {
                GhostModalCaller(<UpgradeModal />, 'UpgradeModal');
              }}
              type="primary"
              block
            >
              升级套餐
            </Button>
          </Col>
        </Row>
      );
    }
    return (
      <Row gutter={[16, 16]} wrap={false} style={{ marginTop: 12 }}>
        <Col span={12}>
          <Button onClick={openRenewModal} loading={loading} type="primary" block>
            立即续费
          </Button>
        </Col>
        <Col span={12}>
          <Button
            block
            onClick={() => {
              noRemindDuringSession();
            }}
          >
            继续使用
          </Button>
        </Col>
      </Row>
    );
  }, [data?.status, loading, noRemindDuringSession, openRenewModal]);
  useEffect(() => {
    timer.current = setInterval(() => {
      refresh();
    }, 20 * 1000);
    return () => {
      clearInterval(timer.current);
    };
  }, [refresh]);
  return (
    <div className={styles.teamInvalidWrapper}>
      <Result
        icon={icon}
        title={title}
        extra={
          <>
            <TeamStatusDescription />
            {buttons}
          </>
        }
        subTitle={subTitle}
      />
    </div>
  );
};
export default TeamInvalidWrapper;
