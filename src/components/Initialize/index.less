@import '~/src/style/color.less';

.create-team-warp {
  :global {
    .ant-modal-content {
      background-color: transparent;
      border-radius: 5px;

      .ant-modal-close-x {
        color: #fff;
      }
    }
  }
}

.create-team-form {
  width: 100%;

  :global {
    .ant-input-affix-wrapper {
      padding-bottom: 10px;
      padding-left: 0;
      border-top: none;
      border-right: none;
      border-left: none;
      border-radius: 0;
      box-shadow: none !important;
    }

    .dm-iconFontIcon {
      font-size: 24px;

      &.input-is-empty {
        color: @text-color-subTitle;
      }
    }

    .ant-form-item {
      margin-bottom: 0;
    }

    .ant-form-item-explain {
      color: @text-color-subTitle;
      line-height: 40px;
    }

    .form-error {
      color: @text-danger;
    }
  }
}

.create-team-banner {
  height: 176px;
  padding: 68px 0 0 48px;
  color: #fff;
  font-size: 24px;
  background: url('./images/banner.png');
}
.modal-content {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  padding: 24px 48px 48px 48px !important;
  background-color: #fff;
  border-radius: 0 0 5px 5px;
}
.content {
  display: flex;
  flex: 0 0 204px;
  align-items: center;
  overflow: hidden;
}

.team-invalid-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: left;
  :global {
    .ant-result-extra {
      text-align: left;
    }
  }
}
