import I18N from '@/i18n';
import { Button, Descriptions, Layout, Typography } from 'antd';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { useRequest } from 'umi';
import { useMemo } from 'react';
import { GhostModalCaller } from '@/mixins/modal';
import { PayStatusEnum } from '@/pages/Cost/constants';
import MiddleSpin from '@/components/Common/MiddleSpin';
import PayingModal from '@/components/Common/Order/PayingModal';
import DMConfirm from '@/components/Common/DMConfirm';
import { onlineService } from '@/utils/pageUtils';
import { tkshopSystemStatusGet } from '@/services/api-TKShopAPI/TkshopSystemController';

const Initialize = (props: { onUpdate: () => void }) => {
  const { onUpdate } = props;
  const { data } = useRequest(() => tkshopSystemStatusGet(), {
    pollingInterval: 5000,
    pollingWhenHidden: false,
    onSuccess: async (res) => {
      if (res?.team?.status === 'Ready') {
        location.href = `/team/${res.team.id}/shop`;
      }
    },
  });

  const { order, team, orderItem } = useMemo(() => {
    return data || {};
  }, [data]);

  const button = useMemo(() => {
    if (!order || !team) {
      return null;
    }

    if (team.status === 'Pending' && ['Created', 'Locked'].includes(order.payStatus!)) {
      return (
        <Button
          type={'primary'}
          onClick={() => {
            GhostModalCaller(<PayingModal orderId={order.id!} onUpdate={onUpdate} />);
          }}
        >
          {I18N.t('立即支付')}
        </Button>
      );
    }
    // if (order.payStatus === 'PAID') {
    //   return (
    //     <>
    //       <Button
    //         type={'primary'}
    //         onClick={() => {
    //           GhostModalCaller(<InitializeModal onUpdate={onUpdate} />);
    //         }}
    //       >
    //         {I18N.t('立即交付')}
    //       </Button>
    //       <Space size={0}>
    //         <Typography.Text>{I18N.t('如何交付？')}</Typography.Text>
    //         <HelpLink href={'/tkshop/buy'} />
    //       </Space>
    //     </>
    //   );
    // }
    if (order.payStatus === 'WAIT_CONFIRM') {
      return (
        <Button
          type={'primary'}
          onClick={() => {
            DMConfirm({
              iconType: 'info',
              title: I18N.t('您的订单正在等待财务确认'),
              content: I18N.t(
                '这可能需要一点时间，如果您确认款项已经支付，请您联络在线客服以加速确认过程',
              ),
              okText: I18N.t('联系客服'),
              cancelText: I18N.t('关闭'),
              onOk: () => onlineService(),
            });
          }}
        >
          {I18N.t('待财务确认')}
        </Button>
      );
    }
    return null;
  }, [onUpdate, order, team]);
  const packName = useMemo(() => {
    return orderItem?.resourceName;
  }, [orderItem?.resourceName]);
  const duration = useMemo(() => {
    return (
      orderItem?.count +
      (orderItem?.goodsPeriodUnit === I18N.t('月') ? I18N.t('个月') : orderItem?.goodsPeriodUnit)
    );
  }, [orderItem?.count, orderItem?.goodsPeriodUnit]);
  if (!data) {
    return (
      <Layout.Content>
        <MiddleSpin />
      </Layout.Content>
    );
  }
  return (
    <Layout.Content>
      <div
        style={{
          width: '720px',
          height: '640px',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          margin: 'auto',
          display: 'flex',
          alignItems: 'center',
          overflow: 'hidden',
          gap: 24,
        }}
      >
        <div
          style={{
            flex: '0 0 200px',
            display: 'flex',
            alignItems: 'center',
            gap: 24,
            justifyContent: 'center',
            flexDirection: 'column',
          }}
        >
          <Typography.Link>
            <IconFontIcon iconName={'kafeibei_24'} size={128} />
          </Typography.Link>
          {button}
        </div>
        <div style={{ flex: 1, overflow: 'hidden' }}>
          <Descriptions column={1} bordered size={'small'} contentStyle={{ overflow: 'hidden' }}>
            <Descriptions.Item label={I18N.t('订单编号')}>{order.serialNumber}</Descriptions.Item>
            <Descriptions.Item label={I18N.t('订单类型')}>{packName}</Descriptions.Item>
            <Descriptions.Item label={I18N.t('购买时长')}>{duration}</Descriptions.Item>
            <Descriptions.Item label={I18N.t('应付金额')}>
              ￥{order.totalPrice?.toFixed(2)}
            </Descriptions.Item>
            <Descriptions.Item label={I18N.t('实付金额')}>
              ￥{order.payablePrice?.toFixed(2)}
            </Descriptions.Item>
            <Descriptions.Item label={I18N.t('订单状态')}>
              {PayStatusEnum[order.payStatus]}
            </Descriptions.Item>
          </Descriptions>
        </div>
      </div>
    </Layout.Content>
  );
};
export default Initialize;
