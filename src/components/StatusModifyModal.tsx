import { useState } from 'react';
import { Form, message } from 'antd';
import { useRequest } from '@@/plugin-request/request';
import { ghCreatorUpdateStatusPut } from '@/services/api-TKGHAPI/GhCreatorController';
import { ghVideoCreatorUpdateStatusPut } from '@/services/api-TKGHAPI/GhVideoCreatorController';
import { ghUserUpdateStatusPut } from '@/services/api-TKGHAPI/GhUserController';
import { ghGifterUpdateStatusPut } from '@/services/api-TKGHAPI/GhGifterController';
import I18N from '@/i18n';
import DMModal from '@/components/Common/Modal/DMModal';
import Selector from '@/components/Common/Selector';
import { CreatorStatusOptions } from '@/pages/TikTok/components/CreatorStatusSelector';
import { insUserUpdateStatusPut } from '@/services/api-InsAPI/InsUserController';

export const StatusModifyModal = (props: {
  ids: number[];
  value?: any;
  bizScene?: API.GhMessageDto['bizScene'];
  onUpdate?: () => void;
}) => {
  const { ids, onUpdate, bizScene, value = 'NotContacted' } = props;
  const [visible, setVisible] = useState(true);
  const [form] = Form.useForm();
  const { run: submit, loading } = useRequest(
    async () => {
      const values = await form.validateFields();
      const options = {
        ids: ids,
        status: values.status,
      };
      if (bizScene === 'LiveCreator') {
        await ghCreatorUpdateStatusPut(options);
      } else if (bizScene === 'VideoCreator') {
        await ghVideoCreatorUpdateStatusPut(options);
      } else if (bizScene === 'User') {
        await ghUserUpdateStatusPut(options);
      } else if (bizScene === 'Gifter') {
        await ghGifterUpdateStatusPut(options);
      } else if (bizScene === 'InsUser') {
        await insUserUpdateStatusPut(options);
      }
      message.success(I18N.t('状态已修改'));
      setVisible(false);
      onUpdate?.();
    },
    {
      manual: true,
    },
  );
  return (
    <DMModal
      headless
      onOk={submit}
      confirmLoading={loading}
      open={visible}
      onCancel={() => {
        setVisible(false);
      }}
    >
      <Form form={form} requiredMark={false} initialValues={{ status: value }}>
        <Form.Item
          name={'status'}
          rules={[
            {
              required: true,
              message: I18N.t('请指定状态'),
            },
          ]}
          label={ids.length > 1 ? I18N.t('批量更改状态') : I18N.t('更改状态')}
          initialValue={value}
        >
          <Selector options={CreatorStatusOptions} />
        </Form.Item>
      </Form>
    </DMModal>
  );
};
