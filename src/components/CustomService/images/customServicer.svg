<?xml version="1.0" encoding="UTF-8"?>
<svg width="189px" height="163px" viewBox="0 0 189 163" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>插图</title>
    <g id="06-团队" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="个人信息-05在线客服" transform="translate(-741.000000, -156.000000)">
            <g id="创建团队" transform="translate(280.000000, 130.000000)">
                <g id="编组" transform="translate(-91.000000, -94.000000)">
                    <g id="插图" transform="translate(552.000000, 120.000000)">
                        <g id="编组-4" transform="translate(0.000000, -0.000000)">
                            <g id="编组-2">
                                <g id="编组备份" transform="translate(53.101935, 46.854239) scale(-1, 1) translate(-53.101935, -46.854239) translate(30.203871, 31.708478)" fill="#253369">
                                    <path d="M35.2479489,1.31450475e-13 C41.0735479,1.3038033e-13 45.796129,4.72258112 45.796129,10.5481802 L45.796129,15.955268 C45.796129,21.780867 41.0735479,26.5034482 35.2479489,26.5034482 L10.869,26.503 L7.09654413,30.1630903 C7.03504061,30.2227445 6.95792882,30.2626796 6.87502113,30.2789188 L6.79054489,30.287112 C6.5478116,30.287112 6.35103738,30.0903378 6.35103738,29.8476045 L6.35103738,29.8476045 L6.35086024,25.6353089 C2.61372318,24.0126359 7.61449744e-15,20.2891858 7.08374558e-15,15.955268 L7.08374558e-15,10.5481802 C6.37031546e-15,4.72258112 4.72258112,1.3252062e-13 10.5481802,1.31450475e-13 L35.2479489,1.31450475e-13 Z" id="形状结合"></path>
                                </g>
                                <g id="编组" fill="#597EF7">
                                    <rect id="矩形" x="0" y="0" width="65.4090323" height="37.8138369" rx="15.0688288"></rect>
                                    <path d="M9.07096774,32.3141335 L9.07096774,42.584322 C9.07096774,42.9310839 9.35207376,43.2121899 9.69883561,43.2121899 C9.86192306,43.2121899 10.0186114,43.1487324 10.1357384,43.0352477 L17.6434853,35.7609764 L17.6434853,35.7609764 L9.07096774,32.3141335 Z" id="路径-2"></path>
                                </g>
                            </g>
                            <g id="编组-3" transform="translate(17.063226, 15.164924)" fill="#FFFFFF">
                                <ellipse id="椭圆形" cx="3.04" cy="3.05267954" rx="3.04" ry="3.05267954"></ellipse>
                                <ellipse id="椭圆形备份" cx="15.4941935" cy="3.05267954" rx="3.04" ry="3.05267954"></ellipse>
                                <ellipse id="椭圆形备份-2" cx="28.2425806" cy="3.05267954" rx="3.04" ry="3.05267954"></ellipse>
                            </g>
                        </g>
                        <g id="人物" transform="translate(53.000000, 6.000000)">
                            <g id="路径-Clipped" transform="translate(58.300000, 63.734884)" fill="#F3B57E" fill-rule="nonzero">
                                <polygon id="路径" points="0 0 19.2779623 0 19.2779623 19.0883721 0 19.0883721"></polygon>
                            </g>
                            <g id="形状-Clipped" transform="translate(22.000000, 89.000000)" fill="#61B3FF" fill-rule="nonzero">
                                <path d="M12,0 C14.209139,0 16,1.790861 16,4 L16,50 C16,52.209139 14.209139,54 12,54 L7,54 C4.790861,54 3,52.209139 3,50 L3,26.370138 C3,24.410674 2.5513313,22.479136 1.6907107,20.722902 L1.5,20.349303 C0.5150534,18.495262 0,16.427895 0,14.328469 L0,8 C0,3.581722 3.581722,0 8,0 L12,0 Z M84,0 C88.334914,0 91.864543,3.4478378 91.996193,7.7508207 L92,8 L92,14.328469 C92,16.287933 91.551331,18.219471 90.690711,19.975705 L90.5,20.349303 C89.580717,22.079742 89.070762,23.996011 89.006854,25.950734 L89,26.370138 L89,50 C89,52.142195 87.316032,53.891079 85.19964,53.995105 L85,54 L80,54 C77.8578046,54 76.1089211,52.316032 76.0048953,50.19964 L76,50 L76,4 C76,1.8578046 77.6839685,0.1089211 79.80036,0.0048953 L80,0 L84,0 Z" id="形状"></path>
                            </g>
                            <g id="形状-Clipped" transform="translate(25.000000, 115.000000)" fill="#FFC999" fill-rule="nonzero">
                                <path d="M13,0 L13,35.1428571 C13,38.3776678 11.2098275,41 9.00153787,41 L4.00346021,41 C1.79517055,41 0.00499807766,38.3776678 0.00499807766,35.1428571 L0.00499807766,0.541987786 L0,0 L13,0 Z M86,0 L85.9950019,0.541987786 L85.9950019,35.1428571 C85.9950019,38.2796427 84.3116814,40.8405085 82.196103,40.9928323 L81.9965398,41 L76.9984621,41 C74.8570903,41 73.1088792,38.5341897 73.0048934,35.4351871 L73,35.1428571 L73,0 L86,0 Z" id="形状"></path>
                            </g>
                            <g id="Path-Clipped" transform="translate(48.143580, 82.823256)" fill="#61B3FF" fill-rule="nonzero">
                                <path d="M17.6204196,0.1767034 L22.1554196,0.1767034 L38.074094,0 L37.9644196,0.1767034 C44.2795436,23.7514832 34.8700873,32.5848302 9.7360509,26.6767442 C0.493205804,24.5040922 -2.1686714,15.670745 1.7504196,0.1767034 L1.6387452,0 L17.6204196,0.1767034 Z" id="Path"></path>
                            </g>
                            <g id="路径-Clipped" transform="translate(49.770000, 76.000000)" fill="#61B3FF" fill-rule="nonzero">
                                <path d="M20.5166744,6.86727063 L27.4470488,0.275472428 C27.8164824,-0.0759689723 28.391175,-0.0931708723 28.7809673,0.235545128 L36.4353488,6.69056713 L36.3256744,6.86727063 L35.6324228,7.59437493 C30.7019063,12.6868593 26.5838118,15.2331016 23.2781395,15.2331016 C22.6102287,15.2331016 21.1737671,8.38362113 20.5166744,7.61618613 L18.9422771,7.60834633 L18.9422771,7.60834633 L18.2160039,7.58796233 L18.2160039,7.58796233 L18.0243519,7.59194583 L17.4930717,7.60834633 C17.1192531,7.61574353 16.7737363,7.60434753 15.9186744,7.61618613 C15.2615817,8.38362113 13.8251201,15.2331016 13.1572093,15.2331016 C9.851537,15.2331016 5.7334426,12.6868593 0.8029261,7.59437493 L0.1096744,6.86727063 L0,6.69056713 L7.6543816,0.235545128 C8.0116912,-0.0657778723 8.5243684,-0.0764336723 8.8918912,0.194582228 L8.9883,0.275472428 L15.9186744,6.86727063 L20.5166744,6.86727063 Z" id="路径"></path>
                            </g>
                            <g id="路径-Clipped" transform="translate(30.000000, 83.000000)" fill="#1366B5">
                                <path d="M56.0512849,0 L72.051285,4.2984476 C74.260424,4.2984476 76.051285,6.0893086 76.051285,8.2984476 L75.793507,10.5766353 C74.742643,20.032616 74.16717,27.423497 74.067087,32.749279 L74.055236,33.573031 L74.048437,34.59091 C73.949707,38.599948 71.283989,45.169128 66.0512849,54.298448 C66.0512849,54.737349 58.6351463,55.048386 49.2385968,55.195295 C49.1036056,55.221473 48.963911,55.234102 48.820311,55.235905 L46.2143605,55.263258 C44.4481969,55.278342 42.6298298,55.287159 40.792399,55.289933 L35.2595938,55.289933 C32.5032293,55.285777 29.7897177,55.268016 27.2309739,55.235905 C27.0871762,55.2341 26.9472946,55.221439 26.8112914,55.19826 L22.2376433,55.108476 C15.1180894,54.93943 10,54.66306 10,54.298448 C4.7672956,45.169128 2.1015783,38.599948 2.002848,34.59091 L1.9960494,33.573031 L1.9841975,32.749279 C1.876214,27.003041 1.2148148,18.852763 0,8.2984476 C0,6.0893086 1.790861,4.2984476 4,4.2984476 L20,0 C23.4455053,7.0041212 29.4538045,10.5295098 38.0248976,10.576166 C46.4469099,10.5304307 52.3941176,7.1268517 55.8675734,0.3654312 L56.0512849,0 Z" id="路径" fill-rule="nonzero"></path>
                                <polygon id="矩形" points="10 53 66 53 64 64 66 74 10 74 12 64"></polygon>
                            </g>
                            <g id="Oval-Copy-Clipped" transform="translate(42.000000, 14.000000)" fill="#253369" fill-rule="nonzero">
                                <circle id="Oval-Copy" cx="25" cy="25" r="25"></circle>
                            </g>
                            <g id="Rectangle-Clipped" transform="translate(38.000000, 45.000000)" fill="#253369" fill-rule="nonzero">
                                <rect id="Rectangle" x="0" y="0" width="17" height="17" rx="8.5"></rect>
                            </g>
                            <g id="形状-Clipped" transform="translate(42.697674, 40.609302)" fill="#F6BE9D" fill-rule="nonzero">
                                <path d="M10.1209303,0 L10.1209303,10.0465117 L4,10.0465117 C1.790861,10.0465117 0,8.2556507 0,6.0465117 L0,4 C0,1.790861 1.790861,0 4,0 L10.1209303,0 Z M46.6046512,0 C48.7468466,0 50.4957301,1.6839685 50.5997559,3.8003597 L50.6046512,4 L50.6046512,6.0465117 C50.6046512,8.188707 48.9206827,9.9375905 46.8042915,10.0416163 L46.6046512,10.0465117 L40.4837209,10.0465117 L40.4837209,0 L46.6046512,0 Z" id="形状"></path>
                            </g>
                            <g id="Path-Copy-2-Clipped" transform="translate(49.794419, 25.539535)" fill="#FFC999" fill-rule="nonzero">
                                <path d="M16.1813954,46.2232558 C11.1209302,46.2232558 2.0241861,37.1813953 1.012093,35.172093 C0.3373643,33.8325581 0,31.8232558 0,29.144186 L0,4.0186046 C0,1.7991906 1.8125179,0 4.0483721,0 L32.3627907,0 C34.5308917,0 36.3009245,1.6918009 36.4062083,3.8180357 L36.4111628,4.0186046 L36.4111628,29.144186 C36.4111628,31.8232558 36.0737985,33.8325581 35.3990698,35.172093 C34.420397,37.1150462 25.8172323,45.6337819 20.7406396,46.1942572 C20.722194,46.2138679 20.704452,46.2232558 20.6869871,46.2232558 L16.1813954,46.2232558 Z" id="Path-Copy-2"></path>
                            </g>
                            <g id="Oval-Copy-3-Clipped" transform="translate(76.000000, 22.000000)" fill="#253369" fill-rule="nonzero">
                                <circle id="Oval-Copy-3" cx="8.5" cy="8.5" r="8.5"></circle>
                            </g>
                            <g id="Oval-Copy-2-Clipped" transform="translate(45.000000, 18.000000)" fill="#253369" fill-rule="nonzero">
                                <path d="M22,0 C9.8497355,0 0,9.8497355 0,22 C11.8669767,22.9883721 22,16.6748735 22,0 Z" id="Oval-Copy-2"></path>
                            </g>
                            <g id="Oval-Clipped" transform="translate(62.000000, 10.000000)" fill="#253369" fill-rule="nonzero">
                                <circle id="Oval" cx="12" cy="12" r="12"></circle>
                            </g>
                            <g id="Rectangle-Clipped" transform="translate(60.780474, 58.930233)" fill="#EE978C" fill-rule="nonzero">
                                <path d="M1.00026249,0 L13.1464102,0 C13.6987042,0 14.1464269,0.447706 14.1464269,1 C14.1464269,1.1025041 14.1306671,1.204399 14.0996997,1.3021135 C13.1218803,4.3875262 10.7797627,5.9302325 7.07334699,5.9302325 C3.36692369,5.9302325 1.02480409,4.3875198 0.0469881925,1.3020944 C-0.119847208,0.7756183 0.171687092,0.2135719 0.698159692,0.0467252 C0.795870792,0.0157591 0.897761992,0 1.00026249,0 Z" id="Rectangle"></path>
                            </g>
                            <g id="路径-Clipped" transform="translate(62.532085, 62.188954)" fill="#D45043" fill-rule="nonzero">
                                <path d="M5.3217356,0 C7.6208141,0 9.3949539,0.2967923 10.6441549,0.8903769 C9.3942992,2.078146 7.62039,2.6715116 5.3217356,2.6715116 C3.0230765,2.6715116 1.2491647,2.0781436 0,0.8914075 C1.2483283,0.2968239 3.0225346,0 5.3217356,0 Z" id="路径"></path>
                            </g>
                            <g id="路径-Clipped" transform="translate(44.250000, 9.250000)" fill="#FCC643" fill-rule="nonzero">
                                <path d="M1.5659341,37.75 L1.5659341,22.75 C1.5659341,11.0139491 11.4980787,1.5 23.75,1.5 C36.0019213,1.5 45.9340659,11.0139491 45.9340659,22.75 L45.9340659,37.75 L47.5,37.75 L47.5,22.75 C47.5,10.1855219 36.8667628,0 23.75,0 C10.6332372,0 0,10.1855219 0,22.75 L0,37.75 L1.5659341,37.75 Z" id="路径"></path>
                            </g>
                            <g id="路径-Clipped" transform="translate(68.000000, 37.500000)" fill="#485666" fill-rule="nonzero">
                                <path d="M0,36.5 C11.8741221,36.5 21.5,26.8741221 21.5,15 L21.5,0 L22.5,0 L22.5,15 C22.5,27.4264069 12.4264069,37.5 0,37.5 L0,36.5 Z" id="路径"></path>
                            </g>
                            <g id="矩形-Clipped" transform="translate(65.000000, 73.000000)" fill="#0B1D32" fill-rule="nonzero">
                                <rect id="矩形" x="0" y="0" width="7" height="4" rx="1.5"></rect>
                            </g>
                            <g id="矩形-Clipped" transform="translate(88.000000, 35.000000)" fill="#FCC643" fill-rule="nonzero">
                                <path d="M2,0 L5.5,0 C8.5375661,0 11,2.4624339 11,5.5 L11,12.5 C11,15.5375661 8.5375661,18 5.5,18 L2,18 C0.8954305,18 0,17.1045695 0,16 L0,2 C0,0.8954305 0.8954305,0 2,0 Z" id="矩形"></path>
                            </g>
                            <g id="矩形备份-Clipped" transform="translate(37.000000, 35.000000)" fill="#FEC737" fill-rule="nonzero">
                                <path d="M2,0 L5.5,0 C8.5375661,0 11,2.4624339 11,5.5 L11,12.5 C11,15.5375661 8.5375661,18 5.5,18 L2,18 C0.8954305,18 0,17.1045695 0,16 L0,2 C0,0.8954305 0.8954305,0 2,0 Z" id="矩形备份" transform="translate(5.500000, 9.000000) scale(-1, 1) translate(-5.500000, -9.000000) "></path>
                            </g>
                            <g id="矩形-Clipped" transform="translate(44.000000, 38.000000)" fill="#FFFFFF" fill-opacity="0.5" fill-rule="nonzero">
                                <rect id="矩形" x="0" y="0" width="1" height="12"></rect>
                            </g>
                            <g id="矩形备份-2-Clipped" transform="translate(91.000000, 38.000000)" fill="#FFFFFF" fill-opacity="0.5" fill-rule="nonzero">
                                <rect id="矩形备份-2" x="0" y="0" width="1" height="12"></rect>
                            </g>
                            <g id="矩形-Clipped" fill="#DAE0E9" fill-rule="nonzero">
                                <rect id="矩形" x="80" y="108" width="19" height="5"></rect>
                            </g>
                            <path d="M68.4576583,85.6354002 L68.6505469,86.1674002 L75.9720054,87.121893 C77.0112906,87.3864383 77.7293422,88.3501392 77.6411604,89.4209179 C77.6159656,89.6854632 77.5466799,89.9500086 77.4270047,90.2019565 L76.0601871,93.0174746 C75.6129796,93.9370846 74.4918113,94.3213052 73.5722013,93.8740976 L70.4195469,91.0614002 L75.1594733,104.166171 C75.1783694,104.210261 75.1909668,104.260651 75.1909668,104.304742 C75.2161616,104.625976 74.9705123,104.940911 74.5988891,105.035391 L70.38506,106.087274 C70.0008394,106.181754 69.6103201,105.999092 69.5032422,105.677858 L69.4843461,105.595975 L69.4843461,105.595975 L67.6425469,93.6134002 L64.4642836,117.208115 C64.4579848,117.264804 64.4390888,117.315193 64.4201927,117.371881 C64.2690239,117.712011 63.8659072,117.900972 63.4627906,117.787596 L58.9151304,116.553051 C58.5183124,116.445973 58.2600658,116.080648 58.3041566,115.709025 C58.3104553,115.652337 58.3293514,115.601947 58.3482475,115.545259 L65.3815469,90.9604002 L62.5179858,93.8803963 C61.6766123,94.3637385 60.6034607,94.1065213 60.0643806,93.3229684 L59.9796104,93.1875395 L58.4301306,90.4728005 C58.2915592,90.23345 58.2033775,89.9752034 58.1655853,89.710658 C58.0104384,88.7081705 58.5901978,87.7452548 59.5192608,87.3681814 L59.6772728,87.310854 L66.9963603,85.8621534 L67.0555469,85.9854002 L68.4576583,85.6354002 Z" id="路径" fill="#267ED0" fill-rule="nonzero"></path>
                            <path d="M65.6106467,87.3927371 C65.6106467,88.202848 66.0428357,88.9514214 66.7444123,89.3564769 C67.445989,89.7615324 68.3103671,89.7615324 69.0119438,89.3564769 C69.7135204,88.9514214 70.1457094,88.202848 70.1457094,87.3927371 C70.1457094,86.5826261 69.7135204,85.8340527 69.0119438,85.4289972 C68.3103671,85.0239417 67.445989,85.0239417 66.7444123,85.4289972 C66.0428357,85.8340527 65.6106467,86.5826261 65.6106467,87.3927371 L65.6106467,87.3927371 Z" id="路径" fill="#00427E" fill-rule="nonzero"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>