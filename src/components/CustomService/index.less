@import '~/src/style/color.less';

.custom-service {
  :global {
    .ant-modal-content {
      background-color: transparent;
      border-radius: 5px;

      .ant-modal-close-x {
        color: #fff;
      }
    }

    .custom-service-head {
      position: relative;
      height: 176px;
      overflow: hidden;
      color: #fff;
      background: url('./images/customServiceHead.png');
      border-radius: 5px;

      .custom-service-head-title {
        padding: 40px 0 0 32px;
        font-size: 24px;
      }

      .custom-service-head-description {
        width: 363px;
        padding: 16px 0 0 32px;
        font-size: 14px;
        line-height: 24px;
      }

      .custom-service-head-banner {
        position: absolute;
        top: 26px;
        right: 70px;
        width: 189px;
        height: 163px;
        background: url('./images/customServicer.svg');
      }
    }

    .custom-service-body {
      display: flex;
      padding: 24px 0 0 0;
      color: #333;
      line-height: 22px;
      background-color: #fff;

      .custom-service-body-left {
        display: flex;
        flex: 0 0 252px;
        flex-direction: column;
        align-items: center;
        text-align: center;
        border-right: 1px dashed @border-color;

        .custom-service-wechat-title {
          margin-bottom: 8px;

          .dm-iconFontIcon {
            color: #00c800;
          }
        }

        .custom-service-wechat-description {
          color: #888;
          line-height: 22px;
        }

        .custom-service-wechat-qrcode {
          width: 130px;
          height: 130px;
          margin: 0 auto;
          margin-bottom: 24px;
          background: url('/qrcode.png');
          background-size: contain;
        }

        .custom-service-wechat-qrcode-description {
          line-height: 22px;
        }
      }

      .custom-service-body-right {
        flex: 1 0 0;
        padding-left: 24px;

        .custom-service-item {
          width: 410px;
          margin-bottom: 16px;
          border-bottom: 1px dashed @border-color;

          .custom-service-item-title {
            position: relative;
            margin-bottom: 16px;
            text-indent: 14px;

            &::after {
              position: absolute;
              top: 3px;
              left: 0;
              width: 4px;
              height: 16px;
              background-color: @primary-color;
              content: ' ';
            }
          }

          .custom-service-item-sub {
            display: flex;
            margin-bottom: 16px;

            .custom-service-item-sub-label {
              flex: 0 0 84px;
            }

            .custom-service-item-sub-colon {
              flex: 0 0 14px;
            }

            .custom-service-item-sub-description {
              flex: 0 0 139px;
              color: #888;
            }

            .custom-service-item-sub-value {
              flex: 1 0 0;
              color: @primary-color;
            }

            .custom-service-item-sub-link {
              cursor: pointer;
            }
          }
        }
      }
    }

    .app-version {
      padding: 0 34px 12px 0;
      font-size: 12px;
      text-align: right;
      background-color: #fff;
    }
  }
}
.service-common-qa {
  display: flex;
  flex-direction: column;
  padding: 0 16px;
  border-radius: 5px 5px 0 0;
  pointer-events: initial;
  :global {
    .title {
      margin-top: 24px;
      margin-bottom: 16px;
      font-size: 16px;
      text-align: center;
    }
    .list {
      flex: 1;
      overflow: auto;
      a:hover {
        cursor: pointer;
        div {
          text-decoration: underline;
        }
      }
    }
    .footer {
      line-height: 56px;
      text-align: center;
    }
  }
}
