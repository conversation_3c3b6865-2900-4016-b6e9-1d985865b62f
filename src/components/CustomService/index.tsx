import I18N from '@/i18n';
import { Button, Dropdown, Modal, Space, Typography } from 'antd';
import styles from './index.less';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { onlineService, openOfficialSiteByAppWindow } from '@/utils/pageUtils';
import constants from '@/constants';
import ColoursIcon from '@/components/Common/ColoursIcon';
import { useCallback, useEffect, useState } from 'react';
import EventEmitter from 'events';
import _ from 'lodash';
import { useLocalStorageState } from 'ahooks';
import { useRouteMatch } from 'umi';

export const event = new EventEmitter();
const onClick = _.throttle(() => {
  event.emit('open');
}, 500);
export function openCustomService() {
  onClick();
}
export function triggerEntry() {
  event.emit('entry');
}
/**
 * 在线客服
 * @constructor
 */
const CustomService = () => {
  /* 这里的弹出框是定制的界面，不能使用DMModal */
  const [open, changeOpen] = useState(false);
  const { params } = useRouteMatch(['/team/:teamId/:module/:refer/:type?']) || {};

  const [isMobile, setIsMobile] = useLocalStorageState('isMobile', () => {
    return navigator.userAgent.includes('Mobile');
  });
  const openCb = useCallback(() => {
    changeOpen(true);
  }, []);
  useEffect(() => {
    event.on('open', openCb);
    return () => {
      event.off('open', openCb);
    };
  }, [openCb]);
  return (
    <>
      <Dropdown
        trigger={['click']}
        menu={{
          items: [
            {
              label: I18N.t('在线客服'),
              key: 'service',
              onClick: () => {
                changeOpen(true);
              },
            },
            {
              label: I18N.t('帮助文档'),
              key: 'help',
              onClick: () => {
                openOfficialSiteByAppWindow('/help/tkshop2/overview');
              },
            },
            // {
            //   label: I18N.t('切换至手机版'),
            //   key: 'mobile',
            //   onClick: () => {
            //     setIsMobile(true);
            //     if (params?.module === 'tiktok') {
            //       if (params?.refer === 'online') {
            //         location.href = `/team/${getTeamIdFromUrl()}/kol/todo/tiktok/online`;
            //       } else if (
            //         params?.type &&
            //         ['gifter', 'video', 'live', 'user'].includes(params?.type)
            //       ) {
            //         location.href = `/team/${getTeamIdFromUrl()}/kol/todo/tiktok/${params.type}`;
            //       } else {
            //         location.href = `/team/${getTeamIdFromUrl()}/kol/todo`;
            //       }
            //     } else if (params?.module === 'ins') {
            //       location.href = `/team/${getTeamIdFromUrl()}/kol/todo/ins`;
            //     }
            //   },
            // },
          ],
        }}
      >
        <Button
          type={'text'}
          size={'small'}
          style={{ color: 'inherit' }}
          onClick={() => {
            changeOpen(false);
          }}
        >
          <Space>
            <IconFontIcon iconName="kefu_24" />
            <span>{I18N.t('支持帮助')}</span>
          </Space>
        </Button>
      </Dropdown>
      <Modal
        footer={false}
        title={false}
        open={open}
        onCancel={() => {
          changeOpen(false);
        }}
        wrapClassName={styles.customService}
        bodyStyle={{ padding: 0 }}
        centered
        modalRender={(node) => {
          return (
            <div style={{ display: 'flex', gap: 16, overflow: 'hidden' }}>
              <div style={{ width: 720 }}>{node}</div>
            </div>
          );
        }}
        width={720}
      >
        <div className="custom-service-head">
          <div className="custom-service-head-title">{I18N.t('在线客服')}</div>
          <div className="custom-service-head-description">
            {I18N.t('有任何关于技术支持、商务、合作等各类需求请与我们的在线客服进行联系')}
          </div>
          <div className="custom-service-head-banner" />
        </div>
        <div className="custom-service-body">
          <div className="custom-service-body-left">
            <Space className="custom-service-wechat-title">
              <IconFontIcon iconName="weixin_24" />
              {I18N.t('微信客服')}
            </Space>
            <div className="custom-service-wechat-description">
              {I18N.t('在线时间，08:00-24:00')}

              <br />
              {I18N.t('其余时间可留言')}
            </div>
            <div className="custom-service-wechat-qrcode" />
            <Space className="custom-service-wechat-title">
              <ColoursIcon className={'zhichi_24'} />
              <Typography.Link
                onClick={() => {
                  openOfficialSiteByAppWindow('/help/tkshop2/overview');
                }}
              >
                {I18N.t('帮助文档')}
              </Typography.Link>
            </Space>
            <div className="custom-service-wechat-description">
              {I18N.t('或请参考我们的帮助文档，')}

              <br />
              {I18N.t('进行自助式查询')}
            </div>
          </div>
          <div className="custom-service-body-right">
            <div className="custom-service-item">
              <div className="custom-service-item-title">{I18N.t('在线客服')}</div>
              <div className="custom-service-item-sub">
                <div className="custom-service-item-sub-label">{I18N.t('产品技术支持')}</div>
                <div className="custom-service-item-sub-colon">：</div>
                <div className="custom-service-item-sub-description">
                  {I18N.t('（08:00-20:00在线）')}
                </div>
                <Space
                  className="custom-service-item-sub-value custom-service-item-sub-link"
                  onClick={() => onlineService()}
                >
                  <IconFontIcon iconName="kefu_24" />
                  {I18N.t('技术支持客服')}
                </Space>
              </div>
              <div className="custom-service-item-sub">
                <div className="custom-service-item-sub-label">{I18N.t('商务合作')}</div>
                <div className="custom-service-item-sub-colon">：</div>
                <div className="custom-service-item-sub-description">
                  {I18N.t('（08:00-20:00在线）')}
                </div>
                <Space
                  className="custom-service-item-sub-value custom-service-item-sub-link"
                  onClick={() => onlineService()}
                >
                  <IconFontIcon iconName="kefu_24" />
                  {I18N.t('商务合作洽谈')}
                </Space>
              </div>
            </div>
            <div className="custom-service-item">
              <div className="custom-service-item-title">{I18N.t('热线电话')}</div>
              <div className="custom-service-item-sub">
                <div className="custom-service-item-sub-label">{I18N.t('热线电话')}</div>
                <div className="custom-service-item-sub-colon">：</div>
                <div className="custom-service-item-sub-description">
                  {I18N.t('（08:00-24:00接听）')}
                </div>
                <Space className="custom-service-item-sub-value">
                  <IconFontIcon iconName="dianhua_24" />
                  {constants.hotline}
                </Space>
              </div>
            </div>
            <div className="custom-service-item">
              <div className="custom-service-item-title">{I18N.t('邮件咨询')}</div>
              <div className="custom-service-item-sub">
                <div className="custom-service-item-sub-label">{I18N.t('技术支持邮件')}</div>
                <div className="custom-service-item-sub-colon">：</div>
                <div className="custom-service-item-sub-description">
                  {I18N.t('（2小时内响应）')}
                </div>
                <Space className="custom-service-item-sub-value">
                  <IconFontIcon iconName="xiaoxizhongxin_24" />
                  <EMAIL>
                </Space>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default CustomService;
