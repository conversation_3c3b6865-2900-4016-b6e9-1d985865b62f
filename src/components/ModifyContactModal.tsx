import { useEffect, useMemo, useRef, useState } from 'react';
import DMModal from '@/components/Common/Modal/DMModal';
import I18N from '@/i18n';
import { Form, Input, message, Radio, Select } from 'antd';
import DMFormItem, { DMFormItemContext } from '@/components/Common/DMFormItem';
import { useRequest } from '@@/plugin-request/request';
import { ContactType, ContactTypeLabel, WithFriendshipPerson } from '@/pages/TikTok/utils/utils';
import { getFriendByContactType } from '@/pages/TikTok/utils/utils';
import { getValidFriendByContactType } from '@/pages/TikTok/utils/utils';
import { ContactType2PlatformType } from '@/pages/TikTok/utils/utils';
import _ from 'lodash';
import { trimValues } from '@/utils/utils';
import styled from 'styled-components';
import { tkshopBuyerByIdOneContactPut } from '@/services/api-TKShopAPI/TkshopBuyerController';
import { tkshopCreatorByIdOneContactPut } from '@/services/api-TKShopAPI/TkshopCreatorController';
import { getSupportMobileAccountType } from '@/components/Common/Selector/MobileAccountSelector';
import { getMobileSelectorOptions } from '@/components/Common/Selector/MobileSelector';
import validate from '@/utils/validate';
import { GhostModalWrapperComponentProps } from '@/mixins/modal';

const StyledRadioGroup = styled(Radio.Group)`
  display: flex;
  flex-direction: column;
  align-items: stretch;
  .ant-radio-wrapper {
    display: flex;
    gap: 8px;
    align-items: baseline;
    margin-right: 0 !important;
    line-height: 32px;
    &:after {
      display: none;
    }
    .ant-radio {
      flex: 0 0 16px;
      & + span {
        display: flex;
        flex: 1;
        flex-wrap: nowrap;
        gap: 8px;
        align-items: baseline;
        padding: 0 !important;
        overflow: hidden;
      }
    }
  }
`;

const ModifyContactModal = (
  props: GhostModalWrapperComponentProps & {
    contactType: ContactType;
    person: WithFriendshipPerson;
    onUpdate: () => void;
    refer: 'creator' | 'buyer';
  },
) => {
  const { contactType, person, onUpdate, refer, modalProps } = props;
  const [form] = Form.useForm();
  const [open, setOpen] = useState(true);
  const inputRef = useRef();
  const initialAccountId = useMemo(() => {
    return getValidFriendByContactType(person, contactType)?.accountId;
  }, [contactType, person]);
  const initialFriendStatus = useMemo(() => {
    const friend = getFriendByContactType(person, contactType);
    if (friend) {
      return friend.status === 'NotFound' ? 'Unknown' : friend.status;
    }
    return 'Unknown';
  }, [contactType, person]);
  const initialContactStatus = useMemo(() => {
    return person?.contactMap?.[contactType] ? 'Ready' : 'NotReady';
  }, [contactType, person]);
  useEffect(() => {
    if (inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 300);
    }
  }, []);
  const { run: submit, loading: submitting } = useRequest(
    async () => {
      const { contact_status, status, accountId, input } = trimValues(await form.validateFields());
      const body: API.UpdateOneContactRequest = {
        contact: contact_status === 'Ready' ? input : undefined,
        contactType,
        mobileAccountId: accountId,
        status,
      };
      if (contactType === 'email') {
        body.contact = input;
        body.status = 'Unknown';
      } else if (contactType === 'phone') {
        body.contact = input;
        if (accountId) {
          body.mobileId = accountId;
          delete body.mobileAccountId;
        }
        if (!input) {
          body.status = 'Unknown';
        }
      }
      if (refer === 'buyer') {
        await tkshopBuyerByIdOneContactPut(
          {
            id: person.id!,
          },
          body,
        );
      } else {
        await tkshopCreatorByIdOneContactPut(
          {
            id: person.id!,
          },
          body,
        );
      }
      message.success(I18N.t('修改成功'));
      setOpen(false);
      onUpdate();
    },
    {
      manual: true,
    },
  );
  const { data: options, loading } = useRequest(
    async () => {
      if (contactType !== 'phone') {
        return {
          data: await getSupportMobileAccountType([ContactType2PlatformType[contactType!]]),
        };
      }
      return { data: await getMobileSelectorOptions() };
    },
    {
      defaultLoading: true,
      onSuccess(res) {
        if (!initialAccountId && initialContactStatus === 'Ready') {
          form.setFieldValue('accountId', res?.[0]?.id);
        }
      },
    },
  );
  const label = ContactTypeLabel[contactType];
  const title = useMemo(() => {
    if (contactType === 'email') {
      return I18N.t('修改电子邮箱');
    }
    if (contactType === 'phone') {
      return I18N.t('修改电话');
    }
    return I18N.t('修改{{type}}', {
      type: label,
    });
  }, [contactType, label]);
  return (
    <DMModal
      onOk={submit}
      width={460}
      confirmLoading={submitting}
      open={open}
      {...modalProps}
      bodyStyle={{ paddingBottom: 0 }}
      onCancel={() => {
        setOpen(false);
      }}
      title={title}
    >
      <DMFormItemContext.Provider value={{ labelWidth: 90 }}>
        <Form requiredMark={false} form={form}>
          <Form.Item noStyle shouldUpdate>
            {() => {
              let prefix = (
                <DMFormItem
                  label={label}
                  name={'contact_status'}
                  initialValue={initialContactStatus}
                  style={{ marginBottom: 0 }}
                >
                  <StyledRadioGroup
                    onChange={(e) => {
                      if (e.target.value === 'Ready') {
                        inputRef?.current?.focus?.();
                      }
                    }}
                  >
                    <Radio value={'NotReady'}>{I18N.t('未注册')}</Radio>
                    <Radio value={'Ready'}>
                      <Form.Item
                        style={{ marginBottom: 24, flex: 1, overflow: 'hidden' }}
                        shouldUpdate
                      >
                        {() => {
                          const disabled = form.getFieldValue('contact_status') !== 'Ready';
                          return (
                            <Form.Item
                              noStyle
                              name={'input'}
                              initialValue={person?.contactMap?.[contactType] || ''}
                              rules={[
                                {
                                  validator(_rule, val) {
                                    if (disabled) {
                                      return Promise.resolve();
                                    }
                                    if (!_.trim(val)) {
                                      return Promise.reject(
                                        I18N.t('请填写{{type}}账号', {
                                          type: label,
                                        }),
                                      );
                                    }
                                    return Promise.resolve();
                                  },
                                },
                              ]}
                            >
                              <Input allowClear ref={inputRef} disabled={disabled} />
                            </Form.Item>
                          );
                        }}
                      </Form.Item>
                    </Radio>
                  </StyledRadioGroup>
                </DMFormItem>
              );
              if (contactType === 'email') {
                prefix = (
                  <DMFormItem
                    name={'input'}
                    initialValue={person?.contactMap?.[contactType]}
                    rules={[
                      {
                        validator(rule, val) {
                          if (_.trim(val)) {
                            return validate.email(val);
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                    label={I18N.t('电子邮箱')}
                  >
                    <Input allowClear ref={inputRef} autoFocus />
                  </DMFormItem>
                );
              } else if (contactType === 'phone') {
                prefix = (
                  <DMFormItem
                    initialValue={person?.contactMap?.[contactType]}
                    name={'input'}
                    rules={[
                      {
                        required: true,
                        whitespace: false,
                      },
                    ]}
                    label={I18N.t('电话')}
                  >
                    <Input allowClear ref={inputRef} autoFocus />
                  </DMFormItem>
                );
              }
              return (
                <div>
                  {prefix}
                  <Form.Item shouldUpdate noStyle>
                    {() => {
                      if (contactType === 'email') {
                        return false;
                      }
                      const contact_disabled =
                        contactType === 'phone'
                          ? !_.trim(form.getFieldValue('input'))
                          : form.getFieldValue('contact_status') !== 'Ready';
                      return (
                        <DMFormItem
                          label={I18N.t('好友状态')}
                          name={'status'}
                          initialValue={initialFriendStatus}
                        >
                          <StyledRadioGroup disabled={contact_disabled}>
                            <Radio value={'Unknown'}>{I18N.t('未知')}</Radio>
                            <Radio value={'Error'}>{I18N.t('添加失败')}</Radio>
                            <div style={{ display: 'flex', gap: 8, flexWrap: 'nowrap' }}>
                              <Radio value={'Ready'}>
                                <span style={{ whiteSpace: 'nowrap', overflow: 'visible' }}>
                                  {I18N.t('指定好友')}
                                </span>
                              </Radio>
                              <Form.Item shouldUpdate noStyle>
                                {() => {
                                  const placeholder =
                                    contactType === 'phone'
                                      ? I18N.t('请指定好友手机')
                                      : I18N.t('请指定好友账号');
                                  const disabled =
                                    form.getFieldValue('status') !== 'Ready' || contact_disabled;
                                  return (
                                    <Form.Item
                                      noStyle
                                      initialValue={initialAccountId}
                                      name={'accountId'}
                                      rules={[
                                        {
                                          validator(_rule, val) {
                                            if (disabled) {
                                              return Promise.resolve();
                                            }
                                            if (!val) {
                                              return Promise.reject(new Error(placeholder));
                                            }
                                            return Promise.resolve();
                                          },
                                        },
                                      ]}
                                    >
                                      <Select
                                        placeholder={placeholder}
                                        style={{ flex: 1, overflow: 'hidden' }}
                                        disabled={disabled}
                                        loading={loading}
                                        options={options}
                                      />
                                    </Form.Item>
                                  );
                                }}
                              </Form.Item>
                            </div>
                          </StyledRadioGroup>
                        </DMFormItem>
                      );
                    }}
                  </Form.Item>
                </div>
              );
            }}
          </Form.Item>
        </Form>
      </DMFormItemContext.Provider>
    </DMModal>
  );
};
export default ModifyContactModal;
