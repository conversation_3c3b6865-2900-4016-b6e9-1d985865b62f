@import '../../style/color.less';
.set-page-size {
  //width: 80px;
}
.page-list {
  display: flex;
  align-items: center;
  height: 32px;
  border: 1px solid @border-color;
  border-radius: 3px 3px;
  :global {
    .dm-iconFontIcon {
      display: inline-block;
      width: 32px;
      height: 32px;
      color: @primary-color;
      font-size: 12px;
      line-height: 32px;
      text-align: center;
      border-right: 1px solid @border-color;
      cursor: pointer;
      &:hover {
        background: #eee;
      }
      &.disabled {
        color: @text-color-secondary;
        pointer-events: none;
      }

      &:last-child {
        border-right: none;
      }
    }
  }
}
.current-page {
  display: inline-block;
  height: 32px;
  padding: 0 10px;
  color: @text-color;
  line-height: 32px;
  text-align: center;
  border-right: 1px solid @border-color;
}
.goto-page {
  //width: 80px;
}
