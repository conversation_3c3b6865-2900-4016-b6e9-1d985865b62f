import React, { useMemo } from 'react';
import { Checkbox, Col, Dropdown, Menu, Radio, Row } from 'antd';

type Option = {
  value: any;
  label: any;
  disabled?: boolean;
};

interface Props {
  value: any | Option[];
  options: Option[];
  disabled?: boolean;
  onSelect: (keys: any[]) => void;
}

/**
 * 下拉选择器
 * @param props
 * @constructor
 */
const DropdownSelector: React.FC<Props> = (props) => {
  const { children, options, value, disabled, onSelect } = props;
  const selectedKeys = useMemo(() => {
    if (value instanceof Array) {
      return value.map((item) => item.value);
    }
    return [value];
  }, [value]);
  const overlay = useMemo(() => {
    return (
      <Menu
        selectedKeys={selectedKeys}
        onSelect={({ selectedKeys }) => {
          onSelect(selectedKeys);
        }}
      >
        {options.map((item) => {
          let form = (
            <Radio disabled={item.disabled} value={item.value} checked={value === item.value}>
              {item.label}
            </Radio>
          );
          if (value instanceof Array) {
            const checked = selectedKeys.includes(item.value);
            form = <Checkbox disabled={item.disabled} value={item.value} checked={checked} />;
          }
          return (
            <Menu.Item disabled={item.disabled} key={item.value}>
              <Row>
                <Col flex={1}>{item.label}</Col>
                <Col>{form}</Col>
              </Row>
            </Menu.Item>
          );
        })}
      </Menu>
    );
  }, [options, value]);
  return (
    <Dropdown disabled={disabled} overlay={overlay}>
      {children}
    </Dropdown>
  );
};
export default DropdownSelector;
