import ColoursIcon from '@/components/Common/ColoursIcon';
import LocaleMap from '@/constants/LocaleConstants';
import Placeholder from '@/components/Common/Placeholder';
import { Col, Row, Typography } from 'antd';

const Locale = (props: { locale?: string }) => {
  const { locale } = props;
  return (
    <Row align={'middle'} wrap={false} gutter={4}>
      <Col>
        <ColoursIcon className="yuyan_24" />
      </Col>
      <Col style={{ flex: 1, overflow: 'hidden' }}>
        <Typography.Text ellipsis>{LocaleMap[locale || ''] || <Placeholder />}</Typography.Text>
      </Col>
    </Row>
  );
};
export default Locale;
