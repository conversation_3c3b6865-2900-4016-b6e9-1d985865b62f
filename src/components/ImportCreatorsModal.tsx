import I18N from '@/i18n';
import { useEffect, useMemo, useRef, useState } from 'react';
import DMModal from '@/components/Common/Modal/DMModal';
import {
  Button,
  Checkbox,
  Col,
  Form,
  Input,
  message,
  Radio,
  Row,
  Space,
  Typography,
  Upload,
} from 'antd';
import { useRequest } from 'umi';
import DMFormItem from '@/components/Common/DMFormItem';
import { ghImportExcelByScenePost, ghImportPost } from '@/services/api-TKGHAPI/GhUserController';
import { showStatusMsg } from '@/mixins/modal';
import HelpLink from '@/components/HelpLink';
import HelpTooltip from '@/components/HelpTooltip';
import moment from 'moment';
import DMConfirm from '@/components/Common/DMConfirm';
import { useLocalStorageState } from 'ahooks';
import _ from 'lodash';
import type { RcFile } from 'antd/es/upload';
import { downloadByIframe } from '@/utils/utils';
import TkRegionSelector from '@/components/Common/Selector/TkRegionSelector';
import InfoTip from '@/components/Tips/InfoTip';

const ImportCreatorsModal = (props: {
  bizScene?: API.GhMessageDto['bizScene'];
  onUpdate?: (sort?: boolean) => void;
}) => {
  const { bizScene = 'ShopCreator', onUpdate } = props;
  const [visible, changeVisible] = useState(true);
  const [form] = Form.useForm();

  const label = useMemo(() => {
    if (bizScene === 'User') {
      return I18N.t('用户');
    }
    return I18N.t('达人');
  }, [bizScene]);
  const [file, changeFile] = useState<RcFile>();
  const ref = useRef();
  const [importType, changeImportType] = useState<'text' | 'file'>('text');
  const [addTags, setAddTags] = useLocalStorageState(`${bizScene}_importUser_addTags`, true);
  const { run, loading } = useRequest(
    async () => {
      const { region, creatorList, tags } = await form.validateFields();
      let result = [];
      if (importType === 'text') {
        const list = creatorList.split(/[\n,]/).filter((item: string) => !!item.trim?.());
        result = await ghImportPost(
          {
            scene: bizScene,
          },
          {
            handles: list,
            region,
            tags: addTags ? tags.split(/[,，]/).filter((item: string) => !!item.trim?.()) : [],
          },
        ).then((res) => {
          return res.data!;
        });
      } else {
        result = await ghImportExcelByScenePost(
          {
            scene: bizScene,
            tags: addTags ? tags.split(/[,，]/).filter((item: string) => !!item.trim?.()) : [],
          },
          {
            file,
          },
        ).then((res) => {
          return res.data!;
        });
      }

      let doAdvancedSearch = (localStorage.getItem('importUser.advancedSearch') ?? '1') === '1';
      DMConfirm({
        title: `${I18N.t('已成功导入')}${result.length}${I18N.t('个')}${label}`,
        content: (
          <div>
            <p>
              {I18N.t('您可以按“导入时间”进行排序，查看最近导入的')}
              {label}
              {I18N.t(
                '；如果您打了标签，可以按照标签进行筛选；如果执行了信息更新流程，则稍候会查看到',
              )}
              {label}
              {I18N.t('的详情信息')}
            </p>
            <Checkbox
              defaultChecked={doAdvancedSearch}
              onChange={(e) => {
                doAdvancedSearch = e.target.checked;
                localStorage.setItem('importUser.advancedSearch', e.target.checked ? '1' : '0');
              }}
            >
              {I18N.t('按照导入时间筛选')}

              {label}
            </Checkbox>
          </div>
        ),

        onOk: () => {
          onUpdate?.(doAdvancedSearch);
        },
      });
      message.success(I18N.t('导入成功'));
      changeVisible(false);
    },
    {
      manual: true,
    },
  );

  useEffect(() => {
    showStatusMsg('import-creator-modal', {
      type: 'link',
      content: (
        <HelpLink href="/tkshop/faq#id">
          {I18N.t('进一步了解什么是')}
          {label}ID
        </HelpLink>
      ),
    });
  }, [label]);
  useEffect(() => {
    setTimeout(() => {
      ref.current?.focus?.();
    }, 500);
  }, []);
  const footer = useMemo(() => {
    return (
      <div
        style={{ flex: 1, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
      >
        <InfoTip
          message={<HelpLink href={'/tkshop2/faq#creator'}>{I18N.t('了解什么是达人ID')}</HelpLink>}
        />
        <Space>
          <Button onClick={run} loading={loading} type={'primary'}>
            {I18N.t('确定')}
          </Button>
          <Button
            onClick={() => {
              changeVisible(false);
            }}
            type={'default'}
          >
            {I18N.t('取消')}
          </Button>
        </Space>
      </div>
    );
  }, [loading, run]);

  return (
    <DMModal
      footer={footer}
      open={visible}
      width={600}
      title={I18N.t('导入达人')}
      onCancel={() => {
        changeVisible(false);
      }}
    >
      <Form form={form} requiredMark={false}>
        <Radio
          checked={importType === 'text'}
          onClick={() => {
            changeImportType('text');
          }}
        >
          {I18N.t('文本框直接导入')}
        </Radio>
        <div style={{ paddingLeft: 24, paddingTop: 4 }}>
          <DMFormItem
            name="creatorList"
            initialValue={''}
            rules={[
              {
                validator: (rule, value: string) => {
                  if (importType !== 'text') {
                    return Promise.resolve();
                  }
                  try {
                    const validList = value
                      .split(/[\n,]/)
                      .filter((item: string) => !!item.trim?.());
                    if (validList.length) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error(`${I18N.t('请输入')}${label}ID`));
                  } catch (e) {}
                  return Promise.reject(new Error(`${I18N.t('请输入')}${label}ID`));
                },
              },
            ]}
          >
            <Input.TextArea
              ref={ref}
              disabled={importType !== 'text'}
              autoFocus
              style={{ resize: 'none', overflow: 'auto', height: 400 }}
              placeholder={`${I18N.t('请输入')}${label}${I18N.t(
                'ID，支持英文逗号分隔或者每行一个',
              )}`}
            />
          </DMFormItem>
          <DMFormItem
            label={I18N.t('达人所属国家')}
            name={'region'}
            rules={[
              {
                validator(_rule, value) {
                  if (importType !== 'text') {
                    return Promise.resolve();
                  }
                  if (!value) {
                    return Promise.reject(new Error(`${I18N.t('请指定达人所属国家')}`));
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <TkRegionSelector
              placeholder={I18N.t('请指定达人所属国家')}
              disabled={importType !== 'text'}
              valuePropName={'region'}
            />
          </DMFormItem>
        </div>
        <div>
          <Radio
            checked={importType === 'file'}
            onClick={() => {
              changeImportType('file');
            }}
          >
            {I18N.t('花漾Excel模板文件')}
          </Radio>
          <div
            style={{
              paddingLeft: 24,
              display: 'flex',
              gap: 4,
              flexDirection: 'column',
              paddingTop: 4,
            }}
          >
            <Form.Item
              name={'_file'}
              validateTrigger={'onBlur'}
              rules={[
                {
                  validator() {
                    if (!file && importType === 'file') {
                      return Promise.reject(new Error(`${I18N.t('请选择文件')}`));
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  color: '#999',
                  paddingBottom: 4,
                }}
              >
                {I18N.t('您可以通过Excel文件批量导入')}

                {label}
                {I18N.t('及其联系方式')}
                <a
                  onClick={(e) => {
                    e.preventDefault();
                    downloadByIframe(
                      'https://dl.szdamai.com/downloads/files/TKShop_%E8%BE%BE%E4%BA%BA%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx',
                    );
                  }}
                  style={{ whiteSpace: 'nowrap' }}
                >
                  {I18N.t('下载模板文件')}
                </a>
              </div>
              <Row gutter={[16, 16]}>
                <Col flex={1}>
                  <Input disabled={importType !== 'file'} value={file?.name} readOnly />
                </Col>
                <Col>
                  <Upload
                    showUploadList={false}
                    beforeUpload={async (_file) => {
                      changeFile(_file);
                      return false;
                    }}
                  >
                    <Button disabled={importType !== 'file'} type={'primary'}>
                      {I18N.t('浏览')}
                    </Button>
                  </Upload>
                </Col>
              </Row>
            </Form.Item>
          </div>
        </div>
        <Row align="top" gutter={[8, 8]}>
          <Col flex="0 0 auto">
            <DMFormItem shouldUpdate>
              <Checkbox
                checked={addTags}
                onChange={(e) => {
                  setAddTags(e.target.checked);
                }}
              >
                {I18N.t('为导入的')}
                {label}
                {I18N.t('打上标签')}
              </Checkbox>
            </DMFormItem>
          </Col>
          <Col flex="1">
            <DMFormItem
              name="tags"
              initialValue={'导入达人_' + moment().format('MMDD')}
              rules={[
                {
                  validator(rule, val) {
                    const value = _.trim(val);
                    if (!value) {
                      if (addTags) {
                        return Promise.reject(I18N.t('请输入标签内容'));
                      }
                      return Promise.resolve();
                    }
                    if (val.length > 10) {
                      return Promise.reject(I18N.t('最多10个字符'));
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <Input
                disabled={!addTags}
                placeholder={I18N.t('请输入标签名称，多个标签通过逗号分隔')}
              />
            </DMFormItem>
          </Col>
          <Col>
            <DMFormItem>
              <Typography.Link>
                <HelpTooltip title={I18N.t('打标签的目的是为了更好的筛选')} />
              </Typography.Link>
            </DMFormItem>
          </Col>
        </Row>
      </Form>
    </DMModal>
  );
};
export default ImportCreatorsModal;
