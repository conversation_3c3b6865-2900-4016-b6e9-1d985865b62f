import I18N from '@/i18n';
import type { ProFormProps } from '@ant-design/pro-form';
import { StepsForm } from '@ant-design/pro-form';
import { <PERSON><PERSON>, <PERSON> } from 'antd';
import styles from '@/components/StepGuide/index.less';
import type { RefAttributes } from 'react';
import React, { forwardRef, useCallback, useImperativeHandle, useRef, useState } from 'react';
import defaultBgImg from '@/components/StepGuide/images/step-bg-default.svg';
import classNames from 'classnames';
import type { SubmitterProps } from '@ant-design/pro-form/es/components/Submitter';
import Slot from '@/components/Slot';
import _ from 'lodash';

const StepGuideFooterStatusBarSymbol = Symbol('StepGuideFooterStatusBarSymbol');
const customDot = (dot, { index }) => <span>{index + 1}</span>;
export type StepInfo = {
  title: React.ReactNode;
  extra?: React.ReactNode;
  formRef?: React.MutableRefObject<any>;
  content: () => React.ReactNode;
  footer?: (
    props: SubmitterProps<any> & {
      step: number;
      onPre: () => void;
    },
    dom: React.ReactNode[],
  ) => React.ReactNode[];
  onFinish?: (values: any) => Promise<boolean>;
  initialValues?: any;
  hidden?: boolean;
};
interface StepGuideProps {
  guideName: string;
  guideDesc?: string;
  steps: StepInfo[];
  initStep?: number;
  loading?: boolean;
  // 背景图
  bgImg?: string;
  formProps?: Omit<ProFormProps, 'initialValues'>;
  onCurrentChange?: (current: number) => void;
}

export interface StepGuideInstance {
  changeStepTo: (step: number) => void;
  isLastStep: () => boolean;
}
const StepGuideContent: React.FC<RefAttributes<StepGuideInstance> & StepGuideProps> = forwardRef(
  (props, ref) => {
    const {
      initStep = 0,
      bgImg = defaultBgImg,
      formProps = {},
      onCurrentChange = () => {},
      loading,
      steps: _steps,
      guideDesc,
      guideName,
    } = props;
    const [currentStep, handleCurrentChange] = useState<number>(initStep);
    const formMap = useRef();
    const stepNodes: React.ReactNode[] = [];
    const steps = _.filter(_steps, (o) => {
      return !o.hidden;
    });
    useImperativeHandle(ref, () => {
      return {
        changeStepTo(step: number) {
          handleCurrentChange(step);
        },
        isLastStep() {
          return currentStep === steps.length - 1;
        },
      };
    });
    steps.forEach((o, i) => {
      stepNodes.push(
        <StepsForm.StepForm<{
          name: string;
        }>
          style={{ overflow: 'hidden' }}
          name={`step-form-${i}`}
          key={o.title}
          title={o.title}
          layout="horizontal"
          preserve
          labelCol={{ span: 5 }}
          initialValues={o.initialValues}
          onFinish={o?.onFinish}
          validateTrigger="onBlur"
          formRef={o.formRef}
        />,
      );
    });

    const stepsRender = useCallback(() => {
      const stepsDom = steps?.map((o, i) => {
        return (
          <div
            key={o.title}
            className={classNames(currentStep === i ? 'active-step' : '', 'stepGuide-steps-step')}
          >
            {i + 1}、{o.title}
          </div>
        );
      });
      return (
        <div className="stepGuide-steps">
          <div className="stepGuide-steps-guideName">{guideName}</div>
          {guideDesc && <div className="stepGuide-steps-guideDesc">{guideDesc}</div>}
          <div className="stepGuide-steps-stepList">{stepsDom}</div>
          <div className="step-bg">
            <img src={bgImg} alt="" />
          </div>
        </div>
      );
    }, [bgImg, currentStep, guideDesc, guideName, steps]);

    const stepFormRender = () => (
      <Card
        className={'card-class'}
        title={steps[currentStep].title}
        extra={steps[currentStep].extra}
        bordered={false}
      >
        {steps[currentStep].content()}
      </Card>
    );

    return (
      <StepsForm<{
        name: string;
      }>
        current={currentStep}
        stepsProps={{
          direction: 'vertical',
          size: 'small',
          progressDot: customDot,
        }}
        formMapRef={formMap}
        stepsRender={stepsRender}
        stepFormRender={stepFormRender}
        layoutRender={({ stepsDom, formDom }) => {
          return (
            <div style={{ display: 'flex', flexWrap: 'nowrap', height: '100%', width: '100%' }}>
              {stepsDom}
              {formDom}
            </div>
          );
        }}
        onCurrentChange={(current) => {
          handleCurrentChange(current);
          onCurrentChange(current);
        }}
        formProps={{
          validateMessages: {
            required: I18N.t('此项为必填项'),
          },
          ...formProps,
        }}
        submitter={{
          render: (_p) => {
            const { onSubmit, onPre } = _p;
            let presetNodes: React.ReactNode[] = [];
            let nodes: React.ReactNode[] = [];
            if (currentStep === 0) {
              presetNodes = [
                <Button key="next" type="primary" disabled={loading} onClick={onSubmit}>
                  {I18N.t('下一步')}
                </Button>,
              ];
            } else if (currentStep < steps.length - 2) {
              presetNodes = [
                <Button key="pre" disabled={loading} onClick={onPre}>
                  {I18N.t('上一步')}
                </Button>,
                <Button type="primary" disabled={loading} key="next" onClick={onSubmit}>
                  {I18N.t('下一步')}
                </Button>,
              ];
            }

            if (currentStep === steps.length - 2) {
              presetNodes = [
                <Button key="pre" disabled={loading} onClick={onPre}>
                  {I18N.t('上一步')}
                </Button>,
                <Button type="primary" disabled={loading} key="submit" onClick={onSubmit}>
                  {I18N.t('提交')}
                </Button>,
              ];
            } else if (currentStep === steps.length - 1) {
              presetNodes = [
                <Button key="done" type="primary" disabled={loading} onClick={onSubmit}>
                  {I18N.t('完成')}
                </Button>,
              ];
            }

            if (steps[currentStep].footer) {
              // @ts-ignore
              nodes = steps?.[currentStep]?.footer?.(
                {
                  ..._p,
                },
                presetNodes,
              );
            } else {
              nodes = presetNodes;
            }

            return (
              <div
                className={classNames(
                  styles.footer,
                  'step-guide-footer',
                  `step-${currentStep + 1}`,
                )}
              >
                <div className={styles.statusBar}>
                  <Slot.On id={StepGuideFooterStatusBarSymbol} />
                </div>
                {nodes}
              </div>
            );
          },
        }}
      >
        {stepNodes}
      </StepsForm>
    );
  },
);
export default StepGuideContent;
