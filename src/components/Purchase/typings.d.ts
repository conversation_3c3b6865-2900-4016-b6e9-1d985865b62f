// @ts-ignore
/* eslint-disable */

export interface OrderOptions {
  agreement?: boolean;
  balanceAmount: number;
  payablePrice: number;
  payType: PayType;
  /** 代金券抵扣金额，不得大于代金券余额 */
  voucherAmount: number;
  /** 要使用的代金券id */
  voucherId?: number;
}

// 第三步的几种状态，分别代表：
// 未提交订单、正在支付、支付成功、支付完成但未支付成功
export type PayOrderStage = 'CREATING' | 'PAING' | 'PAID' | 'UNPAID';

export type PeriodUnit =
  | '月'
  | 'Byte'
  | 'GB'
  | 'GB天'
  | '个'
  | '个天'
  | '周'
  | '天'
  | '年'
  | '无'
  | '次'
  | undefined;
