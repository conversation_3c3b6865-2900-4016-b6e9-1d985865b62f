@import '~/src/style/color.less';

.default-pay-success {
  display: flex;
  flex-direction: column;
  padding: 44px 0;

  :global {
    .dm-iconFontIcon {
      margin: 8px auto 32px auto;
      color: @primary-color;
      font-size: 64px;
    }

    .success-title {
      margin: auto;
      margin-bottom: 40px;
      font-size: 24px;
      line-height: 32px;
    }

    .order-list {
      .ant-card-body {
        margin-bottom: 16px;
        padding: 0;
        font-size: 12px;
        border-top: 1px solid @border-color;
        border-right: 1px solid @border-color;
        border-left: 1px solid @border-color;

        tr th {
          color: @text-color-subTitle;
        }

        tr th,
        tr td {
          padding: 12px 4px !important;
          font-size: 12px;
          line-height: 1.5;
        }

        td:first-child,
        th:first-child {
          padding-left: 24px !important;
        }
      }
    }
  }
}
