import I18N from '@/i18n';
import PayConstans from '@/constants/PayConstans';
import React from 'react';
import IconFontIcon from '@/components/Common/IconFontIcon';
import styles from './DefaultPaySuccess.less';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

interface Props {
  orderDetail: API.OrderDetailVo;
}

const DefaultPaySuccess: React.FC<Props> = (props) => {
  const { orderDetail } = props;

  const columns: ProColumns<API.OrderDetailVo>[] = [
    {
      title: I18N.t('订单编号'),
      dataIndex: 'serialNumber',
      ellipsis: true,
      width: 100,
    },
    {
      title: I18N.t('订单类型'),
      dataIndex: 'orderType',
      render: (dom, record) => PayConstans.orderType[record.orderType || '--'],
      width: 60,
    },
    {
      title: I18N.t('创建时间'),
      dataIndex: 'createTime',
      valueType: 'dateTime',
      width: 90,
    },
    {
      title: I18N.t('应付金额'),
      dataIndex: 'totalPrice',
      valueType: 'money',
      width: 50,
    },
    {
      title: I18N.t('实付金额'),
      dataIndex: 'realPrice',
      valueType: 'money',
      width: 50,
    },
  ];

  return (
    <div className={styles.defaultPaySuccess}>
      <IconFontIcon iconName="chenggong_24" />
      <div className="success-title">{I18N.t('支付成功')}</div>
      <div className="order-list">
        <ProTable
          dataSource={[orderDetail]}
          rowKey="id"
          columns={columns}
          search={false}
          pagination={false}
          options={false}
        />
      </div>
    </div>
  );
};

export default DefaultPaySuccess;
