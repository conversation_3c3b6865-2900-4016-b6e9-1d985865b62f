@import '~/src/style/color.less';
@import '~/src/style/mixin.less';

.createOrder {
  position: relative;
  height: 100%;
  overflow: auto;

  :global {
    .dm-label-row {
      margin: 0 0 24px 0;

      .row-value {
        padding-left: 10px;
      }
    }

    .text-sub-title {
      color: @text-color-subTitle;
    }

    .use-voucher {
      align-items: flex-start;
    }

    .voucher-card {
      display: flex;
      width: 564px;
      background-color: @primary-color;
      border: 1px solid @border-color;
      border-radius: 3px;

      .voucher-card-left {
        position: relative;
        flex-grow: 0;
        flex-shrink: 0;
        width: 206px;
        height: 91px;
        padding: 6px;
        color: #fff;
        font-size: 12px;
        background-image: url('./images/voucher.svg');
        background-repeat: no-repeat;
        background-position: 147px 31px;
        background-size: 90px 90px;

        .voucher-card-title {
          display: flex;
          justify-content: space-between;
          line-height: 20px;
        }

        .voucher-card-left-value {
          margin: 9px auto;
          font-size: 28px;
          line-height: 36px;
          text-align: center;
        }
      }

      .voucher-card-right {
        display: flex;
        flex-grow: 1;
        flex-wrap: wrap;
        height: 91px;
        padding: 16px;
        line-height: 20px;
        background-color: #fff;
        border-radius: 0 1px 1px 0;

        .dm-label-row {
          margin-top: 0;
          margin-bottom: 13px;

          &.voucher-detail-row-left {
            width: 196px;
          }

          .row-label,
          .row-value {
            min-height: 22px;
            font-size: 14px;
            line-height: 22px;
          }
        }
      }

      &.voucher-card-checked .voucher-card-checked {
        display: block;
      }
    }

    .pay-method-row {
      .ant-radio-button-wrapper {
        height: 45px;
        margin-right: 16px;
        padding: 0;
        border-radius: 0;

        &:last-child {
          margin-right: 0;
        }
      }

      .pay-method {
        width: 120px;
        height: 45px;
        background-repeat: no-repeat;
        background-position: center;

        &.alipay {
          background-image: url('./images/alipay.svg');
          background-size: 62px 23px;
        }

        &.wechat {
          background-image: url('./images/wechatPay.svg');
          background-size: 67px 22px;
        }

        &.bank {
          background-image: url('./images/bank.svg');
          background-size: 84px 19px;
        }
      }

      .ant-radio-button-wrapper-checked {
        border-width: 2px;
      }
      .ant-radio-button-wrapper-checked::after {
        position: absolute;
        top: 0;
        right: 0;
        width: 27px;
        height: 27px;
        background: url('./images/checked.svg') center no-repeat;
        content: '';
      }
    }

    .need-pay-row .row-value {
      flex-direction: column;
      align-items: flex-start;
      margin-top: 6px;

      .need-pay {
        margin-top: -3px;
        color: #faad14;
        line-height: 22px;

        .need-pay-number {
          font-size: 30px;
        }
      }
    }

    .create-order-foot {
      display: flex;
      justify-content: space-between;
    }
  }
}
.need-pay-row {
  margin-top: 10px;

  .row-value {
    flex-direction: column;
    align-items: flex-start;
    margin-top: 6px;

    .need-pay {
      color: #faad14;
      line-height: 22px;

      .need-pay-number {
        font-size: 30px;
      }
    }
  }
}
