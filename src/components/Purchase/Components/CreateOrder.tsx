import I18N from '@/i18n';
import type { FC } from 'react';
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import LabelRow from '@/components/Common/LabelRow';
import { Space, InputNumber, Radio, Checkbox, Button, message } from 'antd';
import { useRequest } from 'umi';
import constants from '@/constants';
import {
  paymentBalanceGet,
  paymentVoucherCard_numberByCardNumberGet,
} from '@/services/api-PaymentAPI/PaymentController';
import styles from './CreateOrder.less';
import ErrorBlock from '@/components/Common/ErrorBlock';
import MiddleSpin from '@/components/Common/MiddleSpin';
import type { PayType } from '@/types/Purchase';
import { openOfficialSiteByAppWindow } from '@/utils/pageUtils';
import type { OrderOptions } from '../typings';
import { useVoucherSelect } from '@/pages/Cost/tabpanes/Voucher/components/VoucherSelect';
import { voucherTypeScopeCheck } from '@/utils/purchaseUtils';
import { AvailableVoucherType } from '@/constants/PayConstans';

const payTypeOptions = [
  { label: <div className="pay-method alipay" />, value: 'AliPay' },
  { label: <div className="pay-method wechat" />, value: 'WechatPay' },
  { label: <div className="pay-method bank" />, value: 'BankPay' },
];

interface Props {
  showAgreement?: boolean;
  orderTarget?: React.ReactElement;
  totalPrice: number | undefined;
  orderDetail?: API.OrderDetailVo;
  orderType?: string;
  onOrderOptionsChange: (orderOptions: OrderOptions) => void;
}

const CreateOrder: React.FC<Props> = (props) => {
  const labelWidth = 110;
  const {
    showAgreement = true,
    orderTarget,
    totalPrice: oldTotalPrice,
    orderDetail,
    orderType,
    onOrderOptionsChange,
  } = props;
  const payMethodLocked = orderDetail?.payStatus === 'Locked';

  // 解决totalPrice不精确的bug
  const totalPrice =
    oldTotalPrice !== undefined ? Math.floor(oldTotalPrice * 100) / 100 : undefined;
  const { data: teamBalance, loading, error } = useRequest(paymentBalanceGet);
  const [voucher, setVoucher] = useState<API.VoucherVo | undefined>();
  const [isUseBalance, setIsUseBalance] = useState<boolean>(!!orderDetail?.balanceAmount || false);
  const [balanceAmount, setBalanceAmount] = useState<number>(orderDetail?.balanceAmount || 0);
  const [payType, setPayType] = useState<PayType>(orderDetail?.cashPayType || 'AliPay');
  const [agreement, setAgreement] = useState<boolean>(true);
  // 自动设置使用团队余额的数量
  // 此时应该是优先使用选代金券，再使用所有可用的团队余额，最后不足的部分才需要现金支付
  const autoSetBalanceAmountUse = (
    newVoucher: API.VoucherVo | undefined,
    newIsUseBalance: boolean,
  ) => {
    let newBalanceAmount = 0;
    // 数据加载完成才需要计算
    if (totalPrice && teamBalance) {
      // 有团队余额时才需要计算
      if (teamBalance > 0) {
        if (newVoucher) {
          // 有选择使用代金券时先扣除代金券的所有余额
          newBalanceAmount = totalPrice - newVoucher.balance!;
          newBalanceAmount = Math.max(0, newBalanceAmount);
          if (newIsUseBalance) {
            // 剩余需要支付的金额中不超过团队余额的部分就是默认使用团队余额的数量
            newBalanceAmount = Math.min(teamBalance, newBalanceAmount);
          }
        } else if (newIsUseBalance) {
          // 未选择使用代金券时待付金额中不超过团队余额的部分就是默认使用团队余额的数量
          newBalanceAmount = Math.min(teamBalance, totalPrice);
        }
      }
      setBalanceAmount(newBalanceAmount);
    }
  };
  const openVoucherSelectModal = useVoucherSelect(
    voucher ? [voucher] : [],
    (vouchers) => {
      const newVoucher = vouchers?.[0];
      setVoucher(newVoucher);
      autoSetBalanceAmountUse(newVoucher, isUseBalance);
    },
    (v) => {
      // 限制只能选择可用范围相匹配的代金券
      const newOrderType = orderType || orderDetail?.orderType;
      const availableVoucherType = AvailableVoucherType[newOrderType || ''];
      return (
        (v.minOrderPrice || 0) > (totalPrice || 0) ||
        !voucherTypeScopeCheck(v.voucherType || 0, availableVoucherType)
      );
    },
  );

  useEffect(() => {
    if (!payMethodLocked) {
      let newVoucher = voucher;
      // 订单总价发生变化时需要重新检查之前选择的代金券是否可用
      if (voucher && totalPrice && voucher.minOrderPrice && totalPrice < voucher.minOrderPrice) {
        newVoucher = undefined;
        setVoucher(newVoucher);
      }
      autoSetBalanceAmountUse(newVoucher, isUseBalance);
    }
  }, [isUseBalance, totalPrice, voucher]);

  // 如果是重新支付，需要查询之前选择的代金券信息
  useEffect(() => {
    if (orderDetail && orderDetail.voucherCardNumber) {
      paymentVoucherCard_numberByCardNumberGet({ cardNumber: orderDetail.voucherCardNumber }).then(
        (rs) => {
          if (rs.data) {
            setVoucher(rs.data);
          }
        },
      );
    }
  }, [orderDetail]);

  // 除了余额支付之外还要用多少代金券抵扣
  let voucherAmount = 0;
  // 除了代金券和余额支付之外还要付多少现金
  let payablePrice = 0;
  if (totalPrice) {
    if (payMethodLocked) {
      // 如果是锁定的订单，直接显示后台记录的数据
      payablePrice = orderDetail.realPrice || 0;
      voucherAmount = orderDetail.voucherAmount || 0;
    } else {
      payablePrice = totalPrice;
      if (isUseBalance) {
        // 优先使用用户指定的账户余额
        payablePrice -= balanceAmount;
      }
      if (voucher) {
        // 使用用户指定的账户余额支付完后还不够的部分使用用户选择的代金券
        voucherAmount = Math.min(payablePrice, voucher.balance!);
        // 使用用户选择的代金券支付完后还不够的部分就需要现金支付了
        payablePrice -= voucherAmount;
      }
    }
  }

  // 自动汇报支付信息给父组件
  useEffect(() => {
    onOrderOptionsChange({
      agreement,
      payablePrice,
      balanceAmount: isUseBalance ? balanceAmount : 0,
      payType,
      voucherAmount,
      voucherId: voucher?.id,
    });
  }, [
    agreement,
    payablePrice,
    isUseBalance,
    balanceAmount,
    payType,
    voucherAmount,
    voucher,
    onOrderOptionsChange,
  ]);

  // 选择使用代金券后需要更新使用团队余额的数量

  // 选择使用账户余额支付后自动填写需要使用余额支付的金额
  // 此时应该是优先使用选代金券，再使用所有可用的团队余额，最后不足的部分才需要现金支付
  const onSetIsUseBalance = (newIsUseBalance: boolean) => {
    setIsUseBalance(newIsUseBalance);
    autoSetBalanceAmountUse(voucher, newIsUseBalance);
  };

  if (loading) {
    return <MiddleSpin />;
  }
  if (error) {
    return <ErrorBlock error={error} />;
  }

  let maxBlanceAmount = 0;
  if (totalPrice) {
    // 最多可用的余额支付数量
    maxBlanceAmount = totalPrice;
    if (voucher) {
      maxBlanceAmount -= voucher.balance!;
    }
    maxBlanceAmount = Math.min(maxBlanceAmount, teamBalance!);
  }
  let realPrice = '--';
  if (payMethodLocked && orderDetail.realPrice) {
    realPrice = orderDetail.realPrice?.toFixed(2);
  } else if (totalPrice !== undefined) {
    realPrice = payablePrice.toFixed(2);
  }

  return (
    <div className={styles.createOrder}>
      <div className="create-order-form">
        {orderTarget}
        {orderType !== 'RechargeCredit' && (
          <LabelRow label={I18N.t('代金券抵扣')} labelMuted={true} labelWidth={labelWidth}>
            {voucher ? (
              <Space className="use-voucher">
                {I18N.t('已经选择代金券抵扣')}
                {voucherAmount.toFixed(2)}
                {I18N.t('元')}
                {!payMethodLocked && (
                  <Space className="voucher-actions">
                    <a onClick={() => openVoucherSelectModal()}>{I18N.t('重新选择')}</a>
                    <a onClick={() => setVoucher(undefined)}>{I18N.t('不使用代金券')}</a>
                  </Space>
                )}
              </Space>
            ) : (
              <Space>
                <span className="text-sub-title">{I18N.t('未选择代金券')}</span>
                {!payMethodLocked && (
                  <a onClick={() => openVoucherSelectModal()}>{I18N.t('选择')}</a>
                )}
              </Space>
            )}
          </LabelRow>
        )}
        <LabelRow label={I18N.t('账户余额扣减')} labelMuted={true} labelWidth={labelWidth}>
          <Checkbox
            checked={isUseBalance}
            disabled={payMethodLocked}
            onChange={(e) => onSetIsUseBalance(e.target.checked)}
          >
            {I18N.t('从账户余额（可用￥')}

            {teamBalance?.toFixed(2)}
            {I18N.t('）中扣减')}
          </Checkbox>
          <Space>
            <InputNumber
              min={0}
              max={maxBlanceAmount}
              value={balanceAmount}
              size="small"
              onChange={setBalanceAmount}
              disabled={!isUseBalance || payMethodLocked}
              style={{ width: '80px' }}
            />
            {I18N.t('元')}
          </Space>
        </LabelRow>
        <LabelRow
          label={I18N.t('现金支付方式')}
          labelMuted={true}
          labelWidth={labelWidth}
          className="pay-method-row"
        >
          <Radio.Group
            options={payTypeOptions}
            onChange={(e) => setPayType(e.target.value)}
            value={payType}
            disabled={payMethodLocked}
            optionType="button"
            style={{ marginTop: '-5px' }}
          />
        </LabelRow>
        <LabelRow
          label={I18N.t('应付金额')}
          labelMuted={true}
          labelWidth={labelWidth}
          className="need-pay-row"
        >
          <div className="need-pay">
            <span className="need-pay-number">{realPrice}</span>
            {I18N.t('元')}
          </div>
        </LabelRow>
      </div>
      {showAgreement && (
        <div className="create-order-foot">
          <Checkbox
            checked={agreement}
            disabled={payMethodLocked}
            onChange={(e) => setAgreement(e.target.checked)}
          >
            {I18N.t('我已阅读和同意')}

            <a
              onClick={(e) => {
                e.preventDefault();
                openOfficialSiteByAppWindow('/service/license');
              }}
              target="_blank"
            >
              《{constants.siteName}
              {I18N.t('用户协议》')}
            </a>
          </Checkbox>
        </div>
      )}
    </div>
  );
};

export default CreateOrder;

type Options = {
  onFinish: () => void;
  onSubmit: (data: OrderOptions & { immediatePay: boolean }) => Promise<API.CreateOrderResponse>;
  title?: string;
  totalPrice: number;
  width?: number;
};
