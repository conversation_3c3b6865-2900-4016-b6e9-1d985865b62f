import React from 'react';
import ErrorBlock from '@/components/Common/ErrorBlock';
import BuyOrderPaySuccess from './BuyIp/Components/BuyOrderPaySuccess';
import RechargeSuccess from './Recharge/RechargeSuccess';
import MergePaySuccess from './MergePaySuccess';
import { useRequest } from 'umi';
import { paymentOrdersByOrderIdDetailGet } from '@/services/api-PaymentAPI/PaymentController';
import MiddleSpin from '../Common/MiddleSpin';
import IpRenewPaySuccess from './IpRenew/IpRenewPaySuccess';
import CreditChargePaySuccess from './CreditCharge/CreditChargePaySuccess';
import DefaultPaySuccess from './DefaultPaySuccess';

interface Props {
  orderId: number;
}

const PaySuccess: React.FC<Props> = (props) => {
  const { orderId } = props;
  const {
    data: orderDetail,
    loading,
    error,
  } = useRequest(() => {
    return paymentOrdersByOrderIdDetailGet({
      orderId,
    });
  });

  if (error) {
    return <ErrorBlock error={error} />;
  }

  if (loading || !orderDetail) {
    return <MiddleSpin />;
  }

  switch (orderDetail.orderType) {
    case 'BuyIp':
      return <BuyOrderPaySuccess orderDetail={orderDetail} />;
    case 'Recharge':
      return <RechargeSuccess orderDetail={orderDetail} />;
    case 'Merge':
      return <MergePaySuccess orderDetail={orderDetail} />;
    case 'RenewIp':
      return <IpRenewPaySuccess orderDetail={orderDetail} />;
    case 'RechargeCredit':
      return <CreditChargePaySuccess orderDetail={orderDetail} />;
    default:
      return <DefaultPaySuccess orderDetail={orderDetail} />;
  }
};

export default PaySuccess;
