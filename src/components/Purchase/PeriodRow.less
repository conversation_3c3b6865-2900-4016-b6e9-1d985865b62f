@import '~/src/style/color.less';

.period-row {
  :global {
    .ant-radio-button-wrapper {
      width: 75px;
      margin-right: 8px;
      text-align: center;
      border-left: 1px solid @border-color-input;
      border-radius: 3px;

      &:last-child {
        margin-right: 0;
      }

      &.ant-radio-button-wrapper-checked {
        color: #fff;
        background-color: @primary-color;
        border-left-color: @primary-color;
      }

      &::before {
        content: none;
      }

      .ant-badge {
        width: 40px;
        color: inherit;
      }

      sup {
        top: -12px;
        right: -5px;
        width: 36px;
        height: 20px;
        background: #faad14;
        border-radius: 2px;
      }
    }
  }
}
