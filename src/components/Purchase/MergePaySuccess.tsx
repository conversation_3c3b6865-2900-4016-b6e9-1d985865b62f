import I18N from '@/i18n';
import PayConstans from '@/constants/PayConstans';
import React from 'react';
import IconFontIcon from '@/components/Common/IconFontIcon';
import styles from './MergePaySuccess.less';
import {
  paymentFindOrderResourcesGet,
  paymentOrdersGet,
} from '@/services/api-PaymentAPI/PaymentController';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

interface Props {
  orderDetail: API.OrderDetailVo;
}

const MergePaySuccess: React.FC<Props> = (props) => {
  const { orderDetail } = props;

  const columns: ProColumns<API.OrderDetailVo & { resources: API.OrderResourceVo[] }>[] = [
    {
      title: I18N.t('订单编号'),
      dataIndex: 'serialNumber',
      ellipsis: true,
      width: 100,
    },
    {
      title: I18N.t('订单类型'),
      dataIndex: 'orderType',
      render: (dom, record) => PayConstans.orderType[record.orderType || '--'],
      width: 50,
    },
    {
      title: I18N.t('商品明细'),
      width: 50,
      render: (dom, record) => `${record.resources.length}${I18N.t('个IP')}`,
    },
    {
      title: I18N.t('创建时间'),
      dataIndex: 'createTime',
      valueType: 'dateTime',
      width: 100,
    },
    {
      title: I18N.t('应付金额'),
      dataIndex: 'totalPrice',
      valueType: 'money',
      width: 50,
    },
    {
      title: I18N.t('实付金额'),
      dataIndex: 'realPrice',
      valueType: 'money',
      width: 50,
    },
  ];

  return (
    <div className={styles.mergePaySuccess}>
      <IconFontIcon iconName="chenggong_24" />
      <div className="success-title">{I18N.t('支付成功')}</div>
      <div className="success-description">
        {I18N.t(
          '如果您的订单中包含购买IP地址的操作，大概需要5分钟左右的时间，创建成功后您将获得消息通知，请稍候...',
        )}
      </div>
      <div className="order-list">
        <ProTable
          request={async () => {
            const rs1 = await paymentOrdersGet({
              parentOrderId: orderDetail.id,
              pageNum: 1,
              pageSize: 9999,
            });
            const rs2 = await paymentFindOrderResourcesGet({ orderIds: orderDetail.id || 0 });
            return { data: [] };
            return {
              data: rs1.data?.list?.map((o) => {
                return { ...o, resources: rs2.data!![o.id || 0] };
              }),
            };
          }}
          rowKey="id"
          columns={columns}
          search={false}
          pagination={false}
          options={false}
        />
      </div>
    </div>
  );
};

export default MergePaySuccess;
