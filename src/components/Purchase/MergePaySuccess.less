@import '~/src/style/color.less';

.merge-pay-success {
  display: flex;
  flex-direction: column;
  padding: 44px 0;

  :global {
    .dm-iconFontIcon {
      margin: 8px auto 32px auto;
      color: @primary-color;
      font-size: 64px;
    }

    .success-title {
      margin: auto;
      font-size: 24px;
      line-height: 32px;
    }

    .success-description {
      margin: 16px 11px 24px 11px;
      color: @text-color-subTitle;
      line-height: 22px;
      text-align: center;
    }

    .order-list {
      .ant-card-body {
        margin-bottom: 16px;
        padding: 0;
        font-size: 12px;
        border-top: 1px solid @border-color;
        border-right: 1px solid @border-color;
        border-left: 1px solid @border-color;

        tr th {
          color: @text-color-subTitle;
        }

        tr th,
        tr td {
          padding: 12px 4px !important;
          line-height: 1;
        }

        td:first-child,
        th:first-child {
          padding-left: 24px !important;
        }
      }
    }
  }
}
