import I18N from '@/i18n';
import { FC, useMemo } from 'react';
import { useRef, useState } from 'react';
import { Form, Input, message, Tooltip, Typography } from 'antd';

import DMModal from '@/components/Common/Modal/DMModal';
import { scrollProTableOptionFn } from '@/mixins/table';
import type { ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import ColoursIcon from '@/components/Common/ColoursIcon';
import { ComplexTagIdsFormItem } from '@/components/Common/Selector/TagSelector';
import { useRequest } from '@@/plugin-request/request';
import MoreDropdown from '@/components/Common/MoreDropdown';
import styles from '@/components/Common/MoreDropdown/index.less';
import TagItem from '@/components/Common/TagManage/TagItem';
import Placeholder from './Common/Placeholder';
import styled from 'styled-components';
import { shopMobileAccountListAuthorizedAccountsGet } from '@/services/api-ShopAPI/MobileAccountController';
import PlatformCateIcon from '@/components/Common/PlatformCateIcon';
import { AreaIcon } from '@/components/Common/LocationCascader';
import { useOrder } from '@/components/Sort/SortDropdown';
import SortTitle from '@/components/Sort/SortTitle';
import CategorySelector from '@/components/Common/Selector/CategorySelector';
import MobileSelector from '@/components/Common/Selector/MobileSelector';
import { ghBindMobileAccountToCreatorPut } from '@/services/api-TKGHAPI/GhCreatorController';
import { trimValues } from '@/utils/utils';
import _ from 'lodash';

interface SelectMobileTableProps {
  current?: number;
  creatorId: number;
  onUpdate: () => void;
  ghBizScene: API.ghBindMobileAccountToCreatorPutParams['ghBizScene'];
}
const StyledWrap = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;

  .mobile-filter-header {
    display: flex;
    flex: 0 0 auto;
    justify-content: flex-end;
    .ant-form-item {
      margin-right: 8px;
      &:last-child {
        margin-right: 0;
      }
    }
  }

  .mobile-table-container {
    height: 450px;
  }
`;
export const SelectMobileAccountModal: FC<SelectMobileTableProps> = (props) => {
  const { current, creatorId, onUpdate, ghBizScene } = props;
  const { order, changeOrder } = useOrder({
    key: 'username',
    ascend: true,
  });
  const [timestamp, setTimestamp] = useState<number>(Date.now());
  const [selectedKeys, setSelectedKeys] = useState<number[]>(current ? [current] : []);
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(true);
  const table = useRef<ActionType>();
  const { run: submit, loading } = useRequest(
    async () => {
      await ghBindMobileAccountToCreatorPut({
        creatorId,
        mobileAccountId: selectedKeys[0],
        ghBizScene,
      });
      onUpdate();
      message.success(I18N.t('分配成功'));
      setVisible(false);
    },
    {
      manual: true,
    },
  );
  const { data: list, loading: fetching } = useRequest(
    () => {
      return shopMobileAccountListAuthorizedAccountsGet();
    },
    {
      onSuccess(res) {
        setSelectedKeys(
          res
            ?.filter((item) => {
              return (
                item.tags?.findIndex((tag) => {
                  return tag?.tag === I18N.t('账号封禁');
                }) === -1
              );
            })
            .map((item) => item.id!) || [],
        );
      },
    },
  );
  const dataSource = useMemo(() => {
    if (!list) {
      return [];
    }
    const { platformTypes, mobileId, tagIds, tagLc, query } = trimValues(form.getFieldsValue());
    return list.filter((item) => {
      let result = true;
      if (platformTypes?.length) {
        result = platformTypes.includes(item.platform?.typeName);
      }
      if (mobileId && result) {
        result = item.mobileId === mobileId;
      }
      if (tagIds?.length && result) {
        if (tagLc === 'OR') {
          return _.some(tagIds, (tagId) => {
            return _.some(item.tags, (tag) => {
              return tag.id === tagId;
            });
          });
        } else {
          return _.every(tagIds, (tagId) => {
            return _.some(item.tags, (tag) => {
              return tag.id === tagId;
            });
          });
        }
      }
      if (query && result) {
        result = !!item.username?.includes(query);
      }
      return result;
    });
  }, [list, form]);

  return (
    <DMModal
      width={780}
      title={I18N.t('选择账号')}
      confirmLoading={loading}
      okButtonProps={{
        disabled: !selectedKeys.length,
      }}
      onOk={submit}
      open={visible}
      bodyStyle={{ paddingTop: 8 }}
      onCancel={() => {
        setVisible(false);
      }}
    >
      <StyledWrap>
        <div className="mobile-filter-header">
          <Form
            form={form}
            style={{
              display: 'flex',
              justifyContent: 'flex-end',
              gap: 8,
            }}
          >
            <Form.Item noStyle name={'platformTypes'}>
              <CategorySelector
                onChange={() => {
                  setTimestamp(() => Date.now());
                }}
                style={{ width: '140px' }}
              />
            </Form.Item>
            <Form.Item noStyle name={'mobileId'}>
              <MobileSelector
                onChange={() => {
                  setTimestamp(() => Date.now());
                }}
                style={{ width: '140px' }}
              />
            </Form.Item>
            <Form.Item noStyle shouldUpdate>
              <ComplexTagIdsFormItem
                style={{ width: '140px' }}
                showCondition
                onChange={() => {
                  setTimestamp(() => Date.now());
                }}
                resourceType={'MobileAccount'}
                form={form}
              />
            </Form.Item>
            <Form.Item noStyle name="query">
              <Input.Search
                onChange={() => {
                  setTimestamp(() => Date.now());
                }}
                allowClear
                placeholder={I18N.t('根据名称检索')}
                style={{ width: '200px' }}
              />
            </Form.Item>
          </Form>
        </div>
        <div className="mobile-table-container">
          <ProTable<API.MobileAccountVo>
            {...scrollProTableOptionFn({
              size: 'small',
              search: false,
              rowSelection: {
                type: 'radio',
                preserveSelectedRowKeys: true,
                selectedRowKeys: selectedKeys,
                onChange: (keys) => {
                  setSelectedKeys(keys);
                },
                getCheckboxProps(record) {
                  return {
                    disabled:
                      record.tags?.findIndex((tag) => {
                        return tag?.tag === I18N.t('账号封禁');
                      }) !== -1,
                  };
                },
              },
              pagination: false,
              onRow(record: API.MobileAccountVo) {
                const disabled =
                  record.tags?.findIndex((tag) => {
                    return tag?.tag === I18N.t('账号封禁');
                  }) !== -1;
                return {
                  style: {
                    cursor: 'pointer',
                  },
                  onClick: () => {
                    if (!disabled) {
                      setSelectedKeys([record.id!]);
                    }
                  },
                  disabled,
                };
              },
            })}
            components={{
              body: {
                row(props) {
                  const { children, className, disabled, ...rest } = props;

                  return (
                    <Tooltip
                      placement={'left'}
                      title={disabled ? I18N.t('该账号已封禁') : undefined}
                    >
                      <tr className={className} {...rest}>
                        {children}
                      </tr>
                    </Tooltip>
                  );
                },
              },
            }}
            columns={[
              {
                title: (
                  <SortTitle
                    label={I18N.t('账号名称')}
                    order={order}
                    orderKey={'username'}
                    onSort={changeOrder}
                  />
                ),

                dataIndex: 'username',
                ellipsis: true,
              },
              {
                title: I18N.t('平台'),
                width: 60,
                dataIndex: 'platform',
                render(_text, record) {
                  const { platform } = record;
                  return <PlatformCateIcon platformName={platform?.name} />;
                },
              },
              {
                title: I18N.t('站点'),
                dataIndex: 'site',
                width: 60,
                render(_text, record) {
                  const { platform } = record;
                  return <AreaIcon area={platform?.area} />;
                },
              },
              {
                title: I18N.t('手机'),
                dataIndex: 'mobile',
                width: 150,
                ellipsis: true,
                render(_text, record) {
                  const { mobileName } = record;
                  return (
                    <div
                      style={{
                        display: 'flex',
                        flexWrap: 'nowrap',
                        gap: 4,
                        overflow: 'hidden',
                      }}
                    >
                      <span style={{ flex: '0 0 16px' }}>
                        <ColoursIcon className={'Android_24'} />
                      </span>
                      <Typography.Text style={{ flex: 1 }} ellipsis>
                        {mobileName}
                      </Typography.Text>
                    </div>
                  );
                },
              },
              {
                title: I18N.t('标签'),
                key: 'tags',
                width: 250,
                render: (dom, record) => {
                  const { tags } = record;
                  if (tags?.length) {
                    return (
                      <MoreDropdown
                        className={styles.tagMoreWrapper}
                        data={tags}
                        renderItem={(tag) => {
                          return <TagItem tag={tag} key={tag.id} />;
                        }}
                      />
                    );
                  }
                  return <Placeholder />;
                },
              },
            ]}
            loading={fetching}
            dataSource={dataSource}
            actionRef={table}
          />
        </div>
      </StyledWrap>
    </DMModal>
  );
};
