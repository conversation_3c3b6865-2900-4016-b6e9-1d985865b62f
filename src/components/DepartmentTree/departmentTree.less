@import '~/src/style/color.less';

.departmentTreeMixin(@department-tree-width) {
  overflow-x: hidden;
  overflow-y: auto;

  :global {
    .ant-tree-treenode-motion {
      width: @department-tree-width;
    }

    .ant-tree-treenode {
      display: flex;
      width: @department-tree-width;
      padding-bottom: 0;

      &.ant-tree-treenode-selected {
        background-color: @active-background;
      }
    }

    .ant-tree-node-content-wrapper {
      display: flex;
      flex: 1;
      overflow: hidden;

      &.ant-tree-node-selected,
      &:hover {
        background-color: transparent;
      }

      .team-name,
      .dm-iconFontIcon {
        margin: 4px 4px 4px 0;
      }
    }

    .ant-tree-title {
      display: flex;
      flex: 1;
      overflow: hidden;
    }

    .ant-tree-switcher {
      background: transparent;

      &.ant-tree-switcher-noop {
        .ant-tree-switcher-line-icon {
          display: none;
        }

        &::before {
          position: absolute;
          top: 20px;
          left: 3px;
          width: 15px;
          border-bottom: 1px dashed @tree-line-color;
          content: '';
        }
      }

      .ant-tree-switcher-icon {
        margin: 10px 0 0 0;
        transform: scale(0.5);

        .dm-iconFontIcon {
          width: 20px;
          height: 20px;
          font-size: 20px;
        }
      }

      &.ant-tree-switcher_open {
        .icon-angle-down_24 {
          display: block;
        }

        .icon-angle-right_24 {
          display: none;
        }
      }

      &.ant-tree-switcher_close {
        .icon-angle-down_24 {
          display: none;
        }

        .icon-angle-right_24 {
          display: block;
        }
      }
    }

    .department-node {
      display: flex;
      flex: 1;
      height: 40px;
      overflow: hidden;
      line-height: 40px;
    }

    .ant-tree-indent-unit::before {
      top: -46px;
      right: 36px;
      bottom: -21px;
      border-right: 1px dashed @tree-line-color;
    }

    .ant-tree-indent-unit:last-child::after {
      position: absolute;
      top: -21px;
      right: -2px;
      bottom: 9px;
      display: block;
      width: 15px;
      background: url('./dashed-19.png') 0 41px repeat-x;
      border-left: 1px dashed @tree-line-color;
      content: '';
    }

    .ant-tree-treenode-switcher-open + .ant-tree-treenode .ant-tree-indent-unit:last-child::after {
      top: -11px;
      background-position-y: 31px;
    }
    .ant-tree-treenode-leaf-last .ant-tree-indent-unit:last-child::after {
      bottom: 19px;
    }
  }
}
