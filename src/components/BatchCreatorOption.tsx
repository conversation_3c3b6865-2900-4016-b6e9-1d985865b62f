import I18N from '@/i18n';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import DMModal from '@/components/Common/Modal/DMModal';
import { Alert, Form, Input, message, Radio, Tooltip, Typography } from 'antd';
import type { GhostModalWrapperComponentProps } from '@/mixins/modal';
import { GhostModalCaller } from '@/mixins/modal';
import DMConfirm from '@/components/Common/DMConfirm';
import ExportCreatorsModal from '@/pages/components/ExportTiktokCreatorsModal';
import TagOperation from '@/components/Common/TagOperation';
import CollectOperation from '@/components/Common/CollectOperation';
import { ghFavoriteByQueryPost, ghTagByQueryPost } from '@/services/api-TKGHAPI/GhUserController';
import useTeamFavor from '@/hooks/useTeamFavor';
import type { ResourceType } from '@/types/ResourceType';
import { getLabelByResourceType } from '@/components/Common/TagManage';
import {
  tkshopCreatorDeleteByQueryPost,
  tkshopCreatorGroupByRegionPost,
} from '@/services/api-TKShopAPI/TkshopCreatorController';
import { useRequest } from '@@/plugin-request/request';
import { useLocationGroupConfirmModal } from '@/pages/TikTok/Live/components/LocationGroupConfirmModal';
import { useSyncCreatorsModal } from '@/pages/TikTok/components/SyncCreatorSelectedModal';
import InviteSelectedModal from '@/pages/TikTok/components/InviteSelectedModal';
import SendMsgToCreatorSelectedModal from '@/pages/TikTok/components/SendMsgToCreatorSelectedModal';
import { dateFormat, trimValues } from '@/utils/utils';
import IconFontIcon from '@/components/Common/IconFontIcon';
import DMFormItem, { DMFormItemContext } from '@/components/Common/DMFormItem';
import {
  tkshopGlobalCreatorGroupByRegionPost,
  tkshopGlobalCreatorImportToTeamPost,
} from '@/services/api-TKShopAPI/TkshopGlobalCreatorController';
import { tkshopSystemStatusGet } from '@/services/api-TKShopAPI/TkshopSystemController';
import UpgradeModal from '@/pages/Setting/components/UpgradeModal';
import { openCustomService } from '@/components/CustomService';
import HelpLink from '@/components/HelpLink';
import { showFunctionCodeAlert, useAuthJudgeCallback } from '@/components/Common/FunctionButton';
import Functions from '@/constants/Functions';

export const BATCH_LIMIT = 2000;

export const ImportGlobalCreatorsConfirmModal = (
  props: GhostModalWrapperComponentProps & {
    onSubmit: (tag: string) => Promise<any>;
  },
) => {
  const { onSubmit, modalProps } = props;
  const title = useMemo(() => {
    return I18N.t('确定要将选中的公海达人导入至您的团队达人库吗？');
  }, []);
  const ref = useRef<HTMLInputElement>();
  const [visible, setVisible] = useState(true);
  const [form] = Form.useForm();
  const hasAuth = useAuthJudgeCallback();
  const { run: submit, loading } = useRequest(
    async () => {
      const { tag } = trimValues(await form.validateFields());
      if (!hasAuth(Functions.TKSHOP_CREATOR_MANAGER)) {
        showFunctionCodeAlert();
        return;
      }
      await onSubmit(tag);
      message.success(I18N.t('导入成功'));
      setVisible(false);
    },
    {
      manual: true,
    },
  );
  useEffect(() => {
    ref.current?.focus();
  }, []);
  return (
    <DMModal
      headless
      bodyStyle={{
        paddingBottom: 0,
      }}
      width={600}
      onOk={submit}
      confirmLoading={loading}
      open={visible}
      onCancel={() => {
        setVisible(false);
      }}
      {...modalProps}
    >
      <div style={{ display: 'flex', alignItems: 'flex-start', gap: 24, overflow: 'hidden' }}>
        <Typography.Link>
          <IconFontIcon iconName={'bangzhu_241'} size={48} />
        </Typography.Link>
        <div style={{ display: 'flex', flex: 1, flexDirection: 'column', gap: 16 }}>
          {title}
          <Form requiredMark={false} form={form}>
            <DMFormItemContext.Provider value={{ disableLabelMuted: false, labelWidth: 190 }}>
              <DMFormItem
                label={I18N.t('导入后为这批达人打个标签')}
                name={'tag'}
                initialValue={`公海达人_${dateFormat(new Date(), 'MMDD')}`}
              >
                <Input ref={ref} allowClear />
              </DMFormItem>
            </DMFormItemContext.Provider>
          </Form>
        </div>
      </div>
    </DMModal>
  );
};

const BatchCreatorOption = (props: {
  total: number;
  global?: boolean;
  getParams: () => any;
  onUpdate: () => void;
  resourceType: ResourceType;
}) => {
  const { total, getParams, onUpdate, resourceType, global } = props;
  const [visible, setVisible] = useState(true);
  const [form] = Form.useForm();
  const scene = 'ShopCreator';
  const groupRegionCallback = useLocationGroupConfirmModal();
  const syncCreators = useSyncCreatorsModal();
  const [type, setType] = useState<
    'invite' | 'tag' | 'favor' | 'export' | 'clear' | 'message' | 'sync' | 'import'
  >(() => {
    if (global) {
      return 'import';
    }
    return 'invite';
  });
  const { data: valid } = useRequest(
    () => {
      return tkshopSystemStatusGet();
    },
    {
      formatResult(res) {
        const _valid = res.data?.tkshopTeam?.version === 'TkshopEnterprise';
        if (!_valid && type === 'import') {
          setType('invite');
        }
        return _valid;
      },
    },
  );

  const groupRegion = useCallback(() => {
    let action = tkshopCreatorGroupByRegionPost;
    if (global) {
      action = tkshopGlobalCreatorGroupByRegionPost;
    }
    return action({
      ...getParams(),
    }).then((res) => {
      const _map: any = {};
      (res?.data || []).forEach(({ region, creators }) => {
        if (creators?.length && region) {
          _map[region] = creators;
        }
      });
      return _map;
    });
  }, [getParams, global]);
  const label = useMemo(() => {
    return getLabelByResourceType(resourceType);
  }, [resourceType]);
  const { refreshFavor } = useTeamFavor(resourceType);
  const hasAuth = useAuthJudgeCallback();

  const tip = useMemo(() => {
    let suffix = (
      <span>
        {I18N.t('您希望对这些{{label}}：', {
          label,
        })}
      </span>
    );
    if (total > BATCH_LIMIT) {
      suffix = (
        <span>
          {I18N.t('您希望对前 {{total}} 个{{label}}：', {
            total: (
              <Typography.Link style={{ margin: '0 4px' }}>
                {BATCH_LIMIT.toLocaleString()}
              </Typography.Link>
            ),
            label,
          })}
        </span>
      );
    }
    return (
      <div>
        {I18N.t('当前条件下，共检索出 {{total}} 个{{label}}，', {
          total: total.toLocaleString(),
          label,
        })}
        {suffix}
      </div>
    );
  }, [label, total]);
  const { run: submit, loading } = useRequest(
    async () => {
      if (type === 'import') {
        setVisible(false);
        GhostModalCaller(
          <ImportGlobalCreatorsConfirmModal
            onSubmit={async (tag) => {
              await tkshopGlobalCreatorImportToTeamPost({
                ...getParams(),
                tags: [tag],
              });
            }}
          />,
        );
      }
      switch (type) {
        case 'invite':
        case 'sync':
        case 'message':
          await groupRegion().then((group) => {
            if (type === 'sync') {
              setVisible(false);
              syncCreators({
                group,
              });
            } else {
              groupRegionCallback({
                group: group,
                onSubmit: async ({ region, creators }) => {
                  setVisible(false);
                  if (type === 'message') {
                    GhostModalCaller(
                      <SendMsgToCreatorSelectedModal
                        region={region}
                        refer={global ? 'global' : 'list'}
                        selected={creators}
                      />,
                    );
                  } else if (type === 'invite') {
                    GhostModalCaller(
                      <InviteSelectedModal
                        refer={global ? 'global' : 'list'}
                        selected={creators}
                        region={region}
                      />,
                    );
                  }
                },
              });
            }
          });

          break;
        case 'export':
          const fn = () => {
            GhostModalCaller(<ExportCreatorsModal global={global} getParams={getParams} />);
          };
          if (total > BATCH_LIMIT) {
            DMConfirm({
              width: 460,
              title: I18N.t('一次最多导出 {{total}} 个{{label}}', {
                total: BATCH_LIMIT.toLocaleString(),
                label,
              }),
              content: I18N.t('请确认是否继续导出前 {{total}} 个{{label}}', {
                total: BATCH_LIMIT.toLocaleString(),
                label,
              }),
              onOk() {
                fn();
              },
            });
          } else {
            fn();
          }
          break;
        case 'clear':
          if (!hasAuth(Functions.TKSHOP_CREATOR_ALLOCATE)) {
            showFunctionCodeAlert();
            return;
          }
          DMConfirm({
            title: `${I18N.t('确定屏蔽搜索出的 {{count}} 个{{label}}吗？', {
              count: Math.min(BATCH_LIMIT, total)?.toLocaleString(),
              label,
            })}`,
            onOk() {
              tkshopCreatorDeleteByQueryPost(
                {
                  force: false,
                },
                getParams(),
              ).then(() => {
                message.success(I18N.t('屏蔽成功'));
                onUpdate();
              });
            },
          });
          break;
        case 'tag': {
          GhostModalCaller(
            <TagOperation
              resourceType={resourceType}
              onAppend={async (tagIds) => {
                await ghTagByQueryPost({
                  query: getParams(),
                  tagIds,
                  scene,
                  tag: true,
                });
                onUpdate();
              }}
              onClear={async () => {
                await ghTagByQueryPost({
                  query: getParams(),
                  scene,
                  tag: false,
                });
                onUpdate();
              }}
            />,
          );
          break;
        }
        case 'favor': {
          GhostModalCaller(
            <CollectOperation
              showLoading
              onLike={async () => {
                setVisible(false);
                await ghFavoriteByQueryPost({
                  favorite: true,
                  query: getParams(),
                  scene,
                });
                refreshFavor();
              }}
              resourceType={resourceType}
              onDislike={async () => {
                setVisible(false);
                await ghFavoriteByQueryPost({
                  favorite: false,
                  query: getParams(),
                  scene,
                });
                refreshFavor();
              }}
            />,
          );
          break;
        }
        default: {
          throw new Error('Not implemented yet: "favor" case');
        }
      }
    },
    {
      manual: true,
    },
  );

  const actions = useMemo(() => {
    if (global) {
      return (
        <Radio.Group
          value={type}
          onChange={(e) => {
            setType(e.target.value);
          }}
        >
          <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
            <Radio value={'invite'}>
              <div>{I18N.t('定向邀约')}</div>
            </Radio>
            <div style={{ display: 'flex', alignItems: 'center', gap: 24 }}>
              <Radio value={'message'}>
                <span>{I18N.t('发送店铺站内消息')}</span>
              </Radio>
              <span style={{ fontSize: '14px' }}>
                <Typography.Text type={'secondary'}>耗时较长，请谨慎使用</Typography.Text>
                <HelpLink href={'/tkshop2/message#other'} style={{ marginLeft: 16 }} />
              </span>
            </div>

            <Tooltip
              placement={'right'}
              title={
                !valid && (
                  <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
                    <div>企业版用户可解锁此功能</div>
                    <div style={{ display: 'flex', gap: 16 }}>
                      <Typography.Link
                        underline
                        style={{ color: 'inherit' }}
                        onClick={() => {
                          GhostModalCaller(<UpgradeModal />, 'UpgradeModal');
                        }}
                      >
                        {I18N.t('立即升级')}
                      </Typography.Link>
                      <Typography.Link
                        underline
                        style={{ color: 'inherit' }}
                        onClick={openCustomService}
                      >
                        {I18N.t('在线客服')}
                      </Typography.Link>
                    </div>
                  </div>
                )
              }
            >
              <Radio value={'import'} disabled={!valid}>
                <div>{I18N.t('批量导入至团队达人库')}</div>
              </Radio>
            </Tooltip>
          </div>
        </Radio.Group>
      );
    }
    return (
      <Radio.Group
        value={type}
        onChange={(e) => {
          setType(e.target.value);
        }}
      >
        <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
          <Radio value={'invite'}>
            <div>{I18N.t('定向邀约')}</div>
          </Radio>
          <Radio value={'message'}>
            <div>{I18N.t('发送消息')}</div>
          </Radio>
          <Radio value={'sync'}>
            <div>{I18N.t('信息更新')}</div>
          </Radio>
          <Radio value={'tag'}>
            <div>{I18N.t('增加/清空标签')}</div>
          </Radio>
          <Radio value={'favor'}>
            <div>{I18N.t('关注/取消关注')}</div>
          </Radio>
          <Radio value={'export'}>
            <div>{I18N.t('批量导出')}</div>
          </Radio>
          <Radio value={'clear'}>
            <div>{I18N.t('批量屏蔽')}</div>
          </Radio>
        </div>
      </Radio.Group>
    );
  }, [global, type, valid]);

  return (
    <DMModal
      open={visible}
      width={640}
      title={I18N.t('全量操作')}
      onOk={submit}
      confirmLoading={loading}
      onCancel={() => {
        setVisible(false);
      }}
    >
      <Alert
        showIcon
        message={I18N.t('全量操作一次最多操作 {{limit}} 个{{label}}', {
          limit: BATCH_LIMIT,
          label,
        })}
      />
      <div style={{ margin: '16px 0' }}>
        <Typography.Text>{tip}</Typography.Text>
      </div>
      <Form form={form}>{actions}</Form>
    </DMModal>
  );
};
export default BatchCreatorOption;
