import I18N from '@/i18n';
import type { ReactNode } from 'react';
import React, { useCallback, useEffect, useMemo } from 'react';
import { Col, List, Row, Space, Tooltip, Typography } from 'antd';
import { history, useModel, useRouteMatch } from 'umi';
import { getUrlOfCurrentTeam } from '@/utils/utils';
import styles from './index.less';
import ColoursIcon from '@/components/Common/ColoursIcon';
import classNames from 'classnames';
import CustomView from '@/components/Common/CustomView';
import { useRequest } from '@@/plugin-request/request';
import {
  viewByViewIdDelete as viewUseDelete,
  viewsByTypeByResourceTypeGet as byTypeUseGet,
} from '@/services/api-ShopAPI/ViewController';
import DMConfirm from '@/components/Common/DMConfirm';
import colors from '@/style/color.less';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { useSortableTransferModal } from '@/components/SortableTransfer';
import Functions from '@/constants/Functions';
import { useAuthJudgeCallback } from '@/components/Common/FunctionButton';
import Slot from '@/components/Slot';
import type { Type } from '@/hooks/useCustomView';
import { useCustomViewPreferenceItem } from '@/hooks/useCustomView';
import { GhostModalCaller } from '@/mixins/modal';
import EditCustomViewModal from '@/components/Common/CustomView/EditCustomViewModal';

export const NavSymbol = Symbol('left-panel-nav');

type ViewItem = {
  id: string;
  name: string;
  icon: string;
  functionCode?: string;
  customMenuItemCon?: boolean;
};

function isDefaultView(viewId: string) {
  return [
    'all',
    'collected',
    'opened',
    'unbound',
    'unfingerprinted',
    'ungranted',
    'unavailable',
    'tpl',
    'ins',
    'tplIns',
    'standIns',
    'expring',
    'expired',
    'flow',
    'schedule',
    'task',
    'voucher',
    'collect',
    'UserDisk',
    'TeamDisk',
    'analyze',
    'pool',
    'windowSync',
    'platform',
    'aptotic',
    'Shop',
    'SocialMedia',
    'dynamic',
  ].includes(viewId);
}

export const DEFAULT_IP_VIEWS = [
  { id: 'all', name: I18N.t('全部IP'), icon: 'IP_24', removable: false },
  {
    id: 'aptotic',
    name: I18N.t('静态IP'),
    icon: 'jingtaiIP_24',
  },
  {
    id: 'dynamic',
    name: I18N.t('动态IP'),
    icon: 'dongtaiIP_24',
  },
  {
    id: 'unbound',
    name: I18N.t('空闲的IP'),
    icon: 'weibangding_24',
  },
  {
    id: 'platform',
    name: I18N.t('购买的平台IP'),
    icon: 'gongyouIP_24',
  },
  {
    id: 'expring',
    name: I18N.t('临期的IP'),
    icon: 'jijiangguoqi_241',
  },
  {
    id: 'unavailable',
    name: I18N.t('已失效的IP'),
    icon: 'yishixiaoIP_24',
  },
  {
    id: 'pool',
    name: I18N.t('IP池'),
    icon: 'IPchi_24',
    removable: false,
  },
];

export const DEFAULT_RPA_VIEWS = [
  {
    id: 'flow',
    name: I18N.t('流程定义'),
    icon: 'liucheng_24',
    removable: false,
    customMenuItemCon: true,
  },
  {
    id: 'schedule',
    name: I18N.t('流程计划'),
    icon: 'jihua_24',
  },
  {
    id: 'task',
    name: I18N.t('流程任务'),
    icon: 'renwu_24',
    functionCode: Functions.RPA_RUN,
  },
  {
    id: 'voucher',
    name: I18N.t('流程任务卡'),
    icon: 'zhifupingtai_24',
  },
  {
    id: 'browserFlow',
    name: I18N.t('内置到浏览器的流程'),
    icon: 'yiwancheng_24',
  },
  {
    id: 'openapi',
    name: I18N.t('Open API'),
    icon: 'API_24',
  },
];

export const DEFAULT_YUNPAN_VIEWS = [
  {
    id: 'UserDisk',
    name: I18N.t('我的云盘'),
    icon: 'renwu_241',
    removable: false,
  },
  {
    id: 'TeamDisk',
    name: I18N.t('团队云盘'),
    icon: 'gongxiang_24',
  },
  {
    id: 'collected',
    name: I18N.t('我的收藏'),
    icon: 'shoucang_24',
  },
  {
    id: 'analyze',
    name: I18N.t('存储空间分析'),
    icon: 'jibenxinxi_24',
  },
];

export function getViewId(viewId) {
  if (isDefaultView(viewId)) {
    return '';
  }
  return viewId;
}
export function getViewCategory(viewId: string) {
  if (isDefaultView(viewId)) {
    return viewId;
  }
  return 'custom';
}
