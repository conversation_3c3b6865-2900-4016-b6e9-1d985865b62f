import I18N from '@/i18n';
import React, { useMemo } from 'react';
import { history } from 'umi';
import { getTeamIdFromUrl } from '@/utils/utils';
import CustomView from '@/components/Common/CustomView';
import { useRequest } from '@@/plugin-request/request';
import { viewByViewIdDelete as viewUseDelete } from '@/services/api-ShopAPI/ViewController';
import { useCustomViewPreferenceItem } from '@/hooks/useCustomView';
import useCurrentRole from '@/hooks/useCurrentRole';
import {
  shopViewConfigGet,
  shopViewConfigPut,
  shopViewsGet,
} from '@/services/api-ShopAPI/ShopController';
import LeftPanelLayout from '@/components/LeftPanelView/components/LeftPanelLayout';
import LayoutViewHeader from '@/components/LeftPanelView/components/LayoutViewHeader';
import type { ViewConfig, ViewItem } from '@/components/LeftPanelView/components/LayoutViewItem';
import {
  CustomLayoutViewItem,
  PresetLayoutViewItem,
} from '@/components/LeftPanelView/components/LayoutViewItem';
import ColoursIcon from '@/components/Common/ColoursIcon';
import _ from 'lodash';

function isDefaultView(viewId: string) {
  return !/^\d+$/.test(viewId);
}
export const DEFAULT_SHOP_VIEWS = [
  {
    id: 'all',
    name: I18N.t('全部分身'),
    icon: 'Chrome_24',
    removable: false,
    description: I18N.t('您有权限访问的所有浏览器分身'),
  },
  {
    id: 'Shop',
    name: I18N.t('电商平台'),
    icon: 'dianpu_24',
    description: I18N.t(
      '您有权限访问的所有平台类别为”电商平台“的浏览器分身，如Amazon/eBay/TikTok小店等',
    ),
  },
  {
    id: 'SocialMedia',
    name: I18N.t('社交媒体'),
    icon: 'shejiaopingtai_24',
    description: I18N.t('您有权限访问的所有平台类别为”社交媒体“的浏览器分身，如抖音/知乎/TikTok等'),
  },
  {
    id: 'unbound',
    name: I18N.t('未绑定IP的分身'),
    icon: 'weibangding_24',
    description: I18N.t('没有绑定IP地址的浏览器分身'),
  },
  {
    id: 'ungranted',
    name: I18N.t('待授权的分身'),
    icon: 'daishouquandianpu_24',
    description: I18N.t('没有明确授权的浏览器分身'),
  },
  {
    id: 'collected',
    name: I18N.t('我收藏的分身'),
    icon: 'shoucang_24',
    description: I18N.t('以您名下收藏的浏览器分身'),
  },
  {
    id: 'windowSync',
    name: I18N.t('窗口同步'),
    icon: 'chuangkoutongbu_24',
    description: I18N.t('可对打开的浏览器分身窗口进行排列布局与群控操作'),
  },
  {
    id: 'recycleBin',
    name: I18N.t('分身回收站'),
    icon: 'huishouzhan_24',
    description: I18N.t('删除的浏览器分身会在回收站中保留7天'),
  },
];

interface Props {
  currentType: string;
  onUpdate?: () => void;
  className?: string;
}

/**
 * 分身布局
 * @param props
 * @constructor
 */
const ShopLeftPanelView: React.FC<Props> = (props) => {
  const { currentType } = props;
  const { data: config, run: fetch } = useRequest(async () => {
    const _config = await shopViewConfigGet({}).then((res) => res.data!);
    const viewVos = await shopViewsGet().then((res) => res.data!);
    return {
      data: {
        ..._config,
        viewVos,
      },
    };
  }, {});

  const { value = {}, set } = useCustomViewPreferenceItem('Shop');
  const { isSpecialRole } = useCurrentRole();
  const showViewKeys = useMemo(() => {
    if (!config) {
      return [];
    }
    if (config?.globalSync) {
      return config.views;
    }

    return value?.sort || [];
  }, [value?.sort, config]);
  const hiddenViewKeys = useMemo(() => {
    // 针对两种角色隐藏视图
    if (!config) {
      return [];
    }
    if (config?.globalSync) {
      return config.hiddenViews;
    }

    if (value?.hidden) {
      return value.hidden;
    }
    if (isSpecialRole) {
      return ['unbound', 'ungranted'];
    }
    return ['unbound', 'ungranted', 'recycleBin'];
  }, [value, isSpecialRole, config]);
  const data = useMemo(() => {
    if (!config) {
      return [];
    }
    const list: ViewItem[] = DEFAULT_SHOP_VIEWS.map((item) => {
      const { name, id } = item;
      return {
        ...item,
        value: id,
        label: name,
      };
    });
    config.viewVos.forEach((item) => {
      const { view } = item;
      if (view) {
        const { name, id } = view;
        // 自定义视图
        list.push({
          ...view,
          value: String(id),
          label: name,
          icon: 'fenzutubiao_24',
        });
      }
    });
    return list;
  }, [config]);
  const viewToShow = useMemo(() => {
    return data
      ?.filter((item) => !hiddenViewKeys.includes(item.value))
      .sort((a, b) => {
        const indexA = showViewKeys.indexOf(a.value);
        const indexB = showViewKeys.indexOf(b.value);
        return indexA === -1 ? 1 : indexA - indexB;
      });
  }, [data, hiddenViewKeys, showViewKeys]);

  const { run: updateConfig } = useRequest(
    async (_config: ViewConfig, viewId?: string) => {
      const { show, hidden, globalSync } = _config;
      if (isSpecialRole) {
        const views = show?.filter((key) => {
          return data.findIndex((i) => i.value === key) !== -1;
        });
        if (viewId) {
          views.push(viewId);
        }
        await shopViewConfigPut({
          globalSync,
          views: _.uniq(views),
          hiddenViews: hidden,
        });
      }
      set({ sort: show, hidden });
      fetch();
    },
    {
      manual: true,
    },
  );

  const viewSortable = useMemo(() => {
    if (!config) {
      return false;
    }
    if (isSpecialRole) {
      return true;
    }
    return !config?.globalSync;
  }, [config, isSpecialRole]);

  return (
    <LeftPanelLayout
      current={currentType}
      header={
        <LayoutViewHeader
          sortable={viewSortable}
          data={data}
          globalSync={isSpecialRole ? config?.globalSync : undefined}
          type={I18N.t('分身')}
          selected={viewToShow?.map((item) => item.value!)}
          onSubmit={async (_config) => {
            await updateConfig(_config);
            if (!_config.show.includes(currentType)) {
              history.push(`/team/${getTeamIdFromUrl()}/shopManage/all`);
            }
          }}
        />
      }
      onSelect={(item) => {
        history.push(`/team/${getTeamIdFromUrl()}/shopManage/${item.value}`);
      }}
      dataSource={viewToShow.map((item) => {
        const _data = {
          ...item,
          icon: <ColoursIcon className={item.icon} />,
        };
        if (isDefaultView(item.value!)) {
          return {
            value: item.value,
            label: <PresetLayoutViewItem key={item.value} data={_data} />,
          };
        }
        return {
          value: item.value,
          label: (
            <CustomLayoutViewItem
              data={_data}
              onDelete={(_item) => {
                return viewUseDelete({ viewId: _item.value as unknown as number }).then(() => {
                  fetch();
                  if (String(_item.value) === String(currentType)) {
                    history.push(`/team/${getTeamIdFromUrl()}/shopManage/all`);
                  }
                });
              }}
              onUpdate={() => {
                fetch();
              }}
            />
          ),
        };
      })}
      extra={
        viewSortable && (
          <CustomView
            callback={async (view) => {
              const { hiddenViews, globalSync } = config!;
              await updateConfig(
                {
                  show: showViewKeys || [],
                  hidden: hiddenViews!,
                  globalSync,
                },
                String(view.id),
              );
              fetch();
            }}
            resourceType={'Shop'}
          />
        )
      }
    >
      {props.children}
    </LeftPanelLayout>
  );
};

export default ShopLeftPanelView;
