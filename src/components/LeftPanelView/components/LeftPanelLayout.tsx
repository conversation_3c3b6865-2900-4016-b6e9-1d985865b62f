import type { CSSProperties, FC, ReactNode } from 'react';
import { useMemo, useState } from 'react';
import { Menu, Typography } from 'antd';
import styles from './style.less';
import classNames from 'classnames';
import Slot from '@/components/Slot';
import type { ViewItem } from '@/components/LeftPanelView/components/LayoutViewItem';
import MiddleSpin from '@/components/Common/MiddleSpin';
import IconFontIcon from '@/components/Common/IconFontIcon';

const NavSymbol = Symbol('left-panel-nav');

/**
 * 布局文件
 * @param props
 * @constructor
 */
const LeftPanelLayout: FC<{
  className?: string;
  current?: string;
  onSelect?: (item: ViewItem) => void;
  dataSource: ViewItem[];
  extra?: ReactNode;
  loading?: boolean;
  header?: ReactNode;
}> = (props) => {
  const { children, className, current, extra, dataSource, loading, onSelect, header } = props;
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const items = useMemo(() => {
    return dataSource.map((item) => {
      if (item.children?.length) {
        return (
          <Menu.SubMenu key={item.value} title={item.label} className={'view-menu-item'}>
            {item.children.map((_item) => {
              const _active = current === _item.value;
              if (_active) {
                setOpenKeys(() => [item.value!]);
              }

              return (
                <Menu.Item
                  key={_item.value}
                  className={classNames('view-menu-item', { active: _active })}
                  onClick={() => {
                    onSelect?.(_item);
                  }}
                >
                  {_item.label}
                </Menu.Item>
              );
            })}
          </Menu.SubMenu>
        );
      }
      const _active = current === item.value;
      return (
        <Menu.Item
          key={item.value}
          className={classNames('view-menu-item', { active: _active })}
          onClick={() => {
            onSelect?.(item);
          }}
        >
          {item.label}
        </Menu.Item>
      );
    });
  }, [current, dataSource, onSelect]);
  return (
    <div className={classNames(styles.leftPanelViewWrapper, className)}>
      <Slot.On id={NavSymbol} />
      <div className={styles.leftPanelViewContainer}>
        <div className={classNames(styles.leftPanel)}>
          <div className={classNames(styles.layoutLeftHeader, { [styles.empty]: !header })}>
            {header}
          </div>
          <div className={styles.viewListContainer}>
            {loading ? (
              <MiddleSpin />
            ) : (
              <>
                <Menu
                  selectable={false}
                  expandIcon={(_props) => {
                    const { isOpen, disabled } = _props;
                    return (
                      <Typography.Link
                        disabled={disabled}
                        style={{ flex: '0 0 28px', textAlign: 'center' }}
                      >
                        <IconFontIcon
                          size={12}
                          iconName={isOpen ? 'angle-down_24' : 'angle-right_24'}
                        />
                      </Typography.Link>
                    );
                  }}
                  mode={'inline'}
                  openKeys={openKeys}
                  onOpenChange={(keys) => {
                    setOpenKeys(keys as unknown as string[]);
                  }}
                  className={classNames(styles.viewList)}
                >
                  {items}
                </Menu>
                {extra}
              </>
            )}
          </div>
        </div>
        <div className={styles.rightPanel}>{children}</div>
      </div>
    </div>
  );
};
export const LeftPanelLayoutNav = (props: { children: ReactNode; style?: CSSProperties }) => {
  const { children, style } = props;
  const child = useMemo(() => {
    if (children) {
      return (
        <div
          key={'LeftPanelLayoutNav'}
          style={style}
          className={classNames('layout-top-nav', styles.layoutTopNav)}
        >
          {children}
        </div>
      );
    }
    return null;
  }, [children, style]);
  return <Slot.Emit id={NavSymbol}>{child}</Slot.Emit>;
};
export default LeftPanelLayout;
