@import '/src/style/space';
@import '/src/style/color';
.left-panel-view-wrapper {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  height: 100%;
  padding-right: 0;
  overflow: hidden;
}
.left-panel-view-container {
  display: flex;
  flex: 1;
  align-content: stretch;
  align-items: stretch;
  overflow: hidden;
}
.layout-top-nav {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  height: 57px;
  padding: 0 12px;
  border-bottom: 1px solid @border-color;
  > div {
    flex: 1;
    overflow: hidden;
  }
  :global {
    .ant-select {
      width: 130px;
    }
    .ant-form {
      padding-right: 0 !important;
    }
  }
}

.left-panel {
  display: flex;
  flex: 0 0 200px;
  flex-direction: column;
  overflow: hidden;
  border-right: 1px solid @border-color;
}
.viewListContainer {
  flex: 1;
  overflow: auto;
}
.view-list {
  display: flex;
  flex-direction: column;
  padding: 0 12px;
  overflow: hidden;
  :global {
    .ant-menu-item {
      height: auto !important;
    }
    .view-menu-item,
    .ant-menu-submenu-title {
      margin: 0 !important;
      padding: 0 !important;
      border-radius: 5px;
      cursor: pointer;
      &:after {
        display: none;
      }
      .ant-menu-title-content,
      .ant-menu-submenu-title {
        display: flex;
        align-items: center;
        height: 46px;
      }
      .ant-menu-title-content {
        padding: 0 8px !important;
        border-bottom: 1px solid #f0f0f0;
      }
      &.active {
        color: inherit;
        background: @active-background;
      }
    }
    .ant-menu-submenu-title {
      .ant-menu-submenu-arrow {
        color: @primary-color;
      }
    }
    .ant-menu-sub {
      background: inherit;
      .ant-menu-title-content {
        padding-left: 24px !important;
      }
    }
  }
}
.layout-left-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 45px;
  padding: 0 16px;
  &.empty {
    height: 12px;
  }
}

.right-panel {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  overflow: hidden;
}

.layout-view-item {
  display: flex;
  flex: 1;
  flex-wrap: nowrap;
  gap: 8px;
  align-items: center;
  height: 100%;
  overflow: hidden;
  cursor: pointer;
  :global {
    .actions {
      display: none;
      gap: 4px;
      justify-content: flex-end;
      color: @primary-color;
    }
  }
  &:hover {
    :global {
      .actions {
        display: inline-flex;
      }
    }
  }
}
