import I18N from '@/i18n';
import type { ReactNode } from 'react';
import React, { useCallback } from 'react';
import { Space, Tooltip } from 'antd';
import styles from './style.less';
import IconFontIcon from '@/components/Common/IconFontIcon';
import DMConfirm from '@/components/Common/DMConfirm';

import useCurrentUser from '@/hooks/useCurrentUser';
import { GhostModalCaller } from '@/mixins/modal';
import EditCustomViewModal from '@/components/Common/CustomView/EditCustomViewModal';
import _ from 'lodash';

export type ViewItem = {
  value?: string;
  label: ReactNode;
  description?: string;
  children?: ViewItem[];
  disabled?: boolean;
  sortable?: boolean;
  removable?: boolean;
  hidden?: boolean;
  [key: string]: any;
};
export type ViewConfig = {
  show: string[];
  hidden: string[];
  globalSync?: boolean;
};

export const PresetLayoutViewItem = (props: {
  data: ViewItem & { icon: any };
  iconSize?: number;
}) => {
  const { data, iconSize = 16 } = props;
  const { label, icon, description } = data;

  return (
    <div className={styles.layoutViewItem}>
      {icon && (
        <div style={{ textAlign: 'center', flex: '0 0 18px' }}>
          {React.cloneElement(icon, {
            size: iconSize,
          })}
        </div>
      )}
      <span style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', flex: 1 }}>
        {label}
      </span>
      {description && (
        <Space wrap={false} className="actions">
          <Tooltip title={description} placement={'right'}>
            <IconFontIcon iconName={'bangzhu_24'} />
          </Tooltip>
        </Space>
      )}
    </div>
  );
};

export const CustomLayoutViewItem = (props: {
  data: ViewItem;
  onUpdate: () => void;
  onDelete?: (item: ViewItem) => Promise<void>;
}) => {
  const { data, onUpdate, onDelete } = props;
  const { creatorName, label, creator, icon, editable } = data;
  const user = useCurrentUser();
  const createdBySelf = creator === user?.id;
  const viewEditable = editable || createdBySelf;
  const openCustomViewEdit = useCallback(() => {
    GhostModalCaller(<EditCustomViewModal onUpdate={onUpdate} data={data} />);
  }, [data, onUpdate]);

  return (
    <div className={styles.layoutViewItem}>
      {icon && (
        <div style={{ textAlign: 'center', flex: '0 0 18px' }}>
          {React.cloneElement(icon, {
            size: 16,
          })}
        </div>
      )}
      <span style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', flex: 1 }}>
        {label}
      </span>
      <Space wrap={false} className="actions">
        {viewEditable && (
          <IconFontIcon
            iconName="edit_24"
            onClick={(e) => {
              openCustomViewEdit();
              e.stopPropagation();
            }}
          />
        )}

        {viewEditable && (
          <IconFontIcon
            iconName="Trash_24"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              DMConfirm({
                title: I18N.t(`${I18N.t('您确定要删除“')}${label}${I18N.t('”视图吗？')}`),
                onOk() {
                  onDelete?.(data);
                },
              });
            }}
          />
        )}
        <Tooltip
          placement={'right'}
          title={
            createdBySelf
              ? I18N.t('您本人创建的视图')
              : I18N.t('由＂{{name}}＂创建并分享给您', {
                  name: _.truncate(creatorName, {
                    length: 10,
                  }),
                })
          }
        >
          <IconFontIcon iconName={'bangzhu_24'} />
        </Tooltip>
      </Space>
    </div>
  );
};
