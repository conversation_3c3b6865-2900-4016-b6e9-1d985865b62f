@import '/src/style/color';
.sortable-transfer-modal {
  :global {
    .ant-modal-body {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
    .sortable-transfer {
      height: 400px;
    }
  }
}
.sortable-helper {
  z-index: 999999;
}
.sortable-transfer {
  display: flex;
  align-content: stretch;
  align-items: stretch;
  width: 100%;
  overflow: hidden;
  user-select: none;
  :global {
    .disabled {
      //background-color: #f5f5f5;
      cursor: not-allowed;
    }
    .hidden {
      display: none;
    }
    .active {
      background-color: @active-background;
    }
  }
}
.list-title {
  display: flex;
  align-items: center;
  padding-left: 10px;
  line-height: 40px;
  background-color: #f6f6f6;
  .filter-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    margin-left: auto;
    cursor: pointer;
    &.active {
      color: @primary-color;
    }
  }
}
.list-item {
  display: flex;
  gap: 8px;
  align-items: center;
  width: 100%;
  height: 35px;
  padding: 0 10px;
  overflow: hidden;
  cursor: pointer;
}
.list-wrapper {
  position: relative;
  flex: 1;
  overflow: auto;
}
.left,
.right {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid @border-color;
}

.middle {
  display: flex;
  flex-direction: column;
  gap: 30px;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
}
