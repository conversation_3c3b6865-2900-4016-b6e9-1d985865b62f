import I18N from '@/i18n';
import type { FC, Key } from 'react';
import { useCallback, useMemo, useState } from 'react';
import { SortableContainer, SortableElement, SortableHandle } from 'react-sortable-hoc';
import styles from './index.less';
import { Button, Col, Form, Row, Switch, Tooltip, Typography } from 'antd';
import classNames from 'classnames';
import { useMount } from 'ahooks';
import DMModal from '@/components/Common/Modal/DMModal';
import IconFontIcon from '@/components/Common/IconFontIcon';
import useCurrentRole from '@/hooks/useCurrentRole';
import _ from 'lodash';
import ColoursIcon from '@/components/Common/ColoursIcon';
import Placeholder from '@/components/Common/Placeholder';
import DMConfirm from '@/components/Common/DMConfirm';
import { useRequest } from '@@/plugin-request/request';
import type { ViewItem } from '@/components/LeftPanelView/components/LayoutViewItem';
import type { ViewConfig } from '@/components/LeftPanelView/components/LayoutViewItem';

interface Props {
  data: ViewItem[];
  /**
   * 带顺序的
   */
  selected: Key[];
  onSubmit: (data: ViewConfig) => Promise<void>;
  globalSync?: boolean;
}
const SortableRow = (props: {
  children: any;
  disabled: any;
  sortable: boolean;
  removable: boolean;
  active: any;
}) => {
  const { children, disabled, sortable = true, removable = true, active } = props;
  const node = (
    <Tooltip title={disabled || !sortable ? undefined : I18N.t('可拖动改变位置')}>
      <Row className={classNames(styles.listItem, { active, disabled })}>
        <Col style={{ flex: 1, overflow: 'hidden' }}>{children}</Col>
        <Col className="handler">
          {disabled ? (
            <Placeholder style={{ fontSize: 12 }}>{I18N.t('（不可移除，不可移动）')}</Placeholder>
          ) : (
            <>
              {!sortable && (
                <Placeholder style={{ fontSize: 12 }}>{I18N.t('（不可移动）')}</Placeholder>
              )}
              {!removable && (
                <Placeholder style={{ fontSize: 12 }}>{I18N.t('（不可移除）')}</Placeholder>
              )}
              {sortable && <IconFontIcon iconName={'menu'} />}
            </>
          )}
        </Col>
      </Row>
    </Tooltip>
  );

  if (disabled) {
    return node;
  }
  const Node = SortableHandle(() => node);
  return <Node />;
};
const SortableItem = SortableElement(
  (props: {
    data: ViewItem;
    active?: boolean;
    onSelect: (item: ViewItem) => void;
    renderItem: (item: ViewItem) => string | JSX.Element;
    onDoubleClick: (item: ViewItem) => void;
  }) => {
    const { active, data, onSelect, renderItem, onDoubleClick } = props;
    const { disabled, sortable = true, removable = true } = data;
    return (
      <div
        onClick={() => {
          if (!disabled && removable) {
            onSelect(data);
          }
        }}
        onDoubleClick={() => {
          if (!disabled && removable) {
            onDoubleClick(data);
          }
        }}
      >
        <SortableRow disabled={disabled} sortable={sortable} removable={removable} active={active}>
          {renderItem(data)}
        </SortableRow>
      </div>
    );
  },
);

const SortableList = SortableContainer(
  (props: {
    renderItem: any;
    data: ViewItem[];
    rowKey: string;
    activeKey: string | number;
    onSelect: (item: ViewItem) => void;
    onDoubleClick: (item: ViewItem) => void;
  }) => {
    const { renderItem, data, rowKey, activeKey, onSelect, onDoubleClick } = props;
    return (
      <div className={styles.listWrapper}>
        {data
          .filter((item) => !item.hidden)
          .map((item, index) => (
            <SortableItem
              key={`item-${item[rowKey]}`}
              active={activeKey === item[rowKey]}
              index={index}
              data={item}
              renderItem={renderItem}
              onSelect={onSelect}
              onDoubleClick={onDoubleClick}
            />
          ))}
      </div>
    );
  },
);

/**
 * @param props
 * @constructor
 */
const ViewConfigTransfer: FC<Props> = (props) => {
  const [visible, changeVisible] = useState(true);
  const { data, selected, globalSync: _globalSync, onSubmit } = props;
  const rowKey = 'value';
  const renderItem = useCallback((item) => {
    const { icon, label } = item;
    return (
      <Row wrap={false} gutter={8} className={styles.itemMain}>
        <Col style={{ minWidth: 21 }}>
          <ColoursIcon className={icon} />
        </Col>
        <Col style={{ flex: 1 }}>
          <Typography.Text ellipsis>{label}</Typography.Text>
        </Col>
      </Row>
    );
  }, []);
  const { isSpecialRole } = useCurrentRole();
  const [globalSync, setGlobalSync] = useState(_globalSync || false);
  const [selectedRows, updateRows] = useState<ViewItem[]>([]);
  const rest = useMemo<ViewItem[]>(() => {
    return data.filter((item) => {
      return (
        _.findIndex(selectedRows, (i) => {
          return i[rowKey] === item[rowKey];
        }) === -1
      );
    });
  }, [data, rowKey, selectedRows]);
  const { run: updateConfig, loading: submitting } = useRequest(
    async () => {
      const show = selectedRows.map((item) => item[rowKey]!);
      const hidden = rest.map((item) => item[rowKey]!);
      await onSubmit({
        globalSync,
        show,
        hidden,
      });
      changeVisible(false);
    },
    {
      manual: true,
    },
  );

  const [currentItem, setCurrentItem] = useState<ViewItem>();

  const autoSelectPrev = useCallback((arr, i) => {
    let index = i;
    if (arr.length === 0) {
      setCurrentItem(undefined);
    } else {
      while ((!arr[index] || arr[index]?.disabled) && index >= 0) {
        index--;
      }
      const target = arr[index];
      if (!target?.disabled) {
        setCurrentItem(target);
      } else {
        setCurrentItem(undefined);
      }
    }
  }, []);
  const toLeft = useCallback(
    (target = currentItem) => {
      setCurrentItem(target);
      // 选中
      if (target) {
        updateRows((rows) => {
          const list = [...rows];
          const index = rest.findIndex((item) => item[rowKey] === target[rowKey]);
          if (list[list.length - 1]?.disabled) {
            // 最后一个不准动的话
            list.splice(list.length - 1, 0, target);
          } else {
            list.push(target);
          }
          const arr = [...rest];
          arr.splice(index, 1);
          autoSelectPrev(arr, index);
          return list;
        });
      }
    },
    [autoSelectPrev, currentItem, rest, rowKey],
  );
  const toRight = useCallback(
    (target = currentItem) => {
      setCurrentItem(target);
      // 取消选中
      if (target?.[rowKey]) {
        updateRows((rows) => {
          const list = [...rows];
          const index = list.findIndex((item) => item[rowKey] === target[rowKey]);
          if (index !== -1) {
            list.splice(index, 1);
          }
          autoSelectPrev(list, index);
          return list;
        });
      }
    },
    [autoSelectPrev, currentItem, rowKey],
  );
  /**
   * 双击快速排序
   */
  const quickTransfer = useCallback(
    (item, direction: 'left' | 'right') => {
      if (direction === 'left') {
        toLeft(item);
      } else {
        toRight(item);
      }
    },
    [toLeft, toRight],
  );
  useMount(() => {
    const list: ViewItem[] = [];
    selected.forEach((key) => {
      const target = data.find((item) => item[rowKey] === key);
      if (target) {
        list.push(target);
      }
    });
    const target = selectedRows.find(
      (item) => !item.hidden && !item.disabled && item.removable !== false,
    );
    if (target) {
      setCurrentItem(target);
    } else {
      setCurrentItem(undefined);
    }
    updateRows(list);
  });

  const restItems = useMemo(() => {
    return rest.map((item) => {
      const { disabled } = item;
      return (
        <div
          className={classNames(styles.listItem, {
            active: currentItem?.[rowKey] === item[rowKey],
            disabled,
          })}
          onClick={() => {
            if (!disabled) {
              setCurrentItem(item);
            }
          }}
          onDoubleClick={() => {
            if (!disabled) {
              quickTransfer(item, 'left');
            }
          }}
          key={item[rowKey]}
        >
          {renderItem(item)}
        </div>
      );
    });
    // 必须监听selectedRows的变化
  }, [currentItem, quickTransfer, renderItem, rest, rowKey, selectedRows]);
  const selectedItems = useMemo(() => {
    return (
      <SortableList
        lockAxis={'y'}
        distance={5}
        lockToContainerEdges
        useDragHandle
        helperClass={styles.sortableHelper}
        rowKey={rowKey}
        activeKey={currentItem?.[rowKey]}
        onSortEnd={(sort) => {
          const { oldIndex, newIndex } = sort;
          updateRows((rows) => {
            const list = [...rows];
            if (!list[oldIndex].disabled && !list[newIndex].disabled) {
              if (oldIndex > newIndex) {
                list.splice(newIndex, 0, list[oldIndex]);
                list.splice(oldIndex + 1, 1);
              } else {
                list.splice(newIndex + 1, 0, list[oldIndex]);
                list.splice(oldIndex, 1);
              }
            }
            return list;
          });
        }}
        onSelect={setCurrentItem}
        onDoubleClick={(item) => {
          quickTransfer(item, 'right');
        }}
        data={selectedRows}
        renderItem={renderItem}
      />
    );
  }, [currentItem, quickTransfer, renderItem, rowKey, selectedRows]);
  const isCurrentSelected =
    currentItem && !!selectedRows.find((item) => item[rowKey] === currentItem?.[rowKey]);
  const isCurrentCancelable = currentItem && currentItem.removable !== false;
  return (
    <DMModal
      title={I18N.t(`${I18N.t('分身视图排序调整')}`)}
      className={styles.sortableTransferModal}
      width={740}
      onOk={updateConfig}
      confirmLoading={submitting}
      visible={visible}
      onCancel={() => {
        changeVisible(false);
      }}
    >
      <div className={classNames(styles.sortableTransfer, 'sortable-transfer')}>
        <div className={styles.left}>
          <div className={styles.listTitle}>{I18N.t('当前显示视图')}</div>
          {selectedItems}
        </div>
        <div className={styles.middle}>
          <Button
            type={isCurrentSelected ? 'default' : 'primary'}
            disabled={!currentItem || isCurrentSelected}
            onClick={() => {
              toLeft();
            }}
          >
            <IconFontIcon iconName="angle-left_24" />
          </Button>
          <Button
            type={isCurrentSelected ? 'primary' : 'default'}
            disabled={!currentItem || !isCurrentSelected || !isCurrentCancelable}
            onClick={() => {
              toRight();
            }}
          >
            <IconFontIcon iconName="angle-right_24" />
          </Button>
        </div>
        <div className={styles.right}>
          <div className={styles.listTitle}>{I18N.t('待显示视图')}</div>
          <div className={styles.listWrapper}>{restItems}</div>
        </div>
      </div>
      {_globalSync !== undefined && (
        <Form.Item
          hidden={!isSpecialRole}
          style={{ marginBottom: 0 }}
          label={I18N.t('团队成员视图同步')}
          tooltip={I18N.t(
            '一旦开启此选项，意味着当前团队所有成员都会采用相同的视图设置（只有超管与BOSS才能够开启/关闭此选项）',
          )}
        >
          <Switch
            checked={globalSync}
            onChange={(val) => {
              setGlobalSync(() => val);
              if (isSpecialRole && val) {
                DMConfirm({
                  width: 540,
                  title: I18N.t('确定要开启同步视图的选项吗？'),
                  content: I18N.t(
                    '一旦开启意味着当前是团队其它成员的分身视图都会以当前的视图设置为准，请确认是否继续',
                  ),
                  onOk() {
                    updateConfig();
                  },
                  onCancel: () => {
                    setGlobalSync(() => !val);
                  },
                });
              }
            }}
          />
        </Form.Item>
      )}
    </DMModal>
  );
};

export default ViewConfigTransfer;
