import { Col, Row, Tooltip, Typography } from 'antd';
import I18N from '@/i18n';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { useCallback } from 'react';
import { GhostModalCaller } from '@/mixins/modal';
import ViewConfigTransfer from '@/components/LeftPanelView/components/ViewConfigTransfer.tsx';
import type { ViewItem } from '@/components/LeftPanelView/components/LayoutViewItem';
import type { ViewConfig } from '@/components/LeftPanelView/components/LayoutViewItem';

const LayoutViewHeader = (props: {
  data: ViewItem[];
  selected?: string[];
  onSubmit?: (data: ViewConfig) => Promise<void>;
  /**
   * 视图title名称
   */
  type: any;
  sortable?: boolean;
  globalSync?: boolean;
}) => {
  const { data, selected, onSubmit, type, sortable, globalSync } = props;
  const openSortModal = useCallback(() => {
    GhostModalCaller(
      <ViewConfigTransfer
        globalSync={globalSync}
        data={data}
        onSubmit={onSubmit}
        selected={selected || []}
      />,
    );
  }, [data, globalSync, onSubmit, selected]);
  return (
    <Row style={{ flex: 1, overflow: 'hidden' }} align={'middle'}>
      <Col flex={1}>
        <div>
          {type}
          {I18N.t('视图')}
        </div>
      </Col>
      {sortable && (
        <Col>
          <Tooltip
            title={`${I18N.t('调整{{type}}视图的显示顺序', {
              type,
            })}`}
          >
            <Typography.Link>
              <IconFontIcon
                iconName="paixu-1_24"
                onClick={() => {
                  openSortModal();
                }}
              />
            </Typography.Link>
          </Tooltip>
        </Col>
      )}
    </Row>
  );
};
export default LayoutViewHeader;
