@import '/src/style/space';
@import '/src/style/color';

.left-panel-view-wrapper {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  height: 100%;
  padding-right: 0;
  overflow: hidden;
}
.left-panel-view-container {
  display: flex;
  flex: 1;
  align-content: stretch;
  align-items: stretch;
  overflow: hidden;
}
.top-nav {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  height: 57px;
  padding: 0 12px;
  border-bottom: 1px solid @border-color;
  > div {
    flex: 1;
    overflow: hidden;
  }
  :global {
    .ant-select {
      width: 130px;
    }
    .ant-form {
      padding-right: 0 !important;
    }
  }
}

.left-panel {
  display: flex;
  flex: 0 0 200px;
  flex-direction: column;
  overflow: hidden;
  border-right: 1px solid @border-color;
  :global {
    .ant-spin-nested-loading {
      flex: 1;
      overflow: auto;
    }
    .ant-list {
      .ant-list-header {
        margin: 0 @layout-margin;
        color: #878787;
      }

      .ant-list-items {
        padding: 0 12px;
      }
    }
    .ant-list-item {
      flex-wrap: wrap;
      padding: 0;
    }
    .ant-list-header,
    .ant-list-item {
      border: none;
    }
  }
}
.current {
  .view-item-row {
    color: @primary-color;
    background: @active-background;
    border-radius: 5px;
  }
}
.view-list {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.item-main {
  width: 100%;
  overflow: hidden;
}
.view-item-row {
  flex: 1;
  padding: 12px 8px;
  overflow: hidden;
  cursor: pointer;
  :global {
    .ant-col {
      display: inline-flex;
      align-items: center;
      overflow: hidden;
    }
    .actions {
      display: none;
      flex: 0 0 36px;
      gap: 0;
      justify-content: space-between;
      color: @primary-color;
    }
  }
  &:hover {
    :global {
      .actions {
        display: flex;
      }
    }
  }
}
.right-panel {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  overflow: hidden;
}
