import I18N from '@/i18n';
import { openOfficialSiteByAppWindow } from '@/utils/pageUtils';
import type { AnchorHTMLAttributes, FC } from 'react';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { useMemo } from 'react';

function getHelpLinkHref(href?: string) {
  let url = href || '';
  const prefix = '/help';
  url = url.replace('//', '/');
  if (url.startsWith(prefix)) {
    return url;
  }
  if (!url.startsWith('/')) {
    url = `/${url}`;
  }
  return `${prefix}${url}`;
}
export function openHelpLink(link?: string) {
  openOfficialSiteByAppWindow(getHelpLinkHref(link));
}

const HelpLink: FC<AnchorHTMLAttributes<HTMLAnchorElement> & { showIcon?: boolean }> = (props) => {
  const { href, style = {}, children = I18N.t('了解更多'), showIcon, ...others } = props;
  const styleObj = useMemo(() => {
    return {
      fontSize: 'inherit',
      ...style,
    };
  }, [style]);
  return (
    <a
      style={styleObj}
      {...others}
      target="_blank"
      onClick={(e) => {
        e.preventDefault();
        openHelpLink(href);
      }}
    >
      {showIcon && <IconFontIcon style={{ marginRight: 4 }} iconName="bangzhu_241" />}
      {children}
    </a>
  );
};
export default HelpLink;
