import React from 'react';
import SessionStatus from './SessionStatus';
import TeamsDropdown from './TeamsDropdown';
import CustomService from '@/components/CustomService';

const GlobalHeaderRight: React.FC = () => {
  return (
    <div style={{ overflow: 'hidden', display: 'flex', gap: 16, alignItems: 'center' }}>
      <TeamsDropdown />
      <SessionStatus />
      <CustomService />
    </div>
  );
};
export default GlobalHeaderRight;
