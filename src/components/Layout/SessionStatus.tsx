import I18N from '@/i18n';
import { Button, Dropdown } from 'antd';
import UserAvatarAndName from '@/components/Common/UserAvatarAndName';
import DMConfirm from '@/components/Common/DMConfirm';
import { accountLogoutGet } from '@/services/api-Account/LoginController';
import IconFontIcon from '@/components/Common/IconFontIcon';
import useCurrentUser, { clearCurrentUser } from '@/hooks/useCurrentUser';
import {
  openPureClient,
  redirectToHome,
  redirectToLogin,
  redirectToRegister,
} from '@/utils/pageUtils';

import { getJwt } from '@/utils/utils';

export const logoutConfirm = () => {
  DMConfirm({
    title: I18N.t('确认退出？'),
    content: I18N.t('请牢记您的登录密码，并建议绑定手机号，以备不时之需'),
    okText: I18N.t('确认'),
    cancelText: I18N.t('取消'),
    onOk: async () => {
      try {
        await accountLogoutGet({
          ignoreTeamId: true,
        });
        clearCurrentUser();
        redirectToHome();
      } catch (e) {
        console.error(e);
      }
    },
  });
};

/**
 * 用户登录状态
 * @constructor
 */
const SessionStatus = () => {
  const user = useCurrentUser();
  if (user) {
    return (
      <Dropdown
        trigger={['click']}
        menu={{
          onClick({ key }) {
            if (key === 'huayoung') {
              const obj = { jwt: getJwt() };
              const queryStr = new URLSearchParams(obj).toString();
              openPureClient(`?${queryStr}`);
            } else {
              logoutConfirm();
            }
          },
          items: [
            {
              key: 'huayoung',
              label: I18N.t('唤醒花漾客户端'),
            },
            {
              key: 'logout',
              label: I18N.t('退出登录'),
            },
          ],
        }}
      >
        <div
          style={{
            maxWidth: 130,
            overflow: 'hidden',
            cursor: 'pointer',
            display: 'inline-flex',
            gap: 8,
            alignItems: 'center',
            flexWrap: 'nowrap',
          }}
        >
          <UserAvatarAndName user={user} />
          <IconFontIcon size={12} iconName={'angle-down_24'} />
        </div>
      </Dropdown>
    );
  }
  return null;
  return (
    <>
      <Button
        size={'small'}
        onClick={() => {
          redirectToLogin();
        }}
      >
        {I18N.t('登录')}
      </Button>
      <Button
        type={'primary'}
        size={'small'}
        onClick={() => {
          redirectToRegister();
        }}
      >
        {I18N.t('注册')}
      </Button>
    </>
  );
};
export default SessionStatus;
