import I18N from '@/i18n';
import { Dropdown, Menu, Space } from 'antd';
import IconFontIcon from '@/components/Common/IconFontIcon';
import classNames from 'classnames';
import styles from './style.less';
import { useRequest } from '@@/plugin-request/request';
import { ghSettingsKolConfigGet } from '@/services/api-TKGHAPI/GhSettingsController';
import ColoursIcon from '@/components/Common/ColoursIcon';
import { useRouteMatch } from 'umi';
import { userByUserIdGrantedFunctionsGet } from '@/services/api-Account/UserController';
import { SkipErrorNotifyOption } from '@/utils/utils';
import Functions from '@/constants/Functions';
import useCurrentUser from '@/hooks/useCurrentUser';

export const ModuleMap = {
  tiktok: 'TikTok',
  kakao: 'Kakao',
  xhs: 'Xiaohongshu',
  ins: 'Ins',
};

function getIcon(item: string) {
  let icon = `${item}_24`;
  if (item === 'Ins') {
    icon = 'Instagram_24';
  }
  return icon;
}
function getLabel(item: string) {
  if (item === 'tiktok' || item === 'TikTok') {
    return 'TikTok';
  }
  if (item === 'Xiaohongshu' || item === 'xhs') {
    return I18N.t('小红书');
  }
  if (item === 'Kakao' || item === 'kakao') {
    return 'Kakao';
  }
  if (item === 'ins' || item === 'Ins') {
    return 'Instagram';
  }
  return item;
}
const ModuleDropdown = () => {
  const { params } = useRouteMatch(['/team/:teamId/:module']) || {};
  const { module } = params || { module: '' };

  const user = useCurrentUser();
  const { data: modules, loading } = useRequest(
    () => {
      return ghSettingsKolConfigGet();
    },
    {
      formatResult(res) {
        return res.data?.platformTypes?.map((item) => {
          return {
            label: (
              <Space>
                <span style={{ flex: '0 0 14px' }}>
                  <ColoursIcon className={getIcon(item)} />
                </span>
                {getLabel(item) + I18N.t('模块')}
              </Space>
            ),

            key: item.toLowerCase(),
          };
        });
      },
    },
  );
  if (!modules?.length || loading || !module) {
    return <></>;
  }

  return (
    <Dropdown
      placement={'bottomRight'}
      arrow
      align={{
        offset: [15, -10],
      }}
      dropdownRender={() => {
        return (
          <div
            style={{ padding: 0, width: 240, overflow: 'hidden' }}
            className={classNames(
              'ant-dropdown-menu ant-dropdown-menu-root ant-dropdown-menu-vertical ant-dropdown-menu-light',
              styles.teamsDropdown,
            )}
          >
            <Menu
              selectable
              items={modules}
              style={{
                maxHeight: '460px',
                overflowY: 'auto',
                overflowX: 'hidden',
                boxShadow: 'none',
              }}
              onSelect={async (item) => {
                const { key } = item;
                if (params.module === key) {
                  return;
                }
                const meta = await userByUserIdGrantedFunctionsGet(
                  { userId: user?.id },
                  SkipErrorNotifyOption,
                ).then((res) => {
                  return res.data?.filter(Boolean).map((item) => item.id!);
                });
                if (key === 'tiktok') {
                  if (!meta?.includes(Functions.KOL_LIST)) {
                    location.href = `/team/${params.teamId}/tiktok/mine/mobile`;
                  } else {
                    location.href = `/team/${params.teamId}/tiktok/store/live`;
                  }
                } else if (key === 'xiaohongshu') {
                  location.href = `/team/${params.teamId}/xhs/SocialMedia`;
                } else if (key === 'ins') {
                  if (!meta?.includes(Functions.KOL_LIST)) {
                    location.href = `/team/${params.teamId}/ins/mine`;
                  } else {
                    location.href = `/team/${params.teamId}/ins/store`;
                  }
                }
              }}
              selectedKeys={[params.module]}
            />
          </div>
        );
      }}
      trigger={['click']}
    >
      <div
        style={{
          overflow: 'hidden',
          cursor: 'pointer',
          display: 'inline-flex',
          gap: 8,
          alignItems: 'center',
          flexWrap: 'nowrap',
        }}
      >
        <ColoursIcon size={24} className={getIcon(ModuleMap[module])} />
        {getLabel(module) + I18N.t('模块')}
        <IconFontIcon size={12} iconName={'angle-down_24'} />
      </div>
    </Dropdown>
  );
};
export default ModuleDropdown;
