import I18N from '@/i18n';
import useTeams from '@/hooks/useTeams';
import { Col, Divider, Dropdown, Menu, Row, Typography } from 'antd';
import { TeamNamePlate } from '@/components/Common/TeamAvatar';
import useCurrentTeam, { clearCurrentTeam, fetchCurrentTeam } from '@/hooks/useCurrentTeam';
import { redirectToCombo, redirectToHome } from '@/utils/pageUtils';
import React, { useCallback } from 'react';
import { DMLoading } from '@/components/Common/DMConfirm';
import IconFontIcon from '@/components/Common/IconFontIcon';
import pMinDelay from 'p-min-delay';
import dmConfirmStyles from '@/components/Common/DMConfirm.less';
import classNames from 'classnames';
import styles from './style.less';
import useCurrentUser from '@/hooks/useCurrentUser';
import type { ItemType } from 'antd/lib/menu/hooks/useItems';
import _ from 'lodash';
import { useLocation } from 'umi';

interface Props {
  extraMenuItems?: ItemType[];
  force?: boolean;
}

const TeamsDropdown: React.FC<Props> = (props) => {
  const { force, extraMenuItems } = props;
  const teams = useTeams();
  const user = useCurrentUser();
  const currentTeam = useCurrentTeam();
  const { pathname } = useLocation();
  /**
   * 切换teamId
   * @param id
   */
  const switchTeam = useCallback(
    async (id: number) => {
      if (currentTeam?.id !== id || force) {
        const loading = DMLoading({
          className: dmConfirmStyles.pureLoading,
          title: I18N.t('正在为您切换团队，请稍后'),
          style: {
            width: 'auto',
          },
          width: 'auto',
        });
        await pMinDelay(fetchCurrentTeam(id), 500);
        loading.destroy();
        redirectToHome();
      }
    },
    [currentTeam?.id, force],
  );
  if (teams?.length && user) {
    let items: ItemType[] = _.orderBy(teams, [
      (item) => {
        return item.status === 'Ready' ? -1 : 1;
      },
      (item) => {
        return new Date(item.createTime!).getTime();
      },
    ]).map((item) => {
      return {
        key: item.id!,
        onClick() {
          switchTeam(item.id!);
        },
        label: (
          <Row gutter={[8, 8]} style={{ overflow: 'hidden' }} wrap={false}>
            <Col flex={1} style={{ overflow: 'hidden' }}>
              <TeamNamePlate team={item} />
            </Col>
            {item.status === 'Blocked' && (
              <Col style={{ whiteSpace: 'nowrap' }}>
                <Typography.Text type={'danger'}>{I18N.t('已过期')}</Typography.Text>
              </Col>
            )}
          </Row>
        ),
      };
    });
    if (extraMenuItems) {
      items = items.concat(extraMenuItems);
    }
    const button = currentTeam ? (
      <TeamNamePlate style={{ cursor: 'pointer', overflow: 'hidden' }} team={currentTeam!} />
    ) : (
      I18N.t('切换团队')
    );

    return (
      <Dropdown
        placement={'bottomRight'}
        arrow
        align={{
          offset: [15, -10],
        }}
        dropdownRender={() => {
          return (
            <div
              style={{ padding: 0, width: 240, overflow: 'hidden' }}
              className={classNames(
                'ant-dropdown-menu ant-dropdown-menu-root ant-dropdown-menu-vertical ant-dropdown-menu-light',
                styles.teamsDropdown,
              )}
            >
              <Menu
                items={items}
                style={{
                  maxHeight: '460px',
                  overflowY: 'auto',
                  overflowX: 'hidden',
                  boxShadow: 'none',
                }}
                selectedKeys={currentTeam ? [String(currentTeam.id)!] : []}
              />

              {pathname !== '/newCombo' && (
                <>
                  <Divider style={{ margin: 0 }} />
                  <Menu
                    style={{ padding: 0, boxShadow: 'none' }}
                    items={[
                      {
                        key: 'newCombo',
                        label: (
                          <Typography.Link
                            onClick={() => {
                              clearCurrentTeam();
                              redirectToCombo();
                            }}
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              height: 30,
                              gap: 4,
                            }}
                          >
                            <div
                              style={{
                                flex: '0 0 22px',
                                height: 22,
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                textAlign: 'center',
                                overflow: 'hidden',
                              }}
                            >
                              <IconFontIcon size={18} iconName={'tianjia_24'} />
                            </div>
                            <div>购买新的TKShop套餐</div>
                          </Typography.Link>
                        ),
                      },
                    ]}
                  />
                </>
              )}
            </div>
          );
        }}
        trigger={['click']}
      >
        <div
          style={{
            maxWidth: 130,
            overflow: 'hidden',
            cursor: 'pointer',
            display: 'inline-flex',
            gap: 8,
            alignItems: 'center',
            flexWrap: 'nowrap',
          }}
        >
          {button}
          <IconFontIcon size={12} iconName={'angle-down_24'} />
        </div>
      </Dropdown>
    );
  }
  return null;
};
export default TeamsDropdown;
