@import './../../style/color';
.wechat-code {
  display: flex;
  flex-direction: column;
  align-content: center;
  align-items: center;
  justify-content: center;
  text-align: center;
  :global {
    .ant-alert {
      margin: 0;
    }
  }
}
.wechat-result {
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  width: 220px;
  height: 220px;
  padding: 0;

  :global {
    .ant-result-icon {
      margin-bottom: 12px;
      .anticon {
        font-size: 64px;
      }
    }
    .ant-result-title {
      font-size: 18px;
      line-height: 1.35;
      .ant-typography {
        margin-bottom: 0;
      }
    }
    .ant-result-content {
      margin-top: 12px;
      padding: 0;
      background: transparent;
    }
  }
}
.logo {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  overflow: hidden;
  background-color: white;
  background-image: url('./logo.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  border-radius: 5px;
  transform: translate(-50%, -50%);
}
