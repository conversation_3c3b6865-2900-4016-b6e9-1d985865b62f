import I18N from '@/i18n';
import styles from './index.less';
import { Col, Row } from 'antd';
import classNames from 'classnames';
import Placeholder from '@/components/Common/Placeholder';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { DndContext, MouseSensor, useSensor, useSensors } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import type { DragEndEvent } from '@dnd-kit/core/dist/types';
import {
  restrictToFirstScrollableAncestor,
  restrictToVerticalAxis,
  restrictToWindowEdges,
} from '@dnd-kit/modifiers';
import type { HTMLProps } from 'react';

export type Item = {
  id?: string;
  disabled?: boolean;
  alwaysShow?: boolean;
  title?: string | JSX.Element;
  [x: string]: any;
};
const SortableItem = (props: {
  id: any;
  sortable?: boolean;
  active?: boolean;
  disabled?: boolean;
  alwaysShow?: boolean;
  children: any;
}) => {
  const { attributes, listeners, transform, transition, setNodeRef } = useSortable({
    id: props.id,
    disabled: props.disabled,
    data: props,
  });
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const { active, disabled, alwaysShow, children, sortable, ...others } = props;
  return (
    <div {...attributes} ref={setNodeRef} {...others}>
      <Row
        className={classNames(styles.listItem, { active, disabled })}
        style={style}
        {...listeners}
      >
        <Col style={{ flex: 1, overflow: 'hidden', textTransform: 'capitalize' }}>{children}</Col>
        <Col className="handler">
          {disabled ? (
            <Placeholder style={{ fontSize: 12 }}>{I18N.t('（不可移除，不可移动）')}</Placeholder>
          ) : (
            <>
              {!sortable && (
                <Placeholder style={{ fontSize: 12 }}>{I18N.t('（不可移动）')}</Placeholder>
              )}
              {alwaysShow && (
                <Placeholder style={{ fontSize: 12 }}>{I18N.t('（不可移除）')}</Placeholder>
              )}
              {sortable && <IconFontIcon iconName={'menu'} />}
            </>
          )}
        </Col>
      </Row>
    </div>
  );
};
type Props = {
  items: Item[];
  direction?: 'vertical' | 'horizontal';
  activeKey?: string;
  onDragEnd?: (e: DragEndEvent) => void;
  onRow?: (data: Item, index: number) => HTMLProps<HTMLDivElement>;
};
const SortableList = (props: Props) => {
  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 5,
      },
    }),
  );
  const { items, onDragEnd, activeKey, onRow } = props;

  return (
    <DndContext
      sensors={sensors}
      onDragEnd={(e) => {
        const { over, active } = e;
        if (over && active && !over.data.current?.disabled) {
          onDragEnd?.(e);
        }
      }}
      modifiers={[restrictToVerticalAxis, restrictToWindowEdges, restrictToFirstScrollableAncestor]}
    >
      <SortableContext items={items} strategy={verticalListSortingStrategy}>
        <div className={styles.sortableList}>
          {items.map((item, index) => {
            const key = item.id;
            return (
              <SortableItem
                sortable
                disabled={item.disabled}
                alwaysShow={item.alwaysShow}
                key={`item-${key}`}
                active={activeKey === key}
                id={key}
                {...onRow?.(item, index)}
              >
                {item.title}
              </SortableItem>
            );
          })}
        </div>
      </SortableContext>
    </DndContext>
  );
};

export default SortableList;
