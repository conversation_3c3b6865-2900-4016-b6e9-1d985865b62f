@import './../../style/color';
.sortable-transfer-modal {
  :global {
    .sortable-transfer {
      height: 400px;
    }
  }
}
.sortable-helper {
  z-index: 999999;
}
.sortable-transfer {
  display: flex;
  align-content: stretch;
  align-items: stretch;
  width: 100%;
  overflow: hidden;
  user-select: none;
  :global {
    .disabled {
      //background-color: #f5f5f5;
      cursor: not-allowed;
    }
    .hidden {
      display: none;
    }
    .active {
      background-color: @active-background;
    }
  }
}
.sortable-list {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
  overflow: auto;
  overflow-x: hidden;
  user-select: none;
  :global {
    .disabled {
      cursor: not-allowed;
    }
    .hidden {
      display: none;
    }
    .active {
      background-color: @active-background;
    }
  }
}
.list-title {
  padding: 0 10px;
  line-height: 40px;
  background-color: #f6f6f6;
}
.list-item {
  display: flex;
  align-items: center;
  width: 100%;
  height: 40px;
  padding: 0 10px;
  overflow: hidden;
  cursor: pointer;
}
.list-wrapper {
  position: relative;
  flex: 1;
  overflow: auto;
}
.left,
.right {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid @border-color;
}

.middle {
  display: flex;
  flex-direction: column;
  gap: 30px;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
}
