import I18N from '@/i18n';
import type { Dispatch, SetStateAction } from 'react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import _ from 'lodash';
import { arrayMove } from '@dnd-kit/sortable';
import { <PERSON><PERSON>, Card, Col, Row, Space, Typography } from 'antd';
import classNames from 'classnames';
import DMModal from '@/components/Common/Modal/DMModal';
import styles from '@/pages/TikTok/components/style.less';
import SortableList from '@/components/SortableTransfer/SortableList';
import IconFontIcon from '@/components/Common/IconFontIcon';

export function getColumnByDataIndex(key: string, meta: any[]) {
  return meta.find((i) => i.dataIndex === key);
}

const TableColumnsSettingPanel = (props: {
  meta: any[];
  value: string[];
  onChange: Dispatch<SetStateAction<string[]>>;
}) => {
  const { meta, value, onChange } = props;
  const [selected, changeSelected] = useState<string>();
  const [direction, changeDirection] = useState<'left' | 'right'>();
  const getColumn = useCallback(
    (key: string) => {
      return getColumnByDataIndex(key, meta);
    },
    [meta],
  );
  const init = useRef(false);
  useEffect(() => {
    if (!selected && value?.length && !init.current) {
      changeSelected(
        _.find(value, (key) => !getColumn(key)?.disabled && !getColumn(key)?.alwaysShow),
      );
      init.current = true;
    }
  }, [selected, value, getColumn]);
  const activeFields = useMemo(() => {
    const _active: {
      title: string;
      dataIndex: string;
      disabled?: boolean;
      alwaysShow?: boolean;
    }[] = [];
    value?.forEach((key) => {
      const item = meta.find((i) => {
        return key === i.dataIndex;
      });
      if (item) {
        _active.push(item);
      }
    });
    return _.uniqBy(_active, 'dataIndex');
  }, [value, meta]);
  const resetFields = useMemo(() => {
    const _resets: { title: string; dataIndex: string; description?: string }[] = [];
    meta?.forEach((item) => {
      const index = value.findIndex((i) => {
        return i === item.dataIndex;
      });
      if (index === -1) {
        _resets.push(item);
      }
    });
    return _resets;
  }, [value, meta]);
  const resetColumns = useMemo(() => {
    return resetFields?.map((i) => {
      return i.dataIndex;
    });
  }, [resetFields]);
  useEffect(() => {
    if (selected) {
      if (_.findIndex(value, (i) => i === selected) !== -1) {
        changeDirection('left');
      } else if (_.findIndex(resetColumns, (i) => i === selected) !== -1) {
        changeDirection('right');
      }
    }
  }, [value, selected, resetFields, resetColumns]);
  const autoSelectNext = useCallback(
    (arr, i) => {
      const item = getColumn(arr[i]);
      if (!item) {
        changeSelected(undefined);
        return;
      }
      if (arr.length === 1) {
        if (item?.disabled || item.alwaysShow) {
          changeSelected(undefined);
        } else {
          changeSelected(arr[0]);
        }
      } else if (i > arr.length - 1) {
        if (item?.disabled || item.alwaysShow) {
          autoSelectNext(arr, 0);
        } else {
          changeSelected(arr[i]);
        }
      } else if (item?.disabled || item.alwaysShow) {
        autoSelectNext(arr, i + 1);
      } else {
        changeSelected(arr[i]);
      }
    },
    [getColumn],
  );
  // 取消选择
  const onDeselect = useCallback(
    async (index: number) => {
      onChange((prev: string[]) => {
        const _columns = [...prev];
        _columns.splice(index, 1);
        autoSelectNext(_columns, index);
        return _columns;
      });
    },
    [autoSelectNext, onChange],
  );
  // 选择
  const onSelect = useCallback(
    (key = selected) => {
      onChange((prevState) => {
        const index = _.findIndex(resetColumns, (i) => i === key);
        const _columns = [...prevState];

        // 找到最后一个没有disabled
        let findLastKey = _.findLastIndex(_columns, (_key) => {
          return (
            _.findIndex(meta, (i) => i.dataIndex === _key && !i.disabled && !i.alwaysShow) !== -1
          );
        });
        if (findLastKey === -1) {
          // 找不到没有disabled,
          //那就找到(disabled 但不是options/option 的key位置),往后插入
          findLastKey = _.findLastIndex(_columns, (_key) => {
            const target = _.find(meta, (i) => {
              return i.dataIndex === _key;
            });
            if (!target) {
              return false;
            }
            if (target.disabled || target.alwaysShow) {
              return _key !== 'option' && _key !== 'options' && target.valueType !== 'option';
            }
            return true;
          });
          _columns.splice(Math.max(findLastKey, 0) + 1, 0, key!);
        } else {
          // 找到最后一个没有disabled的key位置,往后插
          _columns.splice(findLastKey + 1, 0, key!);
        }
        autoSelectNext(
          resetColumns.filter((i) => i !== key),
          index,
        );
        return _columns;
      });
    },
    [selected, onChange, resetColumns, autoSelectNext, meta],
  );
  const onSort = useCallback(
    async (oldId, newId) => {
      onChange((prev: string[]) => {
        const _columns = [...prev];
        const oldIndex = _.findIndex(_columns, (i) => i === oldId);
        const newIndex = _.findIndex(_columns, (i) => i === newId);
        return arrayMove(_columns, oldIndex, newIndex);
      });
    },
    [onChange],
  );
  const remainNodes = useMemo(() => {
    return resetFields?.map(({ title, dataIndex, description }) => {
      return (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: 16,
            flexWrap: 'nowrap',
          }}
          onClick={() => {
            changeSelected(dataIndex);
          }}
          onDoubleClick={() => {
            onSelect(dataIndex);
          }}
          key={dataIndex}
          className={classNames({ active: selected === dataIndex }, 'ant-row')}
        >
          <div style={{ textTransform: 'capitalize', whiteSpace: 'nowrap' }}>{title}</div>
          {description && (
            <div style={{ overflow: 'hidden', fontSize: 12, maxWidth: '9em' }}>
              <Typography.Text
                type={'secondary'}
                ellipsis={{
                  tooltip: description,
                }}
              >
                {description}
              </Typography.Text>
            </div>
          )}
        </div>
      );
    });
  }, [selected, onSelect, resetFields]);
  const leftDisabled = useMemo(() => {
    return !selected || direction !== 'right';
  }, [selected, direction]);
  const rightDisabled = useMemo(() => {
    return !selected || direction !== 'left';
  }, [selected, direction]);
  return (
    <Row align={'stretch'} gutter={[12, 12]} style={{ height: 405 }} wrap={false}>
      <Col span={11}>
        <Card title={I18N.t('当前显示字段')}>
          <SortableList
            onDragEnd={(e) => {
              const { active, over } = e;
              if (over && active) {
                if (active.id !== over.id) {
                  onSort(active.id, over.id);
                }
              }
            }}
            onRow={(item, index) => {
              return {
                onClick() {
                  if (!item.disabled && !item.alwaysShow) {
                    changeSelected(item.id);
                  }
                },
                onDoubleClick() {
                  if (!item.disabled! && !item.alwaysShow) {
                    onDeselect(index);
                  }
                },
              };
            }}
            activeKey={selected}
            items={
              [
                ...(activeFields?.map(({ title, dataIndex, disabled, alwaysShow }) => {
                  return {
                    id: dataIndex,
                    title,
                    disabled,
                    alwaysShow,
                  };
                }) || []),
              ] || []
            }
          />
        </Card>
      </Col>
      <Col
        span={2}
        style={{
          display: 'flex',
          alignItems: 'center',
          flexDirection: 'column',
          justifyContent: 'center',
        }}
      >
        <Space direction={'vertical'} size={'large'}>
          <Button
            size={'small'}
            type={'primary'}
            disabled={rightDisabled}
            onClick={() => {
              // 取消激活
              const index = _.findIndex(value, (i) => {
                return i === selected;
              });
              if (index !== -1) {
                onDeselect(index);
              }
            }}
          >
            <IconFontIcon iconName="angle-right_24" />
          </Button>
          <Button
            size={'small'}
            type={'primary'}
            disabled={leftDisabled}
            onClick={() => {
              onSelect();
            }}
          >
            <IconFontIcon iconName="angle-left_24" />
          </Button>
        </Space>
      </Col>
      <Col span={11}>
        <Card title={I18N.t('待显示字段')} className={styles.columnPlanList}>
          {remainNodes}
        </Card>
      </Col>
    </Row>
  );
};

const TableColumnsSettingModalTrigger = (props: {
  columns: string[];
  meta: {
    dataIndex?: string;
    title?: string;
    disabled?: boolean;
    alwaysShow?: boolean;
    description?: string;
    [key: string]: any;
  }[];
  onSubmit: (keys: string[]) => void;
}) => {
  const { onSubmit, columns, meta } = props;
  const [visible, changeVisible] = useState(false);
  const [activeColumns, setActiveColumns] = useState<string[]>(columns);
  useEffect(() => {
    setActiveColumns(columns);
  }, [columns]);
  return (
    <>
      <Button
        style={{ color: '#666', paddingLeft: 8, paddingRight: 8 }}
        icon={<IconFontIcon iconName="shezhi_24" size={'inherit'} />}
        onClick={() => {
          changeVisible(true);
        }}
      >
        <span>{I18N.t('字段')}</span>
      </Button>
      <DMModal
        className={styles.columnPlanModal}
        width={800}
        onOk={() => {
          onSubmit(activeColumns);
          changeVisible(false);
        }}
        title={I18N.t('表格字段配置')}
        open={visible}
        onCancel={() => {
          changeVisible(false);
        }}
      >
        <TableColumnsSettingPanel value={activeColumns} meta={meta} onChange={setActiveColumns} />
      </DMModal>
    </>
  );
};
export default TableColumnsSettingModalTrigger;
