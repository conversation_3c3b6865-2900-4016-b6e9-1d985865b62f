import styled from 'styled-components';
import type { MouseEventHandler, ReactNode } from 'react';
import { useEffect, useMemo, useRef, useState } from 'react';

const StyledContainer = styled.div``;
export const ResponsiveOptions = (props: {
  itemWidth: number;
  moreBtnWidth: number;
  gap: number;
  items: {
    key: string;
    label: ReactNode;
    icon: ReactNode;
    onClick: MouseEventHandler<MouseEvent>;
  }[];
}) => {
  const { itemWidth, gap, items, moreBtnWidth = 30 } = props;
  const [ellipsis, setEllipsis] = useState(false);
  const ref = useRef<HTMLDivElement>();
  const [maxCount, setMaxCount] = useState(items.length);

  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      const containerWidth = entries[0].contentRect.width;
      // 计算所有项目完全显示需要的总宽度
      const totalItemsWidth = items.length * itemWidth + (items.length - 1) * gap;
      // 判断是否溢出
      if (totalItemsWidth <= containerWidth) {
        // 不溢出，显示所有项目
        setEllipsis(false);
        setMaxCount(items.length);
      } else {
        // 溢出，需要显示"更多"按钮
        setEllipsis(true);
        // 计算可用于显示项目的宽度（减去"更多"按钮和其前面的gap）
        const availableWidth = containerWidth - moreBtnWidth - gap;
        // 计算能显示的项目数量
        // availableWidth = n * itemWidth + (n-1) * gap
        // 解得：n = (availableWidth + gap) / (itemWidth + gap)
        const _count = Math.floor((availableWidth + gap) / (itemWidth + gap));
        // 确保至少为0，最多为总项目数-1（因为有"更多"按钮）
        setMaxCount(Math.max(0, Math.min(_count, items.length - 1)));
      }
    });
    if (ref.current) {
      observer.observe(ref.current);
    }
    return () => {
      observer.disconnect();
    };
  }, [itemWidth, gap, moreBtnWidth, items.length]);
  const nodes = useMemo(() => {}, [items, maxCount]);
  const more = useMemo(() => {}, []);

  return (
    <div style={{ overflow: 'hidden' }} ref={ref}>
      <StyledContainer>
        {nodes}
        {more}
      </StyledContainer>
    </div>
  );
};
