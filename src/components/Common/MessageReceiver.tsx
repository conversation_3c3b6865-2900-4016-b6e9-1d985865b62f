import I18N from '@/i18n';
import React, { useCallback, useMemo, useState } from 'react';
import { Alert, Input, Table } from 'antd';
import colors from '@/style/color.less';
import { useRequest } from '@@/plugin-request/request';
import { userByTeamByTeamIdUsersGet } from '@/services/api-Account/UserController';
import { getTeamIdFromUrl } from '@/utils/utils';
import UserAvatarAndName from '@/components/Common/UserAvatarAndName';
import IconFontIcon from '@/components/Common/IconFontIcon';
import DMModal from '@/components/Common/Modal/DMModal';
import HelpLink from '@/components/HelpLink';

type Props = {
  existUserIds?: React.Key[];
  onSubmit: (userIds: React.Key[]) => void;
};

/**
 * 选择通知接收方用户
 * @param props
 * @constructor
 */
const MessageReceiver: React.FC<Props> = (props) => {
  const { existUserIds = [], onSubmit } = props;
  const [visible, changeVisible] = useState(true);
  const [selectedUserIds, setSelectedUserIds] = useState<React.Key[]>(existUserIds);
  const [searchText, setSearchText] = useState('');
  const onClose = useCallback(() => {
    changeVisible(false);
  }, []);
  const { data, loading } = useRequest(() =>
    userByTeamByTeamIdUsersGet({
      teamId: getTeamIdFromUrl(),
      pageSize: 99999,
      pageNum: 1,
    }),
  );

  const userList = useMemo(() => {
    return (data?.list ?? []).filter(
      (u) => !searchText || u.nickname?.toLowerCase().includes(searchText?.toLowerCase() ?? ''),
    );
  }, [data, searchText]);

  return (
    <DMModal
      open={visible}
      title={I18N.t('选择接收人')}
      width={650}
      bodyStyle={{ height: 385 + 56 }}
      onCancel={onClose}
      okButtonProps={{
        disabled: !selectedUserIds?.length,
      }}
      onOk={() => {
        onSubmit(selectedUserIds);
        onClose();
      }}
    >
      <Alert
        showIcon
        message={<div>{I18N.t('请确保收件人绑定了手机/邮箱/微信，系统并不做强制性检查')}</div>}
        style={{ marginBottom: 16 }}
      />

      <div style={{ textAlign: 'right', marginBottom: 16 }}>
        <Input.Search
          defaultValue={searchText}
          placeholder={I18N.t('根据用户名称进行检索')}
          allowClear
          onSearch={(t) => setSearchText(t)}
          style={{ width: 220 }}
        />
      </div>
      <Table
        size="small"
        loading={loading}
        columns={[
          {
            title: I18N.t('用户'),
            dataIndex: 'id',
            render: (dom, record) => (
              <UserAvatarAndName
                user={{ id: record.id, avatar: record.avatar, nickname: record.nickname }}
              />
            ),
          },
          {
            title: I18N.t('手机'),
            dataIndex: 'phone',
            width: 50,
            align: 'center',
            render: (dom, record) =>
              record.phone ? (
                <IconFontIcon iconName="check_24" color={colors.successColor} />
              ) : (
                <IconFontIcon iconName="guanbi_24" color={colors.textMutedColor} />
              ),
          },
          {
            title: I18N.t('微信'),
            dataIndex: 'weixin',
            width: 50,
            align: 'center',
            render: (dom, record) =>
              record.weixin ? (
                <IconFontIcon iconName="check_24" color={colors.successColor} />
              ) : (
                <IconFontIcon iconName="guanbi_24" color={colors.textMutedColor} />
              ),
          },
          {
            title: I18N.t('邮箱'),
            dataIndex: 'email',
            width: 50,
            align: 'center',
            render: (dom, record) =>
              record.email ? (
                <IconFontIcon iconName="check_24" color={colors.successColor} />
              ) : (
                <IconFontIcon iconName="guanbi_24" color={colors.textMutedColor} />
              ),
          },
        ]}
        rowKey="id"
        dataSource={userList}
        rowSelection={{
          selectedRowKeys: selectedUserIds,
          onChange: (ids) => setSelectedUserIds(ids),
          columnWidth: 42,
        }}
        onRow={(record) => {
          return {
            style: { cursor: 'pointer' },
            onClick: () => {
              if (selectedUserIds.includes(record.id!)) {
                setSelectedUserIds(selectedUserIds.filter((id) => id !== record.id!));
              } else {
                setSelectedUserIds([...selectedUserIds, record.id!]);
              }
            },
          };
        }}
        scroll={{ y: 250 }}
        pagination={false}
      />
    </DMModal>
  );
};

export default MessageReceiver;
