import ColoursIcon from '@/components/Common/ColoursIcon';
import type { CSSProperties, FC } from 'react';
import { isIconValid } from '@/utils/iconUtils';

interface Props {
  platformName: string;
  className?: string;
  size?: number;
  style?: CSSProperties;
}
const Icon = (props: { type: string }) => {
  const { type, ...others } = props;
  let className = type === 'Other' ? 'qitapingtai_24' : `${type}_24`;

  // 如果type以任意数字开头的话得加上icon_前缀
  if (type?.match(/^\d+/)) {
    className = `icon_${className}`;
  }
  if (!isIconValid(className)) {
    return <ColoursIcon className={'qitapingtai_24'} {...others} />;
  }
  return <ColoursIcon className={className} {...others} />;
};

const PlatformCateIcon: FC<Props> = (props) => {
  const { platformName, className, size = 16, ...others } = props;
  if (platformName === 'All') {
    return <ColoursIcon className="buxianpingtai_24" size={size} />;
  }

  if (platformName === 'IM') {
    //  要求不一样
    return <ColoursIcon className={'shejiaopingtai_24'} size={size} {...others} />;
  }
  if (platformName === 'Mobile') {
    return <ColoursIcon className="yidongwangluoIP_24" size={size} {...others} />;
  }
  if (platformName === 'TkVideo') {
    return <ColoursIcon className="TikTok_24" size={size} {...others} />;
  }
  if (platformName === 'Facebook') {
    return <ColoursIcon className="FbMessenger_24" size={size} {...others} />;
  }
  return <Icon type={platformName} size={size} {...others} />;
};
export default PlatformCateIcon;
