import { Button, Dropdown, Space } from 'antd';
import I18N from '@/i18n';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { useLocalStorageState } from 'ahooks';
import { getCurrentTeamId } from '@/hooks/useCurrentTeam';
import { generateUUID } from '@/utils/utils';
import { useCallback, useEffect } from 'react';
import EventEmitter from 'events';
import { ContactTypeLabel } from '@/pages/TikTok/utils/utils';

const eventEmitter = new EventEmitter();

const symbol_event = Symbol('update');
export function useCreatorView(refer: 'buyer' | 'creator') {
  const [view, setView] = useLocalStorageState<
    'normal' | 'whatsapp' | 'line' | 'zalo' | 'fbMessenger' | 'viber'
  >(`im_view_${refer}_${getCurrentTeamId()}_20250624`, 'normal');
  const uuid = generateUUID();
  const fn = useCallback(
    (value: any) => {
      setView(value);
    },
    [setView],
  );
  useEffect(() => {
    eventEmitter.on(symbol_event, fn);
    return () => {
      eventEmitter.off(symbol_event, fn);
    };
  }, [fn]);
  return {
    value: view,
    onChange(value: string) {
      eventEmitter.emit(symbol_event, value, uuid);
    },
  };
}

const CreatorViewDropdown = (props: { refer: 'buyer' | 'creator' }) => {
  const { refer } = props;
  const { value, onChange } = useCreatorView(refer);
  return (
    <Dropdown
      arrow
      align={{
        offset: [15, -10],
      }}
      trigger={['hover']}
      menu={{
        selectable: true,
        selectedKeys: [value],
        multiple: false,
        onSelect({ key }) {
          onChange(key);
        },
        items: [
          {
            label: I18N.t('常规视图'),
            key: 'normal',
          },
          {
            label: I18N.t('即时消息之 Whatsapp'),
            key: 'whatsapp',
          },
          {
            label: I18N.t('即时消息之 Line'),
            key: 'line',
          },
          {
            label: I18N.t('即时消息之 Facebook'),
            key: 'fbMessenger',
          },
          {
            label: I18N.t('即时消息之 Zalo'),
            key: 'zalo',
          },
          {
            label: I18N.t('即时消息之 Viber'),
            key: 'viber',
          },
        ],
      }}
    >
      <Button ghost style={{ background: 'none' }}>
        <Space>
          <IconFontIcon iconName={'Eye-Open_24'} />
          <span style={{ marginRight: 4 }}>
            {value === 'normal' ? '常规视图' : ContactTypeLabel[value]}
          </span>
          <IconFontIcon size={12} iconName={'angle-down_24'} />
        </Space>
      </Button>
    </Dropdown>
  );
};
export default CreatorViewDropdown;
