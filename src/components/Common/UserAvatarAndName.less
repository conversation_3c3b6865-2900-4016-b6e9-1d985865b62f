@import '~/src/style/color.less';

.user-avatar-and-name {
  display: flex;
  align-items: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow-wrap: break-word;

  :global {
    .nickname {
      flex-shrink: 1;
      min-width: 0;
      margin-left: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .user-id {
      flex-shrink: 0;
      color: @text-muted;

      &.user-id-value {
        flex-shrink: 1;
        min-width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
.border {
  box-sizing: content-box;
  border: 2px solid @border-color;
}
