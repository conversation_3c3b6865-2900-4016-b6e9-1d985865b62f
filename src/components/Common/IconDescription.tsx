import React from 'react';
import { Space } from 'antd';
import ColoursIcon from './ColoursIcon';

interface Props {
  iconName: string;
  title?: React.ReactElement | string;
  description?: React.ReactElement | string;
  size?: number;
}

/**
 * 上面是图片，下面是标题加描述
 * @param props
 * @returns
 */
const IconDescription: React.FC<Props> = (props) => {
  return (
    <Space
      direction="vertical"
      style={{
        alignItems: 'center',
        width: '100%',
        marginTop: '100px',
      }}
    >
      <ColoursIcon className={props.iconName} size={props.size || 64} />
      {props.description && <div style={{ marginTop: '20px' }}>{props.title}</div>}
      {props.description && <div style={{ color: '#878787' }}>{props.description}</div>}
    </Space>
  );
};

export default IconDescription;
