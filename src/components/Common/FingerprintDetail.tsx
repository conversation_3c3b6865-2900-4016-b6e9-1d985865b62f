import I18N from '@/i18n';
import type { ReactNode, RefObject } from 'react';
import React, { createRef, useCallback, useMemo, useRef, useState } from 'react';
import { Button, Col, ConfigProvider, Form, Row, Space, Tabs, Typography } from 'antd';
import _ from 'lodash';
import classNames from 'classnames';
import type { RenderTabBar } from 'rc-tabs/lib/interface';
import buttonStyles from '@/style/button.less';
import styles from './fingerprintDetail.less';
import random from '@/utils/fingerprint/random';
import FingerprintForm, { FingerprintDetailContext } from './FingerprintForm';
import type { FormInstance } from 'antd/lib/form/hooks/useForm';
import ColoursIcon from '@/components/Common/ColoursIcon';
import { FingerType } from '@/types/fingerprint';
import { useModalCaller } from '@/mixins/modal';
import Functions from '@/constants/Functions';
import {
  fingerCheckDuplicatePost,
  fingerDetailByFingerprintIdGet,
  fingerGetByFingerprintIdGet,
  fingerSetShopByShopIdPost as shopUsePost,
} from '@/services/api-ShopAPI/FingerprintController';
import {
  shopReGenFingerprintPut,
  shopUpdateShopFingerprintPost,
} from '@/services/api-ShopAPI/ShopController';
import { useAuthJudgeCallback } from '@/components/Common/FunctionButton';
import DMModal, { getModalFormSubmitter } from '@/components/Common/Modal/DMModal';
import { useFingerprintValidateModal } from '@/pages/ShopManage/ShopDetail/components/FingerprintValidateModal';
import PopTips from '@/components/Common/PopTips/PopTips';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { ModalForm } from '@ant-design/pro-form';
import FpUASelector from '@/components/FpUASelector';
import { useWebRTCSettingsModal } from '@/components/Common/FingerprintForm/WebRTCSettingsModal';
import { useHardwareSettingsModal } from '@/components/Common/FingerprintForm/HardwareSettingsModal';

const { TabPane } = Tabs;

interface Props {
  shopDetailVo?: API.ShopDetailVo;
  fingerprint?: API.FingerprintConfigVo;
  // 是否正在编辑
  editing?: boolean;
  // 是否在对话框中
  inDialog?: boolean;
  renderTabBar?: RenderTabBar;
  showTabBarIcon?: boolean;
  // 是否为指纹模板
  isTemplate?: boolean;
  // 场景
  scope?: 'collection' | 'session' | string;
  showComputerName?: boolean;
  templateVo?: API.FingerprintTemplateVo;
  instanceVo?: API.FingerprintVo;
  onTabChange?: (tabKey: TabKey) => void;
  onUpdate?: () => void;
}

type TabKey =
  | 'baseInfo'
  | 'browser'
  | 'hardware'
  | 'media'
  | 'plugin'
  | 'ports'
  | 'webrtc'
  | string;

/**
 * 指纹信息表单
 */
class FingerprintDetail extends React.Component<Props, { activeKey: TabKey }> {
  baseInfoForm: RefObject<FormInstance>;
  browserForm: RefObject<FormInstance>;
  hardwareForm: RefObject<FormInstance>;
  mediaForm: RefObject<FormInstance>;
  pluginForm: RefObject<FormInstance>;
  portsForm: RefObject<FormInstance>;
  webrtcForm: RefObject<FormInstance>;
  constructor(props: Props) {
    super(props);
    this.baseInfoForm = createRef<FormInstance>();
    this.browserForm = createRef<FormInstance>();
    this.hardwareForm = createRef<FormInstance>();
    this.mediaForm = createRef<FormInstance>();
    this.pluginForm = createRef<FormInstance>();
    this.portsForm = createRef<FormInstance>();
    this.webrtcForm = createRef<FormInstance>();
    this.state = {
      activeKey: 'baseInfo',
    };
  }

  componentDidMount() {
    if (this.props.onTabChange) {
      this.props.onTabChange(this.state.activeKey);
    }
  }

  async submit() {
    const { fingerprint } = this.props;
    const { activeKey } = this.state;

    try {
      if (activeKey === 'baseInfo') await this.baseInfoForm.current?.validateFields();
    } catch (e) {
      this.setState({ activeKey: 'baseInfo' });
      throw e;
    }
    try {
      if (activeKey === 'browser') await this.browserForm.current?.validateFields();
    } catch (e) {
      this.setState({ activeKey: 'browser' });
      throw e;
    }
    try {
      if (activeKey === 'hardware') await this.hardwareForm.current?.validateFields();
    } catch (e) {
      this.setState({ activeKey: 'hardware' });
      throw e;
    }
    try {
      if (activeKey === 'media') await this.mediaForm.current?.validateFields();
    } catch (e) {
      this.setState({ activeKey: 'media' });
      throw e;
    }
    try {
      if (activeKey === 'plugin') await this.pluginForm.current?.validateFields();
    } catch (e) {
      this.setState({ activeKey: 'plugin' });
      throw e;
    }
    try {
      if (activeKey === 'ports') await this.portsForm.current?.validateFields();
    } catch (e) {
      this.setState({ activeKey: 'ports' });
      throw e;
    }
    try {
      if (activeKey === 'webrtc') await this.webrtcForm.current?.validateFields();
    } catch (e) {
      this.setState({ activeKey: 'webrtc' });
      throw e;
    }
    const res = {};
    const baseInfoForm = this.baseInfoForm.current?.getFieldsValue(true);
    const browserForm = this.browserForm.current?.getFieldsValue(true);
    const hardwareForm = this.hardwareForm.current?.getFieldsValue(true);
    const mediaForm = this.mediaForm.current?.getFieldsValue(true);
    const pluginForm = this.pluginForm.current?.getFieldsValue(true);
    const portsForm = this.portsForm.current?.getFieldsValue(true);
    const webrtcForm = this.webrtcForm.current?.getFieldsValue(true);
    if (activeKey === 'baseInfo' && baseInfoForm) {
      Object.assign(res, baseInfoForm);
    }
    if (activeKey === 'browser' && browserForm) {
      Object.assign(res, _.pick(browserForm, ['fonts', 'headers', 'userAgent']), {
        headers: (browserForm.headers || '').split('\n'),
        location_type: browserForm.location.type,
        location: browserForm.location.value,
        timezone_type: browserForm.timezone.type,
        timezone: browserForm.timezone.value,
        lang_type: browserForm.lang.type,
        lang: browserForm.lang.value,
      });
    }
    if (activeKey === 'hardware' && hardwareForm) {
      Object.assign(res, hardwareForm, {
        screenSize_type: hardwareForm.screenSize.type,
        screenSize: hardwareForm.screenSize.value,
        audio: hardwareForm.audio ? random.nextInt(1, 1000) : 0,
        ...hardwareForm.canvas,
        webglDX: hardwareForm.webglDX ? random.nextFloat(-1, 1) : 0,
        rectDX: hardwareForm.rectDX ? random.nextFloat(-1, 1) : 0,
        webgl_type: hardwareForm.webgl_type ? FingerType.Assign : FingerType.Original,
        batteryType: hardwareForm.batteryType ? FingerType.Assign : FingerType.Original,
      });
    }
    if (activeKey === 'media' && mediaForm) {
      Object.assign(res, mediaForm);
    }
    if (activeKey === 'plugin' && pluginForm) {
      Object.assign(res, pluginForm, {
        pluginType: pluginForm.pluginType ? FingerType.Assign : FingerType.Original,
      });
    }
    if (activeKey === 'ports' && portsForm) {
      Object.assign(res, portsForm, {
        ports_type: portsForm.ports_type ? FingerType.Assign : FingerType.Original,
        ports: (portsForm.ports || '').split(','),
      });
    }
    if (activeKey === 'webrtc' && webrtcForm) {
      Object.assign(res, {
        webrtcPublicIp_type: webrtcForm.webrtcDisabled
          ? FingerType.Disabled
          : webrtcForm.publicIp.type,
        webrtcPublicIp: webrtcForm.publicIp.value,
        webrtcInnerIp_type: webrtcForm.webrtcDisabled
          ? FingerType.Disabled
          : webrtcForm.innerIp.type,
        webrtcInnerIp: webrtcForm.innerIp.value,
      });
    }
    return {
      tabKey: activeKey,
      ...fingerprint,
      ...res,
    };
  }

  render() {
    const {
      shopDetailVo,
      fingerprint,
      editing = false,
      inDialog = true,
      scope,
      showComputerName = false,
      renderTabBar,
      showTabBarIcon,
      isTemplate = false,
      templateVo = {},
      instanceVo = {},
      onUpdate = () => {},
    } = this.props;
    const { activeKey } = this.state;

    if (!fingerprint) {
      return null;
    }
    const formBaseProps = {
      shopDetailVo,
      config: fingerprint,
      templateVo,
      instanceVo,
      editing,
      isTemplate,
      scope,
      showComputerName,
      onUpdate,
    };
    return (
      <ConfigProvider form={{ requiredMark: false }}>
        <FingerprintDetailContext.Provider value={{ editing }}>
          <Tabs
            tabPosition="left"
            className={classNames(styles.tabs, { 'dm-in-dialog': inDialog }, { disabled: editing })}
            activeKey={activeKey}
            onTabClick={(key: TabKey) => {
              if (!editing) {
                this.setState({ activeKey: key });
                if (this.props.onTabChange) {
                  this.props.onTabChange(key);
                }
              }
            }}
            renderTabBar={renderTabBar}
          >
            <TabPane
              tab={
                <Space size="small">
                  {showTabBarIcon && <ColoursIcon className="jibenxinxi_24" size={16} />}
                  {I18N.t('基本信息')}
                </Space>
              }
              key="baseInfo"
            >
              <FingerprintForm.BaseInfoForm {...formBaseProps} ref={this.baseInfoForm} />
            </TabPane>
            <TabPane
              tab={
                <Space size="small">
                  {showTabBarIcon && <ColoursIcon className="liulanqi_24" size={16} />}
                  {I18N.t('浏览器参数')}
                </Space>
              }
              key="browser"
            >
              <FingerprintForm.BrowserForm {...formBaseProps} ref={this.browserForm} />
            </TabPane>
            <TabPane
              tab={
                <Space size="small">
                  {showTabBarIcon && <ColoursIcon className="kehuduan_24" size={16} />}
                  <span>{I18N.t('硬件指纹')}</span>
                </Space>
              }
              key="hardware"
            >
              <FingerprintForm.HardwareForm {...formBaseProps} ref={this.hardwareForm} />
            </TabPane>
            <TabPane
              tab={
                <Space size="small">
                  {showTabBarIcon && <ColoursIcon className="meitishebei_24" size={16} />}
                  <span>{I18N.t('媒体设备指纹')}</span>
                </Space>
              }
              key="media"
            >
              <FingerprintForm.MediaForm {...formBaseProps} ref={this.mediaForm} />
            </TabPane>
            <TabPane
              tab={
                <Space size="small">
                  {showTabBarIcon && <ColoursIcon className="chajian_24" size={16} />}
                  <span>{I18N.t('插件指纹')}</span>
                </Space>
              }
              key="plugin"
            >
              <FingerprintForm.PluginForm {...formBaseProps} ref={this.pluginForm} />
            </TabPane>
            <TabPane
              tab={
                <Space size="small">
                  {showTabBarIcon && <ColoursIcon className="duankou_24" size={16} />}
                  <span>{I18N.t('端口防护')}</span>
                </Space>
              }
              key="ports"
            >
              <FingerprintForm.PortsForm {...formBaseProps} ref={this.portsForm} />
            </TabPane>
            <TabPane
              tab={
                <Space size="small">
                  {showTabBarIcon && <ColoursIcon className="WebRTC_24" size={16} />}
                  <span>{I18N.t('WebRTC指纹')}</span>
                </Space>
              }
              key="webrtc"
            >
              <FingerprintForm.WebRTCForm {...formBaseProps} ref={this.webrtcForm} />
            </TabPane>
          </Tabs>
        </FingerprintDetailContext.Provider>
      </ConfigProvider>
    );
  }
}

export default FingerprintDetail;

type ModalProps = Omit<Props, 'inDialog'> & {
  shopDetail: API.ShopDetailVo;
  onUpdate?: () => void;
};
const FingerprintDetailModal = (props: ModalProps) => {
  const { shopDetail, onUpdate, fingerprint } = props;
  const [visible, toggle] = useState(true);
  const [md5sum, setMd5] = useState(shopDetail?.fingerprintVo?.md5sum);
  const [isEditing, setEditing] = useState(false);
  const [activeTab, setActiveTab] = useState('baseInfo');
  const [tmpConfig, setTmpConfig] = useState<API.FingerprintConfigVo>(fingerprint!);
  const [fingerprintVoState, setFingerprintVoState] = useState<API.FingerprintVo | undefined>(
    shopDetail?.fingerprintVo,
  );
  const [regenForm] = Form.useForm();
  const fingerprintDetailRef = useRef<FingerprintDetail>(null);
  const hasAuthedFunction = useAuthJudgeCallback();
  const openValidateModal = useFingerprintValidateModal();
  const showWebRTCSettingsModal = useWebRTCSettingsModal();
  const showHardwareSettingsModal = useHardwareSettingsModal();
  const duplicateShops = useMemo(() => {
    return shopDetail.fingerprintVo?.duplicateShops?.length > 0
      ? shopDetail.fingerprintVo?.duplicateShops
      : [];
  }, [shopDetail.fingerprintVo?.duplicateShops]);
  const editBtnVisible = useMemo(() => {
    if (!hasAuthedFunction(Functions.SHOP_FINGERPRINT_MANAGE)) {
      return false;
    }
    if (['webrtc', 'hardware'].includes(activeTab)) {
      return true;
    }
    return false;
  }, [activeTab, hasAuthedFunction]);
  const handleSave = useCallback(async () => {
    try {
      const forms = await fingerprintDetailRef.current?.submit();
      const requestBody = {
        platform: forms?.platform,
        browser: forms?.browser,
        config: forms as any,
      };
      const { data } = await fingerCheckDuplicatePost(forms);
      const submit = async () => {
        await shopUsePost({ shopId: shopDetail.id! }, requestBody);
        setEditing(false);
        setTmpConfig(forms as any);
        if (onUpdate) {
          onUpdate();
        }
        setMd5(data.md5sum);
      };
      if (data.md5sum && data.md5sum === md5sum) {
        // 等于当前的,就别保存了
        setEditing(false);
        setTmpConfig(forms as any);
        if (onUpdate) {
          onUpdate();
        }
        return false;
      }
      const shops = data?.duplicateShops?.filter((shop) => shop && shop.id !== shopDetail.id);
      if (shops.length > 0) {
        openValidateModal({
          res: data,
          onSave: async () => {
            await submit();
          },
          onCancel: () => {
            setEditing(true);
          },
          oldMd5sum: md5sum,
        });
      } else {
        openValidateModal({
          res: data,
          oldMd5sum: md5sum,
        });
        await submit();
      }
    } catch (e) {
      console.info(e);
    }
  }, [md5sum, onUpdate, openValidateModal, shopDetail.id]);
  const renderFooter = useMemo(() => {
    let btns: ReactNode = null;
    const handleCancel = () => {
      setEditing(false);
      toggle(false);
    };
    if (hasAuthedFunction(Functions.SHOP_FINGERPRINT_MANAGE)) {
      if (isEditing) {
        btns = (
          <>
            <Button key="save" type="primary" onClick={() => handleSave()}>
              {I18N.t('保存')}
            </Button>
            <Button key="cancel" onClick={() => setEditing(false)}>
              {I18N.t('取消')}
            </Button>
          </>
        );
      } else {
        return (
          <Row style={{ width: '100%' }}>
            <Col flex={1} style={{ textAlign: 'left' }}>
              <Space style={{ height: '100%' }}>
                {I18N.t('浏览器指纹特征码')}
                <PopTips
                  maxWidth={350}
                  content={[
                    I18N.t(
                      '浏览器指纹特征码是指针对全部指纹信息进行消息摘要算法计算出来的唯一编码，指纹中任何一个属性的变化都会影响到特征码，可通过特征码是否重复判断指纹的唯一性',
                    ),
                  ]}
                  knowmoreLink="/help/fp/brief#code"
                >
                  <IconFontIcon iconName="bangzhu_24" style={{ cursor: 'pointer' }} />
                </PopTips>
                ：
                <Typography.Text type={duplicateShops?.length > 0 ? 'danger' : 'success'}>
                  {md5sum}
                </Typography.Text>
              </Space>
            </Col>
            <Col>
              <ModalForm
                form={regenForm}
                title={I18N.t('重新生成指纹')}
                width={500}
                modalProps={{ centered: true }}
                trigger={
                  <Button type="primary" danger>
                    {I18N.t('重新生成指纹')}
                  </Button>
                }
                onFinish={() => {
                  // 重新生成指纹
                  return shopReGenFingerprintPut({
                    shopIds: [shopDetail.id!],
                    params: regenForm.getFieldsValue(true),
                  }).then(() => {
                    fingerDetailByFingerprintIdGet({
                      fingerprintId: shopDetail!.fingerprintId!,
                    }).then((res) => {
                      setTmpConfig(res.data!);
                    });
                    fingerGetByFingerprintIdGet({
                      fingerprintId: shopDetail!.fingerprintId!,
                    }).then((res) => {
                      setFingerprintVoState(res.data);
                    });
                    if (onUpdate) {
                      onUpdate();
                    }
                    return true;
                  });
                }}
                submitter={getModalFormSubmitter()}
              >
                <FpUASelector
                  layout="vertical"
                  onChange={(values) => {
                    regenForm.setFieldsValue(values);
                  }}
                />
              </ModalForm>
              {editBtnVisible && (
                <Button
                  key="edit"
                  className={buttonStyles.successBtn}
                  onClick={() => {
                    if (activeTab === 'webrtc') {
                      showWebRTCSettingsModal({
                        webrtcPublicIp_type: tmpConfig?.webrtcPublicIp_type,
                        webrtcPublicIp: tmpConfig?.webrtcPublicIp,
                        webrtcInnerIp_type: tmpConfig?.webrtcInnerIp_type,
                        webrtcInnerIp: tmpConfig?.webrtcInnerIp,
                        onSubmit: (values) => {
                          // 修改WebRTC
                          const params = {
                            webrtcPublicIp_type: values.webrtcDisabled
                              ? FingerType.Disabled
                              : values.publicIp.type,
                            webrtcPublicIp: values.publicIp.value,
                            webrtcInnerIp_type: values.webrtcDisabled
                              ? FingerType.Disabled
                              : values.innerIp.type,
                            webrtcInnerIp: values.innerIp.value,
                          };
                          return shopUpdateShopFingerprintPost({
                            shopIds: [shopDetail.id!],
                            updateWebrtc: true,
                            ...params,
                          }).then(() => {
                            setTmpConfig({
                              ...tmpConfig,
                              ...params,
                            });
                          });
                        },
                      });
                      return;
                    }
                    if (activeTab === 'hardware') {
                      showHardwareSettingsModal({
                        cpu: tmpConfig.cpu,
                        mem: tmpConfig.mem,
                        enableCanvas: !!tmpConfig.canvasMode,
                        enableAudio: !!tmpConfig.audio,
                        enableWebgl: tmpConfig.webgl_type !== FingerType.Original,
                        enableRect: !!tmpConfig.rectDX,
                        onSubmit: (values) => {
                          shopUpdateShopFingerprintPost({
                            shopIds: [shopDetail.id!],
                            updateHardware: true,
                            ...values,
                          }).then(() => {
                            fingerDetailByFingerprintIdGet({
                              fingerprintId: shopDetail!.fingerprintId!,
                            }).then((res) => {
                              setTmpConfig(res.data!);
                            });
                          });
                        },
                      });
                      return;
                    }
                    setEditing(true);
                  }}
                >
                  {I18N.t('编辑')}
                </Button>
              )}
              <Button key="close" type={'primary'} onClick={handleCancel}>
                {I18N.t('关闭')}
              </Button>
            </Col>
          </Row>
        );
      }
    } else {
      btns = (
        <Button key="close" type="primary" onClick={handleCancel}>
          {I18N.t('关闭')}
        </Button>
      );
    }
    return btns;
  }, [
    activeTab,
    duplicateShops?.length,
    editBtnVisible,
    handleSave,
    hasAuthedFunction,
    isEditing,
    md5sum,
    onUpdate,
    regenForm,
    shopDetail,
    showHardwareSettingsModal,
    showWebRTCSettingsModal,
    tmpConfig,
  ]);
  return (
    <DMModal
      visible={visible}
      width={800}
      title={I18N.t('查看浏览器指纹')}
      footer={renderFooter}
      bodyStyle={{ padding: 0, height: 545 }}
      onCancel={() => toggle(false)}
    >
      <FingerprintDetail
        ref={fingerprintDetailRef}
        shopDetailVo={shopDetail}
        fingerprint={tmpConfig}
        instanceVo={fingerprintVoState}
        editing={isEditing}
        onUpdate={onUpdate}
        onTabChange={(tabKey) => setActiveTab(tabKey)}
      />
    </DMModal>
  );
};
export function useOpenFingerprintDetail(
  shopDetail: API.ShopDetailVo,
  fingerprintConfigVo: API.FingerprintConfigVo,
  onUpdate?: () => void,
) {
  const modalCaller = useModalCaller(true);
  return useCallback(() => {
    modalCaller({
      component: (
        <FingerprintDetailModal
          shopDetail={shopDetail}
          onUpdate={onUpdate}
          fingerprint={fingerprintConfigVo}
        />
      ),
    });
  }, [fingerprintConfigVo, modalCaller, onUpdate, shopDetail]);
}
