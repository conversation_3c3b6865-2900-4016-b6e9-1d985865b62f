import { useMemo, useState } from 'react';
import DMModal from '@/components/Common/Modal/DMModal';
import { Badge, Button, Checkbox, message, Typography } from 'antd';
import type { GhostModalWrapperComponentProps } from '@/mixins/modal';
import { AddTagDropdown } from './TagManage/TagAddButton';
import {
  tagListByResourceIdsPost,
  tagPost,
  tagResourcesDelete,
  tagResourcesPost,
} from '@/services/api-ShopAPI/TagController';
import _ from 'lodash';
import TagItem from './TagManage/TagItem';
import { useRequest } from 'umi';
import MiddleSpin from './MiddleSpin';
import Segmented from 'rc-segmented';
import 'rc-segmented/assets/index.css';
import styled from 'styled-components';
import EmptyView from '@/components/Common/EmptyView';
import I18N from '@/i18n';
import { getLabelByResourceType } from '@/components/Common/TagManage';

const StyledTitle = styled.div`
  text-align: center;
  .rc-segmented-item {
    width: 140px;
    padding: 4px !important;
    overflow: visible;
    &.rc-segmented-item-selected {
      color: #0f7cf4;
    }
    .ant-badge {
      color: inherit;
    }
  }
`;
const TagOperationSelected = (
  props: GhostModalWrapperComponentProps & {
    resourceType: API.tagResourcesPostParams['resourceType'];
    ids: number[];
    onUpdate: () => void;
  },
) => {
  const { resourceType, modalProps, ids, onUpdate } = props;
  const [mode, changeMode] = useState<'add' | 'clear'>('add');
  const [visible, changeVisible] = useState(true);
  const [addTags, setAddTags] = useState<API.TagVo[]>([]);
  const [clearTags, setClearTags] = useState<API.TagVo[]>([]);
  const { data: clearTagMetas, loading } = useRequest(() => {
    return tagListByResourceIdsPost({
      resourceType,
      resourceIds: ids,
    });
  });
  const disabled = useMemo(() => {
    return (
      _.isEmpty(addTags) &&
      _.isEmpty(clearTags) &&
      (mode === 'add' ? _.isEmpty(addTags) : _.isEmpty(clearTags))
    );
  }, [addTags, clearTags, mode]);
  const { run: submit, loading: submitting } = useRequest(
    async () => {
      if (addTags.length > 0) {
        await tagResourcesPost({
          resourceIds: ids.join(','),
          tagIds: addTags.map((tag) => tag.id).join(','),
          resourceType,
        });
      }
      if (clearTags.length > 0) {
        await tagResourcesDelete({
          resourceIds: ids.join(','),
          tagIds: clearTags.map((tag) => tag.id).join(','),
          resourceType,
        });
      }
      changeVisible(false);
      message.success('操作成功');
      onUpdate();
    },
    {
      manual: true,
    },
  );
  const existTags = useMemo(() => {
    if (loading) {
      return <MiddleSpin />;
    }
    if (clearTagMetas?.length === 0) {
      return (
        <div style={{ width: '100%', height: '100%', overflow: 'hidden', position: 'relative' }}>
          <EmptyView
            description={I18N.t(
              `您当前选中的${getLabelByResourceType(resourceType)}没有包含任何标签`,
            )}
          />
        </div>
      );
    }
    return clearTagMetas?.map((tag) => {
      const checked = clearTags.some((t) => t.id === tag.id);
      return (
        <TagItem
          key={tag.id}
          tag={tag}
          checkable
          checked={checked}
          onChange={(_checked) => {
            if (_checked) {
              setClearTags(_.uniqBy([...clearTags, tag], 'id'));
            } else {
              setClearTags(
                _.uniqBy(
                  clearTags.filter((t) => t.id !== tag.id),
                  'id',
                ),
              );
            }
          }}
        />
      );
    });
  }, [clearTagMetas, clearTags, loading, resourceType]);
  const status = useMemo(() => {
    if (mode === 'clear') {
      if (clearTagMetas?.length === 0) {
        return false;
      }
      // 清除
      return (
        <div>
          <Checkbox
            indeterminate={!!clearTags.length && clearTags.length !== clearTagMetas?.length}
            checked={clearTags.length === clearTagMetas?.length}
            onChange={(e) => {
              setClearTags(e.target.checked ? [...(clearTagMetas || [])] : []);
            }}
          >
            全选
          </Checkbox>
        </div>
      );
    }
    if (addTags.length > 0) {
      return (
        <Typography.Link
          onClick={() => {
            setAddTags([]);
          }}
        >
          清除所选
        </Typography.Link>
      );
    }
    return false;
  }, [addTags.length, clearTagMetas, clearTags.length, mode]);
  const footer = useMemo(() => {
    return (
      <div style={{ width: '100%', display: 'flex', alignItems: 'center', gap: 8 }}>
        <div style={{ flex: 1, overflow: 'hidden', textAlign: 'left' }}>{status}</div>
        <div style={{ display: 'flex', alignItems: 'center', overflow: 'visible' }}>
          <Button
            disabled={disabled}
            loading={submitting}
            type={'primary'}
            onClick={() => {
              submit();
            }}
          >
            确定
          </Button>
          <Button type={'default'} onClick={() => changeVisible(false)}>
            取消
          </Button>
        </div>
      </div>
    );
  }, [disabled, status, submit, submitting]);
  return (
    <DMModal
      footer={footer}
      closable={false}
      width={680}
      title={
        <StyledTitle>
          <Segmented
            onChange={(value) => changeMode(value as 'add' | 'clear')}
            value={mode}
            options={[
              {
                label: (
                  <Badge size={'small'} offset={[12, 0]} overflowCount={99} count={addTags.length}>
                    拟增加的标签
                  </Badge>
                ),
                value: 'add',
              },
              {
                label: (
                  <Badge
                    size={'small'}
                    offset={[12, 0]}
                    overflowCount={99}
                    count={clearTags.length}
                  >
                    拟清空的标签
                  </Badge>
                ),
                value: 'clear',
              },
            ]}
          />
        </StyledTitle>
      }
      open={visible}
      onCancel={() => {
        changeVisible(false);
      }}
      {...modalProps}
    >
      <div
        hidden={mode !== 'add'}
        style={{
          height: 360,
          overflow: 'auto',
          display: 'flex',
          gap: 8,
          flexWrap: 'wrap',
          alignContent: 'flex-start',
        }}
      >
        <AddTagDropdown
          selectable
          tags={addTags}
          onSave={(tagValue) => {
            return tagPost({
              tag: tagValue,
              resourceType,
            }).then((res) => {
              // 自动选中
              if (res?.data) {
                setAddTags(_.uniqBy([...addTags, res.data!], 'id'));
              }
            });
          }}
          onSelect={(tag) => {
            setAddTags(_.uniqBy([...addTags, tag], 'id'));
            // 自动选中
          }}
          resourceType={resourceType}
        />
        {addTags?.map((tag) => (
          <TagItem
            checked
            key={tag.id}
            tag={tag}
            resourceId={tag.id}
            closable
            onClose={(tag) => {
              setAddTags(addTags.filter((t) => t.id !== tag.id));
            }}
          />
        ))}
      </div>
      <div
        hidden={mode !== 'clear'}
        style={{
          height: 360,
          overflow: 'auto',
          display: 'flex',
          gap: 8,
          flexWrap: 'wrap',
          alignContent: 'flex-start',
        }}
      >
        {existTags}
      </div>
    </DMModal>
  );
};
export default TagOperationSelected;
