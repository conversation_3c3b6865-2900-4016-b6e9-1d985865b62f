import I18N from '@/i18n';
import type { HTMLAttributes, ReactNode } from 'react';
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Avatar as AntdAvatar } from 'antd';
import classNames from 'classnames';
import moment from 'moment';
import constants from '@/constants';
import type { AvatarProps } from 'antd/lib/avatar/avatar';
import { getApiUrl, getPortalUrl } from '@/utils/utils';
import styles from './UserAvatarAndName.less';
import { userByUserIdGet } from '@/services/api-Account/UserController';
import Placeholder from './Placeholder';
import defaultAvatar from './images/defaultAvatar.jpg';

interface Props {
  user: {
    id?: number | string;
    avatar?: string;
    nickname?: string;
  };
  showId?: boolean;
  avatarSize?: number;
  avatarProps?: AvatarProps;
}

/**
 * 头像，处理图片加载失败的情况，显示默认头像
 * @param props
 * @constructor
 */
export function Avatar(props: { userId?: number | string; border?: boolean } & AvatarProps) {
  const { src, userId, border, ...otherProps } = props;
  const [isDefaultAvatar, toggle] = useState(true);
  const [avatar, setAvatar] = useState<ReactNode>('');
  const mounted = useRef(false);

  const loadAvatar = (url: ReactNode) => {
    if (typeof url === 'string') {
      let _url = url;
      if (!/^https?:\/\//.test(url)) {
        // 默认头像门户静态资源
        if (constants.defaultAvatar === url && getPortalUrl()) {
          _url = getPortalUrl(url);
        } else if (getApiUrl()) {
          _url = getApiUrl(url);
        }
      }
      const img = new Image();
      img.onload = () => {
        if (mounted.current) {
          setAvatar(_url);
          toggle(false);
        }
      };
      img.src = _url;
    }
  };
  const update = useCallback(() => {
    if (src) {
      // 优先使用 user.avatar
      loadAvatar(src);
    } else if (userId) {
      // 如果没有图像，尝试通过 userId 去获取用户头像
      const avatarUrl = `/api/account/${userId}/avatar?t=${moment().format('YYYYMMDDHHmm')}`;
      loadAvatar(avatarUrl);
    }
  }, [src, userId]);

  useEffect(() => {
    mounted.current = true;
    return () => {
      mounted.current = false;
    };
  }, []);
  useEffect(() => {
    if (mounted.current) {
      update();
    }
  }, [update]);
  return (
    <AntdAvatar
      className={classNames({
        [styles.border]: !isDefaultAvatar && border,
      })}
      src={avatar || defaultAvatar}
      {...otherProps}
    />
  );
}

/**
 * 用户头像、昵称和ID
 * 头像优先使用 user.avatar，如果没有传 avatar ，就使用 user.id 去获取
 * @param props
 * @constructor
 */
const UserAvatarAndName: React.FC<Props & HTMLAttributes<any>> = (props) => {
  const { user, showId, avatarSize = 24, avatarProps, className, ...otherProps } = props;
  let { nickname } = user;
  if (user.id === 0) {
    nickname = I18N.t('系统');
  }

  return (
    <span
      title={nickname}
      className={classNames(styles.userAvatarAndName, className)}
      {...otherProps}
    >
      <Avatar
        size={avatarSize}
        src={user.avatar}
        userId={user.id}
        style={{ flexShrink: 0 }}
        {...avatarProps}
      />

      <span title={nickname} className="nickname">
        {nickname}
      </span>
      {showId && (
        <>
          <span className="user-id" title={`ID：${user.id}`}>
            （
          </span>
          <span className="user-id user-id-value" title={`ID：${user.id}`}>
            {user.id}
          </span>
          <span className="user-id" title={`ID：${user.id}`}>
            ）
          </span>
        </>
      )}
    </span>
  );
};

const userRequestCache: Record<string | number, Promise<any>> = {};
export function useUserCacheById(id?: number) {
  const [user, setUser] = useState<API.UserVo>();
  if (!Number.isNaN(Number(id))) {
    if (!userRequestCache[id]) {
      userRequestCache[id] = userByUserIdGet(
        { userId: id },
        {
          errorHandler(err: any) {
            console.error(err);
          },
        },
      ).then((res) => {
        return res.data!;
      });
    }
    userRequestCache[id].then((res) => {
      if (res) {
        setUser(res);
      }
    });
  }
  return user;
}
export const useUserByCallback = () => {
  return useCallback(async (id: number) => {
    if (!userRequestCache[id]) {
      userRequestCache[id] = userByUserIdGet(
        { userId: id },
        {
          errorHandler(err: any) {
            console.error(err);
          },
        },
      )
        .then((res) => {
          return res.data!;
        })
        .catch((e) => {
          return {
            error: e.message,
            success: false,
          };
        });
    }
    return await userRequestCache[id];
  }, []);
};
export const User = (props: { id: number; size?: number }) => {
  const { id, size = 20 } = props;
  const [user, setUser] = useState<API.UserVo>();
  const userCallback = useUserByCallback();
  useEffect(() => {
    if (!Number.isNaN(Number(id))) {
      userCallback(id).then((res) => {
        setUser(res);
      });
    }
  }, [id, userCallback]);
  if (user) {
    return <UserAvatarAndName user={user} avatarSize={size} />;
  }
  return <Placeholder />;
};

export default UserAvatarAndName;
