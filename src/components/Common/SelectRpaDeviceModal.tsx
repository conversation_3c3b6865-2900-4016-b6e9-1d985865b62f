import I18N from '@/i18n';
import React, { useState } from 'react';
import { Typography } from 'antd';
import DMModal from '@/components/Common/Modal/DMModal';
import { rpaTaskDevicesGet } from '@/services/api-RPAAPI/RpaTaskController';
import { getAppUUID } from '@/utils/ElectronUtils';
import OsPlatform from '@/components/Common/OsPlatform';
import uaParser from 'ua-parser-js';
import { osPlatformMap } from '@/utils/fingerprint/user-agent.util';
import ProTable from '@ant-design/pro-table';
import { scrollProTableOptionFn } from '@/mixins/table';
import _ from 'lodash';
import { useRequest } from '@@/plugin-request/request';
import { Device } from '@/pages/Setting/components/SelectDeviceModal';
import { ghSettingsLoadSettingsGet } from '@/services/api-TKGHAPI/GhSettingsController';

type Props = {
  onSubmit: (data: { concurrent: number; rows: API.RpaRunDeviceVo[] }) => Promise<void>;
  onCancel?: () => void;
  gh?: boolean;
};

/**
 * 选择客户端设备
 * @param props
 * @constructor
 */
const SelectRpaDeviceModal: React.FC<Props> = (props) => {
  const { onSubmit, onCancel, gh } = props;
  const [rows, changeRows] = useState<API.LoginDevice[]>([]);
  const [open, changeOpen] = useState(true);
  const [concurrent, setConcurrent] = useState<number>(5);
  const { run: submit, loading } = useRequest(
    async () => {
      await onSubmit({
        rows,
        concurrent,
      });
      changeOpen(false);
    },
    {
      manual: true,
    },
  );
  return (
    <DMModal
      open={open}
      title={I18N.t('选择客户端设备')}
      width={600}
      okButtonProps={{
        disabled: !rows.length,
      }}
      onCancel={() => {
        changeOpen(false);

        onCancel?.();
      }}
      onOk={submit}
      confirmLoading={loading}
    >
      <div style={{ height: 300, overflow: 'hidden' }}>
        <ProTable<API.RpaRunDeviceVo>
          request={() => {
            let action = () => {
              return rpaTaskDevicesGet({}).then((res) => {
                return res.data!;
              });
            };
            if (gh) {
              action = () =>
                ghSettingsLoadSettingsGet({
                  ghPlatformType: 'TikTok',
                }).then((res) => {
                  return res.data!.devices?.map((item) => {
                    return {
                      ...item,
                      deviceId: item.clientId,
                    };
                  })!;
                });
            }
            return action().then((res) => {
              const list = _.orderBy(
                res?.filter((d) => d.deviceType === 'App'),
                'online',
                'desc',
              );
              const online = list?.find((item) => item.online);
              changeRows(online ? [online!] : []);
              return {
                data: list,
              };
            });
          }}
          size={'small'}
          columns={[
            {
              title: I18N.t('设备名称'),
              dataIndex: 'hostName',
              ellipsis: true,
              render: (dom, record) => <Device data={record} />,
            },
            {
              title: I18N.t('平台'),
              dataIndex: 'userAgent',
              width: 50,
              render: (dom, record) => {
                if (record.appId === getAppUUID()) {
                  return <Typography.Text type="success">{I18N.t('本机')}</Typography.Text>;
                }
                const ua = uaParser(record.userAgent);
                const code = osPlatformMap[ua.os.name!] || 'Win32';
                return <OsPlatform hideName code={code} />;
              },
            },
            {
              title: I18N.t('核数'),
              dataIndex: 'cpus',
              width: 50,
            },
            {
              title: I18N.t('设备标识'),
              dataIndex: 'deviceId',
              width: 125,
              ellipsis: true,
            },
            {
              title: I18N.t('状态'),
              dataIndex: 'online',
              width: 60,
              render(_text, record) {
                const { online } = record;
                if (online) {
                  return <Typography.Text type={'success'}>{I18N.t('在线')}</Typography.Text>;
                }
                return <Typography.Text type={'secondary'}>{I18N.t('离线')}</Typography.Text>;
              },
            },
            {
              title: I18N.t('流程任务卡'),
              dataIndex: 'vouchers',
              width: 125,
              render(_text, record) {
                const { rpaVoucherValid } = record;
                if (rpaVoucherValid) {
                  return <Typography.Text type={'success'}>{I18N.t('已绑定')}</Typography.Text>;
                }
                return <Typography.Text type={'danger'}>{I18N.t('未绑定')}</Typography.Text>;
              },
            },
          ]}
          rowKey="id"
          rowSelection={{
            type: 'radio',
            selectedRowKeys: rows.map((item) => item.id!),
            onChange(ks, rs) {
              changeRows(rs);
              setConcurrent(rs?.[0].cpus! > 4 ? 5 : 2);
            },
          }}
          {...scrollProTableOptionFn({
            pagination: false,
          })}
        />
      </div>
    </DMModal>
  );
};

export default SelectRpaDeviceModal;
