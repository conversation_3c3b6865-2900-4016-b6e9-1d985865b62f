import type { CSSProperties } from 'react';
import { useEffect, useState } from 'react';
import { Tooltip, Typography } from 'antd';
import { SkipErrorNotifyOption } from '@/utils/utils';
import color from '@/style/color.less';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { RESOURCE_NOT_FOUND } from '@/constants/ErrorCode';
import { shopMobileAccountGetGet } from '@/services/api-ShopAPI/MobileAccountController';
import { getMobileAccountIcon } from '@/pages/TikTok/Mine/components/utils';

const MobileAccountByIdCache: Record<number, Promise<API.MobileAccountVo>> = {};

export async function getMobileAccount(id: number) {
  if (!MobileAccountByIdCache[id]) {
    MobileAccountByIdCache[id] = shopMobileAccountGetGet(
      {
        id,
      },
      SkipErrorNotifyOption,
    )
      .then((res) => {
        return res.data!;
      })
      .catch((e) => {
        return {
          code: e?.data?.code,
          id,
          error: true,
          message: e.message,
        };
      });
  }
  return MobileAccountByIdCache[id];
}

export function useMobileAccountById(mobileAccountId: number) {
  const [data, setData] = useState<API.MobileAccountVo>();
  useEffect(() => {
    if (!Number.isNaN(Number(mobileAccountId)) && mobileAccountId) {
      getMobileAccount(mobileAccountId).then((res) => {
        setData(res);
      });
    }
  }, [mobileAccountId]);
  return data;
}

export const MobileAccountNodeById = (props: {
  id: number;
  style?: CSSProperties;
  className?: string;
  iconSize?: number;
  children?: any;
}) => {
  const { id, style, className, iconSize, children = null } = props;
  const data = useMobileAccountById(id);

  if (!data) {
    return <IconFontIcon iconName={'loading_24'} spin style={{ fontSize: iconSize }} />;
  }
  if (data?.error) {
    if (data.code === RESOURCE_NOT_FOUND) {
      // 资源不存在就不显示了
      return children;
    }
    return (
      <Tooltip title={data.message}>
        <IconFontIcon iconName={'baoqian_24'} size={iconSize} color={color.primaryColor} />
      </Tooltip>
    );
  }
  const { username } = data;
  return (
    <div
      className={className}
      style={{
        display: 'flex',
        maxWidth: '100%',
        overflow: 'hidden',
        gap: 4,
        alignItems: 'center',
        ...style,
      }}
    >
      <span style={{ flex: '0 0 16px' }}>{getMobileAccountIcon(data)}</span>
      <Typography.Text ellipsis={{ tooltip: username }} style={{ flex: 1, color: 'inherit' }}>
        {username}
      </Typography.Text>
    </div>
  );
};
export default MobileAccountNodeById;
