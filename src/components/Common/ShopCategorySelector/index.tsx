import I18N from '@/i18n';
/**
 * 经营品类选择器
 */
import { useMemo } from 'react';
import constants from '@/constants';
import AccountCategory from './AccountCategory';
import type { SelectorProps } from '../Selector';
import Selector from '../Selector';

export const CategoryOptions = constants.ShopCategories.map((key) => {
  return {
    label: <AccountCategory category={key} />,
    value: key,
    key,
  };
});
const ShopCategorySelector = (props: SelectorProps) => {
  const { options: notUse, mode, ...others } = props;
  const placeholder = useMemo(() => {
    let text = I18N.t('请选择分身经营品类');
    if (mode === 'multiple') {
      text += `${I18N.t('（可多选）')}`;
    }
    return text;
  }, [mode]);
  return (
    <Selector
      placeholder={placeholder}
      options={CategoryOptions}
      horizontal
      mode={mode}
      {...others}
    />
  );
};
export default ShopCategorySelector;
