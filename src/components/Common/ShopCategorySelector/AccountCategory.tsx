import { Space } from 'antd';
import ColoursIcon from '@/components/Common/ColoursIcon';
import constants from '@/constants';
import styles from './index.less';
import IconFontIcon from '@/components/Common/IconFontIcon';
import classNames from 'classnames';
import { useMemo } from 'react';
import I18N from '@/i18n/I18N';

/**
 * 经营品类
 * @param props
 * @constructor
 */
const AccountCategory = (props: { category?: any; size?: any; showLabel?: any }) => {
  const { category } = props;
  const { showLabel = true, size = '1em' } = props;
  const key = useMemo(() => {
    return constants.ShopCategoryKeys[category] || category;
  }, [category]);
  if (!category) {
    return null;
  }

  return (
    <Space wrap={false} size={4}>
      <IconFontIcon iconName={`${key}_24`} size={size} className={styles.icon} />
      {showLabel && I18N.t(category)}
    </Space>
  );
};

export const ShopCategoryInHouse = (props: {
  category: any;
  style?: {} | undefined;
  className?: '' | undefined;
}) => {
  const { category, style = {}, className = '' } = props;
  return (
    <div
      style={{ width: 78, height: 64, position: 'relative', ...style }}
      className={classNames(className, 'shop-category-in-house')}
    >
      <ColoursIcon style={{ width: '100%', height: '100%' }} className="dianpuxiangqing" />
      <div className={styles.categoryIcon}>
        <AccountCategory category={category} showLabel={false} size="1.5em" />
      </div>
    </div>
  );
};

export default AccountCategory;
