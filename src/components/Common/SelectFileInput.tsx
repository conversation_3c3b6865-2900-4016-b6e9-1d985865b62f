import I18N from '@/i18n';
import { Button, Input, Upload } from 'antd';
import type { RcFile } from 'antd/es/upload';

const SelectFileInput = (props: {
  disabled?: boolean;
  value?: string | undefined;
  text?: '浏览' | undefined;
  onChange: (f: RcFile) => void;
}) => {
  const { disabled, value, text = I18N.t('浏览'), onChange } = props;
  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <div style={{ flex: 1, overflow: 'hidden' }}>
        <Input
          value={value}
          disabled={disabled}
          readOnly
          style={{ borderRight: 'none', borderTopRightRadius: 0, borderBottomRightRadius: 0 }}
        />
      </div>
      <Upload
        showUploadList={false}
        beforeUpload={async (f) => {
          onChange(f);
          return false;
        }}
      >
        <Button
          disabled={disabled}
          type={'primary'}
          style={{ borderTopLeftRadius: 0, borderBottomLeftRadius: 0, width: 70 }}
        >
          {text}
        </Button>
      </Upload>
    </div>
  );
};
export default SelectFileInput;
