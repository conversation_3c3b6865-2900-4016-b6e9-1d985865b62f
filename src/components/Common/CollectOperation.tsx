import I18N from '@/i18n';
import { useCallback, useMemo, useState } from 'react';
import DMModal from '@/components/Common/Modal/DMModal';
import { Radio, Space } from 'antd';
import { DMLoading } from '@/components/Common/DMConfirm';
import pMinDelay from 'p-min-delay';
import type { GhostModalWrapperComponentProps } from '@/mixins/modal';

const CollectOperation = (
  props: GhostModalWrapperComponentProps & {
    onLike: () => Promise<void>;
    onDislike: () => Promise<void>;
    resourceType: API.favoritesDeletePostParams['resourceType'] | 'Video';
    showLoading?: boolean;
  },
) => {
  const { onLike, onDislike, resourceType, showLoading, modalProps } = props;
  const [type, changeType] = useState<'like' | 'dislike'>('like');
  const [visible, changeVisible] = useState(true);
  const label = useMemo(() => {
    if (resourceType === 'GhUser') {
      return I18N.t('用户');
    }
    if (resourceType === 'GhGifter') {
      return I18N.t('刷榜大哥');
    }
    if (resourceType === 'Video') {
      return I18N.t('带货视频');
    }
    return I18N.t('达人');
  }, [resourceType]);

  const submit = useCallback(async () => {
    if (type === 'dislike') {
      changeVisible(false);
      const loadingModal = showLoading
        ? DMLoading({
            title: I18N.t('正在为您批量取消关注，请稍候...'),
          })
        : {
            destroy() {},
          };
      try {
        await pMinDelay(onDislike(), 1000);
        loadingModal.destroy();
      } catch (e) {
        loadingModal.destroy();
      }
    } else {
      const loadingModal = showLoading
        ? DMLoading({
            title: I18N.t('正在为您批量关注，请稍候...'),
          })
        : {
            destroy() {},
          };
      changeVisible(false);
      try {
        await pMinDelay(onLike(), 1000);
        loadingModal.destroy();
      } catch (e) {
        loadingModal.destroy();
      }
    }
  }, [onDislike, onLike, showLoading, type]);
  return (
    <DMModal
      title={I18N.t('批量关注/取消关注')}
      onOk={submit}
      open={visible}
      onCancel={() => {
        changeVisible(false);
      }}
      {...modalProps}
    >
      <Radio.Group
        value={type}
        onChange={(e) => {
          changeType(e.target.value);
        }}
      >
        <Space direction={'vertical'} size={32}>
          <Radio value={'like'}>
            {I18N.t('批量关注所选的{{label}}', {
              label,
            })}
          </Radio>
          <Radio value="dislike">
            {I18N.t('批量取消关注所选的{{label}}', {
              label,
            })}
          </Radio>
        </Space>
      </Radio.Group>
    </DMModal>
  );
};
export default CollectOperation;
