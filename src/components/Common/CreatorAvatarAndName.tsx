import type { HTMLAttributes, ReactNode } from 'react';
import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { Avatar as AntdAvatar } from 'antd';
import classNames from 'classnames';
import constants from '@/constants';
import type { AvatarProps } from 'antd/lib/avatar/avatar';
import { getApiUrl, getPortalUrl } from '@/utils/utils';
import styles from './UserAvatarAndName.less';
import defaultAvatar from './images/defaultAvatar.jpg';
import { getCreatorAvatar } from '@/pages/Creator/utils';

interface Props {
  creator: API.TkshopCreatorDetailVo;
  avatarSize?: number;
  avatarProps?: AvatarProps;
}

/**
 * 头像，处理图片加载失败的情况，显示默认头像
 * @param props
 * @constructor
 */
export function CreatorAvatar(props: { handle?: string; border?: boolean } & AvatarProps) {
  const { src, handle, border, ...otherProps } = props;
  const [isDefaultAvatar, toggle] = useState(true);
  const [avatar, setAvatar] = useState<ReactNode>(constants.defaultAvatar);
  const mounted = useRef(false);

  const loadAvatar = (url: ReactNode) => {
    if (typeof url === 'string') {
      let _url = url;
      if (!/^https?:\/\//.test(url)) {
        // 默认头像门户静态资源
        if (constants.defaultAvatar === url && getPortalUrl()) {
          _url = getPortalUrl(url);
        } else if (getApiUrl()) {
          _url = getApiUrl(url);
        }
      }
      const img = new Image();
      img.onload = () => {
        if (mounted.current) {
          setAvatar(_url);
          toggle(false);
        }
      };
      img.src = _url;
    }
  };
  const update = useCallback(() => {
    if (src) {
      // 优先使用 user.avatar
      loadAvatar(src);
    }
  }, [handle, src]);

  useEffect(() => {
    mounted.current = true;
    return () => {
      mounted.current = false;
    };
  }, []);
  useEffect(() => {
    if (mounted.current) {
      update();
    }
  }, [update]);
  return (
    <AntdAvatar
      className={classNames({
        [styles.border]: !isDefaultAvatar && border,
      })}
      src={avatar || defaultAvatar}
      {...otherProps}
    />
  );
}

/**
 * 用户头像、昵称和ID
 * 头像优先使用 user.avatar，如果没有传 avatar ，就使用 user.id 去获取
 * @param props
 * @constructor
 */
const CreatorAvatarAndName: React.FC<Props & HTMLAttributes<any>> = (props) => {
  const { creator, avatarSize = 20, avatarProps, className, ...otherProps } = props;
  const { handle } = creator;

  const _avatar = useMemo(() => {
    return (
      <CreatorAvatar
        size={avatarSize}
        style={{ minWidth: avatarSize }}
        src={getCreatorAvatar(creator)}
        handle={handle}
        {...avatarProps}
      />
    );
  }, [avatarProps, avatarSize, creator, handle]);
  return (
    <span
      title={handle}
      className={classNames(styles.userAvatarAndName, className)}
      {...otherProps}
    >
      {_avatar}
      <span title={handle} className="nickname">
        {handle}
      </span>
    </span>
  );
};

export default CreatorAvatarAndName;
