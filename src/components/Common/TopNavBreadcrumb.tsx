import { Breadcrumb, Typography } from 'antd';
import { redirectToCurrentTeamPath } from '@/utils/pageUtils';
import React from 'react';

type Item = {
  label: React.ReactNode;
  path?: string;
};

const TopNavBreadcrumb = (props: { items: Item[] }) => {
  const { items } = props;
  return (
    <Breadcrumb separator={<span>&gt;</span>}>
      {items?.map((item, index) => {
        const { path, label } = item;
        return (
          <Breadcrumb.Item key={path || index}>
            <Typography.Link
              onClick={() => {
                if (path) {
                  redirectToCurrentTeamPath(path);
                }
              }}
            >
              {label}
            </Typography.Link>
          </Breadcrumb.Item>
        );
      })}
    </Breadcrumb>
  );
};
export default TopNavBreadcrumb;
