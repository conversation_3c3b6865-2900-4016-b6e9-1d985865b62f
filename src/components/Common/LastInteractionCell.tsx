import { useEffect } from 'react';
import { useState } from 'react';
import { Typography } from 'antd';
import { dateFormat, SkipErrorNotifyOption } from '@/utils/utils';
import Placeholder from '@/components/Common/Placeholder';
import { tkshopLastInteractionGet } from '@/services/api-TKShopAPI/TkshopInteractionController';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { tkshopBuyerLastInteractionGet } from '@/services/api-TKShopAPI/TkshopBuyerController';

const CreatorInteractTypeCache: Record<string, Promise<API.TkshopInteractionDto>> = {};
const BuyerInteractTypeCache: Record<string, Promise<API.TkshopBuyerInteractionDto>> = {};

export function useLastInteraction(id: number, interactType: string, refer: 'creator' | 'buyer') {
  const [data, setData] = useState<API.TkshopInteractionDto | API.TkshopBuyerInteractionDto>();
  const _key = `${id}_${interactType}`;
  useEffect(() => {
    if (id && interactType) {
      if (refer === 'buyer') {
        // 买家
        if (!BuyerInteractTypeCache[_key]) {
          BuyerInteractTypeCache[_key] = tkshopBuyerLastInteractionGet(
            {
              buyerId: id,
              interactType,
            },
            SkipErrorNotifyOption,
          )
            .then((res) => {
              return res.data! || {};
            })
            .catch((e) => {
              return {
                error: true,
                message: e.message,
              } as unknown as any;
            });
        }
        BuyerInteractTypeCache[_key].then((res) => {
          setData(res);
        });
      } else {
        // 达人
        if (!CreatorInteractTypeCache[_key]) {
          CreatorInteractTypeCache[_key] = tkshopLastInteractionGet(
            {
              creatorId: id,
              interactType,
            },
            SkipErrorNotifyOption,
          )
            .then((res) => {
              return res.data! || {};
            })
            .catch((e) => {
              return {
                error: true,
                message: e.message,
              } as unknown as any;
            });
        }
        CreatorInteractTypeCache[_key].then((res) => {
          setData(res);
        });
      }
    }
  }, [_key, id, interactType, refer]);
  return data;
}

export const LastInteractionCell = (props: {
  valuePropName: 'time' | 'message';
  id: number;
  refer: 'buyer' | 'creator';
  interactType: string;
}) => {
  const { valuePropName, id, interactType, refer } = props;
  const lastInteraction = useLastInteraction(id, interactType, refer);
  if (
    interactType.includes('zalo') ||
    interactType.includes('viber') ||
    interactType.includes('fb')
  ) {
    return <Placeholder />;
  }
  if (!lastInteraction) {
    return <IconFontIcon iconName={'loading_24'} />;
  }
  const { interactTime, description } = lastInteraction;
  if (valuePropName === 'time') {
    return interactTime ? (
      <Typography.Text>{dateFormat(interactTime, 'MM-DD HH:mm')}</Typography.Text>
    ) : (
      <Placeholder />
    );
  }
  return description ? (
    <Typography.Text ellipsis={{ tooltip: description }}>{description}</Typography.Text>
  ) : (
    <Placeholder />
  );
};
