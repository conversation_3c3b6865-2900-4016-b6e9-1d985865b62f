import { Button, Col, Dropdown, Row } from 'antd';
import IconFontIcon from '@/components/Common/IconFontIcon';
import I18N from '@/i18n';
import type { CSSProperties, ReactNode } from 'react';
import { useCallback, useMemo } from 'react';

const ColumnItem = (props: { checked: boolean; label: ReactNode; style?: CSSProperties }) => {
  const { checked, label, style = {} } = props;
  const icon = useMemo(() => {
    if (checked) {
      return <IconFontIcon iconName="Check-Circle_24" size={14} />;
    }
    return null;
  }, [checked]);
  return (
    <Row style={{ cursor: 'pointer', ...style }} align="middle" wrap={false}>
      <Col
        flex={1}
        style={{
          overflow: 'hidden',
          textTransform: 'capitalize',
          whiteSpace: 'nowrap',
          color: checked ? '#1890ff' : 'inherit',
        }}
      >
        {label}
      </Col>
      <Col style={{ color: '#1890ff' }}>{icon}</Col>
    </Row>
  );
};

type Props = {
  columns: {
    dataIndex: string;
    title: ReactNode;
    disabled?: boolean;
    [key: string]: any;
  }[];
  selected: string[];
  onChange: (selected: string[]) => void;
  overlayStyle?: CSSProperties;
};
const ColumnDropdown = (props: Props) => {
  const { selected, columns, onChange, overlayStyle = { width: 180 } } = props;
  const _onChange = useCallback(
    (key: string) => {
      const index = selected.indexOf(key);
      if (index === -1) {
        onChange([...selected, key]);
      } else {
        onChange(selected.filter((item) => item !== key));
      }
    },
    [onChange, selected],
  );
  const getTarget = useCallback(
    (key: string) => {
      return columns.find((item) => item.dataIndex === key);
    },
    [columns],
  );
  const items = useMemo(() => {
    return columns.map((item) => {
      const { title, dataIndex, disabled } = item;
      const checked = selected.includes(dataIndex);
      return {
        key: dataIndex,
        label: (
          <ColumnItem
            style={{ cursor: disabled ? 'not-allowed' : 'pointer' }}
            checked={checked}
            label={title}
          />
        ),
        disabled,
      };
    });
  }, [columns, selected]);
  return (
    <Dropdown
      trigger={['click']}
      overlayStyle={overlayStyle}
      menu={{
        items,
        // selectedKeys: selected,
        onClick: (menu) => {
          const { key } = menu;
          const target = getTarget(key);
          if (target) {
            if (!target.disabled) {
              _onChange(key);
            }
          }
        },
      }}
    >
      <Button
        style={{ color: '#666', paddingLeft: 8, paddingRight: 8 }}
        icon={<IconFontIcon iconName="shezhi_24" size={'inherit'} />}
      >
        <span>{I18N.t('字段')}</span>
      </Button>
    </Dropdown>
  );
};
export default ColumnDropdown;
