import { Col, Row, Typography } from 'antd';
import ColoursIcon from '@/components/Common/ColoursIcon';
import Placeholder from '../Common/Placeholder';

const Timezone = (props: { timezone?: string }) => {
  const { timezone } = props;
  return (
    <Row align={'middle'} wrap={false} gutter={4}>
      <Col>
        <ColoursIcon className="Global" />
      </Col>
      <Col style={{ flex: 1, overflow: 'hidden' }}>
        <Typography.Text ellipsis>{timezone || <Placeholder />}</Typography.Text>
      </Col>
    </Row>
  );
};
export default Timezone;
