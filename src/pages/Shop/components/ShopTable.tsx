import I18N from '@/i18n';
import { ProTable } from '@ant-design/pro-table';
import { scrollProTableOptionFn } from '@/mixins/table';
import type { Column } from '@/hooks/useResizableColumns';
import useResizableColumns from '@/hooks/useResizableColumns';
import {
  getShopArea,
  getShopExtensionType,
  getShopLastSyncTime,
  getShopType,
  triggerShopTask,
} from '@/pages/Shop/components/utils';
import CountryIcon from '@/components/Common/CountryIcon';
import { Typography } from 'antd';
import Placeholder from '@/components/Common/Placeholder';
import colors from '@/style/color.less';
import type { ReactNode } from 'react';
import { useMemo } from 'react';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { visitShop } from '@/utils/pageUtils';
import { getTaskShelfJobIcon } from '@/pages/TikTok/Live/components/TaskShelfModal';
import { GhostModalCaller } from '@/mixins/modal';
import InviteByFilterModal from '@/pages/TikTok/components/InviteByFilterModal';
import SendMsgByFilterModal from '@/pages/TikTok/components/SendMsgByFilterModal';
import { history } from '@@/core/history';
import DemandPaymentModal from '@/pages/TikTok/components/DemandPaymentModal';
import { emitReset } from '@/pages/Shop';
import { ResponsiveOptions } from '@/components/Common/MoreDropdown/ResponsiveOptions';

const ShopColumns: Column<API.ShopDetailVo>[] = [
  {
    title: I18N.t('店铺名称'),
    dataIndex: 'name',
    disabled: true,
    render(text, record) {
      const area = record?.platform?.area;
      const name = record.name!;
      return (
        <div
          style={{
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            display: 'flex',
            gap: 4,
            alignItems: 'center',
          }}
        >
          <CountryIcon size={20} country={area} />
          <div
            style={{ flex: 1, overflow: 'hidden', color: colors.primaryColor, cursor: 'pointer' }}
          >
            <Typography.Text style={{ color: 'inherit' }} ellipsis={{ tooltip: name }}>
              {name}
            </Typography.Text>
          </div>
        </div>
      );
    },
    resizable: {
      minWidth: 140,
    },
  },
  {
    title: I18N.t('类型'),
    dataIndex: 'type',
    render(text, record) {
      return getShopType(record);
    },
    width: 70,
  },
  {
    title: I18N.t('站点'),
    dataIndex: 'platform',
    width: 110,
    render(text, record) {
      return getShopArea(record);
    },
  },
  {
    title: I18N.t('浏览器'),
    dataIndex: 'extension',
    width: 180,
    render: (text, record) => {
      return getShopExtensionType(record);
    },
  },
  {
    title: I18N.t('健康状态'),
    dataIndex: 'healthStatus',
    width: 110,
  },
  {
    title: I18N.t('店铺得分'),
    dataIndex: 'shopScore',
    width: 110,
  },
  {
    title: I18N.t('最近更新'),
    dataIndex: 'lastSyncTime',
    width: 110,
    render: (text, record) => {
      return getShopLastSyncTime(record);
    },
  },
  {
    title: I18N.t('备注'),
    dataIndex: 'description',
    resizable: {
      minWidth: 100,
    },
    render: (text, record) => {
      const { description } = record;
      return description ? (
        <Typography.Text ellipsis={{ tooltip: description }}>{description}</Typography.Text>
      ) : (
        <Placeholder />
      );
    },
  },
  {
    title: I18N.t('操作'),
    dataIndex: 'option',
    disabled: true,
    resizable: {
      minWidth: 100,
    },
  },
];
const ShopTable = (props: {
  order: any;
  changeOrder: any;
  dataSource: API.ShopDetailVo[];
  onChange: (reset?: boolean) => void;
  selected: API.ShopDetailVo[];
  onSelect: (rows: API.ShopDetailVo[]) => void;
}) => {
  const { order, changeOrder, onChange, dataSource, selected, onSelect } = props;
  const { columns, header, tableWidth, isInitialized } = useResizableColumns({
    fixWidth: 32,
    columns: ShopColumns,
    scope: 'tkshop_shop_table',
    order,
    changeOrder,
  });
  const tableColumns = useMemo(() => {
    return columns.map((item) => {
      if (item.dataIndex === 'option') {
        return {
          ...item,
          render: (text: ReactNode, record: API.ShopDetailVo) => {
            const { id } = record;
            return (
              <ResponsiveOptions
                gap={8}
                itemWidth={16}
                moreBtnWidth={20}
                items={[
                  {
                    onClick: (e) => {
                      visitShop(id!, 'BROWSER');
                    },
                    label: '访问店铺',
                    icon: <IconFontIcon iconName="fangwendianpu_24" />,
                    key: 'visit',
                  },
                  {
                    onClick: () => {
                      triggerShopTask([record], 'TS_SyncShopInfo');
                    },
                    label: '信息同步',
                    icon: getTaskShelfJobIcon('TS_SyncShopInfo', { iconType: 'iconfont' }),
                    key: 'sync',
                  },
                  {
                    onClick: () => {
                      GhostModalCaller(<InviteByFilterModal shop={record} />);
                    },
                    label: '定向邀约',
                    icon: getTaskShelfJobIcon('TS_TargetPlanByFilter', { iconType: 'iconfont' }),
                    key: 'invite',
                  },
                  {
                    onClick: () => {
                      GhostModalCaller(<SendMsgByFilterModal shop={record} />);
                    },
                    label: '站内消息',
                    icon: getTaskShelfJobIcon('TS_IMChatByFilter', { iconType: 'iconfont' }),
                    key: 'site_message',
                  },
                  {
                    onClick: () => {
                      emitReset();
                      history.push(`/team/${record.teamId!}/sample/${record.id!}`);
                    },
                    label: I18N.t('索样审批'),
                    icon: getTaskShelfJobIcon('TS_SampleApprove', { iconType: 'iconfont' }),
                    key: 'sample',
                  },
                  {
                    onClick: () => {
                      GhostModalCaller(<DemandPaymentModal shop={record} />, 'TS_DemandPayment');
                    },
                    label: I18N.t('未付款订单催付'),
                    icon: <IconFontIcon iconName="xuyaoxufei_24" />,
                    key: 'demandPayment',
                  },
                  {
                    onClick: () => {
                      triggerShopTask([record], 'TS_TargetPlanClear');
                    },
                    label: I18N.t('清理定向邀约计划'),
                    icon: <IconFontIcon iconName="qingchu_24" />,
                    key: 'targetPlanClear',
                  },
                ]}
              />
            );
          },
        };
      }
      return item;
    });
  }, [columns]);
  return (
    <div style={!isInitialized ? { opacity: 0, pointerEvents: 'none' } : {}}>
      <ProTable
        components={{
          header,
        }}
        columns={tableColumns}
        dataSource={dataSource}
        {...scrollProTableOptionFn({
          pagination: false,
          scroll: {
            x: tableWidth,
          },
        })}
        rowSelection={{
          selectedRowKeys: selected.map((item) => item.id!),
          onChange: (selectedRowKeys, rows) => {
            onSelect(rows);
          },
        }}
      />
    </div>
  );
};
export default ShopTable;
