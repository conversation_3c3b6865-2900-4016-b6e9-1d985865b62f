import type { ButtonProps } from 'antd';
import { Select } from 'antd';
import { Form } from 'antd';
import {
  <PERSON><PERSON>c<PERSON>b,
  Button,
  Col,
  Empty,
  Layout,
  Row,
  Space,
  Tooltip,
  Input,
  Typography,
} from 'antd';
import type { CSSProperties } from 'react';
import { useCallback, useEffect, useLayoutEffect, useMemo, useState } from 'react';
import { useLocalStorageState, useThrottleFn } from 'ahooks';
import { GhostModalCaller } from '@/mixins/modal';
import {
  shopByCategoryByCategoryGet,
  shopByShopIdGet,
  shopDeletePost,
} from '@/services/api-ShopAPI/ShopController';
import ColoursIcon from '@/components/Common/ColoursIcon';
import styles from './style.less';
import DMConfirm, { DMLoading } from '@/components/Common/DMConfirm';
import pMinDelay from 'p-min-delay';
import IconFontIcon from '@/components/Common/IconFontIcon';
import classNames from 'classnames';
import { GlobalHeaderAction, visitShop } from '@/utils/pageUtils';
import CountryIcon from '@/components/Common/CountryIcon';
import MiddleSpin from '@/components/Common/MiddleSpin';
import SortDropdown, { useOrder } from '@/components/Sort/SortDropdown';
import LabelRow from '@/components/Common/LabelRow';
import CreateShopGuide from '@/pages/Shop/components/CreateShopGuide';
import TkShopSetting from '@/pages/Shop/components/TkShopSetting';
import { FunctionCodeComponent, useAuthJudgeResult } from '@/components/Common/FunctionButton';
import Functions from '@/constants/Functions';
import { useLocation, history, useRequest } from 'umi';
import { dateFormat, trimValues } from '@/utils/utils';
import constants from '@/constants';
import InviteByFilterModal from '@/pages/TikTok/components/InviteByFilterModal';
import SendMsgByFilterModal from '@/pages/TikTok/components/SendMsgByFilterModal';
import I18N from '@/i18n';
import TkShopTypeSelector from '@/components/Common/Selector/TkShopTypeSelector';
import Placeholder, { OptionPlaceholder } from '@/components/Common/Placeholder';
import { ResponsiveOverflow } from '@/components/Common/MoreDropdown';
import EventEmitter from 'events';
import styled from 'styled-components';
import TkRegionSelector from '@/components/Common/Selector/TkRegionSelector';
import DmPagination from '@/components/DmPagination';
import { getShopStatus, triggerShopTask } from '@/pages/Shop/components/utils';
import { getTaskShelfJobIcon } from '@/pages/TikTok/Live/components/TaskShelfModal';
import DemandPaymentModal from '@/pages/TikTok/components/DemandPaymentModal';
import HelpLink from '@/components/HelpLink';
import { getCurrentTeamId } from '@/hooks/useCurrentTeam';
import ShopTable from '@/pages/Shop/components/ShopTable';
import buttonStyles from '@/style/button.less';

const eventEmitter = new EventEmitter();
export function onReset(fn: any) {
  eventEmitter.on('reset', fn);
}
export function offReset(fn: any) {
  eventEmitter.off('reset', fn);
}
export function emitReset() {
  eventEmitter.emit('reset');
}

const ShopCard = (props: {
  shop: API.ShopDetailVo;
  onUpdate: () => void;
  onSetting: (id: number) => void;
  style?: CSSProperties;
}) => {
  const { shop, onUpdate, onSetting, style } = props;
  const [shopDetail, setShopDetail] = useState(shop);
  const id = useMemo(() => shopDetail.id!, [shopDetail]);
  const { run: refresh, loading } = useRequest(
    () => {
      return shopByShopIdGet({
        shopId: id,
      }).then((res) => {
        setShopDetail(res?.data);
      });
    },
    {
      manual: true,
    },
  );
  const { run: del } = useThrottleFn(() => {
    const confirm = DMConfirm({
      title: I18N.t('确定要删除当前店铺吗？'),
      content: I18N.t('店铺一旦删除，无法恢复，请确认是否继续'),
      async onOk() {
        confirm.destroy();
        const loadingDialog = DMLoading({
          title: '正在为您删除店铺，请稍候...',
        });
        try {
          await pMinDelay(shopDeletePost({ recycled: false }, { ids: [id] }), 1000);
          onUpdate();
          loadingDialog.destroy();
        } catch (error) {
          loadingDialog.destroy();
        }
      },
    });
  });

  const shopType = useMemo(() => {
    const { type } = shopDetail;
    let _shopType = (
      <Space>
        <Typography.Text type={'secondary'}>本土店</Typography.Text>
      </Space>
    );
    if (type === 'Global') {
      _shopType = (
        <Space>
          <Typography.Text type={'secondary'}>跨境店</Typography.Text>
        </Space>
      );
    } else if (type === 'None') {
      _shopType = (
        <Space>
          <Typography.Text type={'secondary'}>未设置</Typography.Text>
        </Space>
      );
    }
    return _shopType;
  }, [shopDetail]);

  const shopStatus = useMemo(() => {
    return getShopStatus(shopDetail);
  }, [shopDetail]);
  const areas = useMemo(() => {
    const { area } = shopDetail?.platform;
    return (
      <Space>
        <CountryIcon size={16} country={area} />
        {constants.Area[area]}
      </Space>
    );
  }, [shopDetail]);

  return (
    <div style={style} className={styles.shopWrap}>
      <div className={styles.shopHeader}>
        <span style={{ flex: '0 0 48px' }}>
          <ColoursIcon className="TikTok_24" size={48} />
        </span>
        <div style={{ flex: 1, overflow: 'hidden' }}>
          <div className={'shop-name'}>
            <Typography.Text ellipsis={{ tooltip: shopDetail.name }}>
              {shopDetail.name}
            </Typography.Text>
          </div>
          <div>
            <Typography.Text ellipsis disabled style={{ fontSize: 12 }}>
              {shopDetail.description}
            </Typography.Text>
          </div>
        </div>
      </div>
      <Row gutter={[12, 12]}>
        <Col span={12}>
          <LabelRow label="店铺类型">{shopType}</LabelRow>
        </Col>
        <Col span={12}>
          <LabelRow label="所属站点">{areas}</LabelRow>
        </Col>
        <Col span={12}>
          <LabelRow label="最近更新">
            {shopDetail.lastSyncTime ? (
              dateFormat(shopDetail.lastSyncTime, 'MM-DD HH:mm')
            ) : (
              <Placeholder />
            )}
          </LabelRow>
        </Col>
        <Col span={12}>
          <LabelRow label="店铺状态">{shopStatus}</LabelRow>
        </Col>
      </Row>
      <div className={styles.actionRow} onClick={(e) => e.stopPropagation()}>
        <ResponsiveOverflow
          itemWidth={110}
          data={[
            {
              node: (_props) => {
                return (
                  <Button
                    icon={<IconFontIcon iconName="fangwendianpu_24" />}
                    onClick={() => visitShop(id!, 'BROWSER')}
                    {..._props}
                  >
                    <span>访问店铺</span>
                  </Button>
                );
              },
              key: 'visit',
            },
            {
              node: (_props) => {
                return (
                  <Button
                    icon={getTaskShelfJobIcon('TS_SyncShopInfo', { iconType: 'iconfont' })}
                    onClick={() => {
                      triggerShopTask([shopDetail], 'TS_SyncShopInfo');
                    }}
                    {..._props}
                  >
                    <span>信息同步</span>
                  </Button>
                );
              },
              key: 'sync',
            },
            {
              key: 'invite',
              node: (_props) => {
                return (
                  <Button
                    icon={getTaskShelfJobIcon('TS_TargetPlanByFilter', { iconType: 'iconfont' })}
                    onClick={() => {
                      GhostModalCaller(<InviteByFilterModal shop={shopDetail} />);
                    }}
                    {..._props}
                  >
                    <span>定向邀约</span>
                  </Button>
                );
              },
            },
            {
              key: 'site_message',
              node: (_props) => {
                return (
                  <Button
                    icon={getTaskShelfJobIcon('TS_IMChatByFilter', { iconType: 'iconfont' })}
                    onClick={() => {
                      GhostModalCaller(<SendMsgByFilterModal shop={shopDetail} />);
                    }}
                    {..._props}
                  >
                    <span>站内消息</span>
                  </Button>
                );
              },
            },
            {
              key: 'sample',
              node: (_props) => {
                return (
                  <Button
                    icon={getTaskShelfJobIcon('TS_SampleApprove', { iconType: 'iconfont' })}
                    onClick={() => {
                      emitReset();
                      history.push(`/team/${shopDetail.teamId!}/sample/${shopDetail.id!}`);
                    }}
                    {..._props}
                  >
                    <span>{I18N.t('索样审批')}</span>
                  </Button>
                );
              },
            },
            {
              key: 'demandPayment',
              node: (_props) => {
                return (
                  <Button
                    icon={<IconFontIcon iconName="xuyaoxufei_24" />}
                    onClick={() => {
                      GhostModalCaller(
                        <DemandPaymentModal shop={shopDetail} />,
                        'TS_DemandPayment',
                      );
                    }}
                    {..._props}
                  >
                    <span>{I18N.t('未付款订单催付')}</span>
                  </Button>
                );
              },
            },
            {
              key: 'targetPlanClear',
              node: (_props) => {
                return (
                  <Button
                    icon={<IconFontIcon iconName="qingchu_24" />}
                    onClick={() => {
                      triggerShopTask([shopDetail], 'TS_TargetPlanClear');
                    }}
                    {..._props}
                  >
                    <span>{I18N.t('清理定向邀约计划')}</span>
                  </Button>
                );
              },
            },
          ]}
        />
      </div>
      <div className={styles.actionCorner}>
        <Tooltip title="刷新店铺状态">
          <Typography.Link
            onClick={() => {
              if (!loading) {
                refresh();
              }
            }}
          >
            <IconFontIcon iconName="shuaxin_24" spin={loading} />
          </Typography.Link>
        </Tooltip>
        <Tooltip title="编辑店铺">
          <FunctionCodeComponent
            code={Functions.SHOP_CONFIG}
            as={'a'}
            onClick={() => {
              onSetting(id);
            }}
          >
            <IconFontIcon iconName="shezhi_24" />
          </FunctionCodeComponent>
        </Tooltip>
        <Tooltip title="删除店铺">
          <FunctionCodeComponent
            code={Functions.SHOP_IMPORT_DELETE}
            as={'a'}
            onClick={() => {
              del();
            }}
          >
            <IconFontIcon iconName="Trash_24" />
          </FunctionCodeComponent>
        </Tooltip>
      </div>
    </div>
  );
};

const StyledLi = styled.div`
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  flex-wrap: nowrap;
  overflow: hidden;
  > span {
    flex: 0 0 20px;
  }
  > div {
    white-space: nowrap;
  }
`;
const Shop = (props: { route: any }) => {
  const { route } = props;
  const { pathname, query } = useLocation();
  const hasAuth = useAuthJudgeResult(Functions.SHOP_IMPORT_DELETE);
  const [pageSize, setPageSize] = useLocalStorageState('tkshop_shop_list_page_size', 150);
  const [current, setCurrent] = useState(1);
  const [total, setTotal] = useState(0);
  const [form] = Form.useForm();
  const [viewType, setViewType] = useLocalStorageState<'Card' | 'List'>(
    `tkshop_shop_list_${getCurrentTeamId()}_view_type`,
    'Card',
  );
  const [selectedShops, setSelectedShops] = useState<API.ShopDetailVo[]>([]);
  const { order, changeOrder } = useOrder(
    {
      key: 'create_time',
      ascend: true,
    },
    'tkshop_list_v20250228',
  );
  const { data, run, loading } = useRequest(
    async () => {
      const { areas, shopType, query: keyword } = trimValues(form.getFieldsValue());
      return shopByCategoryByCategoryGet({
        category: 'all',
        platformTypes: 'TikTok',
        shopType,
        sortFiled: order.key,
        sortOrder: order.ascend ? 'asc' : 'desc',
        areas,
        pageSize,
        pageNum: current,
        query: keyword,
      });
    },
    {
      formatResult(res) {
        setTotal(res.data?.total || 0);
        return res.data?.list || [];
      },

      defaultLoading: true,
      manual: true,
    },
  );
  const _onChange = useCallback(
    (reset?: boolean) => {
      if (!reset) {
        run();
      } else {
        if (current == 1) {
          run();
        } else {
          setCurrent(1);
        }
      }
    },
    [current, run],
  );
  useEffect(() => {
    run();
  }, [order, run, pageSize, current]);
  const showCreateShopGuide = useCallback(() => {
    GhostModalCaller(<CreateShopGuide onUpdate={run} />);
  }, [run]);
  const setting = useCallback(
    (id: number) => {
      GhostModalCaller(<TkShopSetting onUpdate={run} id={id} />);
    },
    [run],
  );
  useLayoutEffect(() => {
    if (data && (query?.id || query.keyword)) {
      history.replace(pathname);
      if (query.id) {
        setting(query.id);
      }
    }
  }, [data, query, pathname, setting]);

  const addCard = useMemo(() => {
    return (
      <div
        className={classNames(styles.shopWrap, styles.add)}
        style={{ flex: '0 0 500px' }}
        onClick={showCreateShopGuide}
      >
        <IconFontIcon iconName="tianjia_24" size={48} />
        <Typography.Link>添加TikTok店铺</Typography.Link>
      </div>
    );
  }, [showCreateShopGuide]);
  const AddButton = useMemo(() => {
    return (ps: ButtonProps) => {
      return (
        <Button {...ps} onClick={showCreateShopGuide}>
          <Space>
            <IconFontIcon iconName={'tianjia_24'} />
            <span>添加TikTok店铺</span>
          </Space>
        </Button>
      );
    };
  }, [showCreateShopGuide]);
  const content = useMemo(() => {
    if (loading) {
      return <MiddleSpin />;
    }
    if (!data?.length) {
      if (!hasAuth) {
        return (
          <Empty
            style={{
              display: 'flex',
              position: 'absolute',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              left: 0,
              top: 0,
              right: 0,
              bottom: 0,
            }}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={'您没有TK店铺的授权，请联络您的团队管理员'}
          />
        );
      }
      return (
        <div
          style={{
            display: 'flex',
            position: 'absolute',
            flexDirection: 'column',
            left: 0,
            top: 0,
            right: 0,
            bottom: 0,
          }}
        >
          <div
            style={{
              display: 'flex',
              flex: 1,
              gap: 48,
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                gap: 24,
                alignContent: 'center',
                alignItems: 'center',
              }}
            >
              <ColoursIcon className={'dianpu_24'} size={84} />
              <AddButton type={'primary'} />
            </div>
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between',
                gap: 16,
              }}
            >
              <Typography.Title level={3}>添加您的TikTok店铺</Typography.Title>
              <div>{I18N.t('在使用 TikTok Shop 达人私域系统之前，您需要：')}</div>
              <StyledLi>
                <span>1.</span>
                <div style={{ flex: 1, overflow: 'hidden' }}>
                  <div>{I18N.t('添加您的 TikTok 店铺')}</div>
                </div>
              </StyledLi>
              <StyledLi>
                <span>2.</span>
                <div>
                  <div>{I18N.t('本土店：请导入您自己的 IP 地址，或者购买平台 IP')}</div>
                </div>
              </StyledLi>
              <StyledLi>
                <span>3.</span>
                <div>
                  <div>{I18N.t('跨境店：可以使用“本机 IP 直连”')}</div>
                </div>
              </StyledLi>
              <StyledLi>
                <span>4.</span>
                <div>
                  <div>{I18N.t('店铺设置好IP隔离后，请打开店铺对应的浏览器，完成登录')}</div>
                </div>
              </StyledLi>
              <div>
                更多信息请参考：
                <HelpLink href={'/tkshop2/buy#create'}>{I18N.t('快速入门')}</HelpLink>
              </div>
            </div>
          </div>
        </div>
      );
    }
    if (viewType === 'List') {
      return (
        <ShopTable
          selected={selectedShops}
          onSelect={setSelectedShops}
          order={order}
          changeOrder={changeOrder}
          dataSource={data}
          onChange={(reset) => {
            _onChange(reset);
          }}
        />
      );
    }
    const list = (data ?? []).map((item) => {
      return (
        <ShopCard
          style={{ flex: '0 0 500px' }}
          key={item.id}
          shop={item}
          onSetting={setting}
          onUpdate={run}
        />
      );
    });
    return (
      <div
        style={{
          padding: 16,
          overflow: 'visible',
          gap: 16,
          flexWrap: 'wrap',
          display: 'flex',
          alignContent: 'flex-start',
        }}
      >
        {list}
        {hasAuth && addCard}
      </div>
    );
  }, [
    AddButton,
    _onChange,
    addCard,
    changeOrder,
    data,
    hasAuth,
    loading,
    order,
    run,
    selectedShops,
    setting,
    viewType,
  ]);
  const sortDropdown = useMemo(() => {
    return (
      <SortDropdown
        list={[
          {
            key: 'create_time',
            label: '创建时间',
          },
          {
            key: 'name',
            label: '店铺名称',
          },
          {
            key: 'type',
            label: '店铺类型',
          },
        ]}
        order={order}
        onChange={changeOrder}
      />
    );
  }, [changeOrder, order]);

  return (
    <Layout.Content className={styles.shopPageContainer}>
      <GlobalHeaderAction.Emit>
        <Space size={32}>
          <Button
            ghost
            style={{ background: 'none' }}
            icon={<IconFontIcon iconName={'shuaxin_24'} />}
            onClick={() => {
              run();
            }}
          >
            <span>{I18N.t('刷新')}</span>
          </Button>
          {hasAuth && <AddButton ghost />}
        </Space>
      </GlobalHeaderAction.Emit>
      <div className={'header'}>
        <Breadcrumb>
          <Breadcrumb.Item>{route.name}</Breadcrumb.Item>
        </Breadcrumb>
        <Form form={form} style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
          <Form.Item noStyle name={'shopType'}>
            <TkShopTypeSelector style={{ width: 130 }} onChange={_onChange} />
          </Form.Item>
          <Form.Item noStyle name={'areas'}>
            <TkRegionSelector
              allowClear
              placeholder={<OptionPlaceholder type={'site'} />}
              valuePropName={'countryEn'}
              style={{ width: 130 }}
              onChange={_onChange}
            />
          </Form.Item>
          <Form.Item noStyle name={'query'} initialValue={query?.keyword || ''}>
            <Input.Search
              placeholder={I18N.t('依据名称或备注检索')}
              onSearch={(v) => {
                form.setFieldValue('query', v);
                _onChange();
              }}
              allowClear
              style={{ width: 200 }}
            />
          </Form.Item>
          <Form.Item noStyle shouldUpdate>
            <Select
              optionLabelProp="icon"
              dropdownMatchSelectWidth={false}
              value={viewType}
              onSelect={(value) => {
                setViewType(value);
              }}
              style={{ width: 68 }}
            >
              <Select.Option value="Card" icon={<IconFontIcon iconName="chanpin_24" />}>
                <Space size={4}>
                  <IconFontIcon iconName="chanpin_24" />
                  {I18N.t('详情')}
                </Space>
              </Select.Option>
              <Select.Option value="List" icon={<IconFontIcon iconName="liebiaoxingshi_24" />}>
                <Space size={4}>
                  <IconFontIcon iconName="liebiaoxingshi_24" />
                  {I18N.t('列表')}
                </Space>
              </Select.Option>
            </Select>
          </Form.Item>
          {sortDropdown}
        </Form>
      </div>
      <div className={'main'}>{content}</div>
      <div className={'footer'}>
        {viewType === 'List' ? (
          <Tooltip
            placement={'topLeft'}
            title={selectedShops.length === 0 ? '请至少选择一个店铺' : false}
          >
            <Space>
              <Button
                type={'primary'}
                ghost
                icon={<IconFontIcon iconName="fangwendianpu_24" />}
                className={selectedShops.length === 0 ? buttonStyles.disabled : ''}
                onClick={async () => {
                  if (selectedShops.length === 0) {
                    return;
                  }
                  if (selectedShops.length > 60) {
                    DMConfirm({
                      width: 460,
                      type: 'info',
                      title: I18N.t('一次最多打开60个分身'),
                      content: I18N.t('请重新选择您要打开的分身'),
                    });
                    return;
                  }
                  // 批量打开会话
                  // openSessions(selectedShopIds);
                }}
              >
                <span>{I18N.t('访问店铺')}</span>
              </Button>
            </Space>
          </Tooltip>
        ) : (
          <span />
        )}
        <DmPagination
          loading={loading}
          onChange={(p, s) => {
            setCurrent(p);
            setPageSize(s);
          }}
          pageSize={pageSize}
          total={total}
          current={current}
        />
      </div>
      {/*<EntryStep />*/}
    </Layout.Content>
  );
};
export default Shop;
