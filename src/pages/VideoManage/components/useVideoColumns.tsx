import { useCallback, useEffect, useMemo, useState } from 'react';
import type { ProColumnType } from '@ant-design/pro-table';
import SortDropdown, { useOrder } from '@/components/Sort/SortDropdown';
import _ from 'lodash';
import { getColumnByDataIndex } from '@/components/SortableTransfer/TableColumnsSettingModalTrigger';
import Placeholder from '@/components/Common/Placeholder';
import useResizableColumns from '@/hooks/useResizableColumns';
import { Button, Checkbox, Form, message, Space, Tooltip, Typography } from 'antd';
import I18N from '@/i18n';
import { dateFormat, SkipErrorNotifyOption } from '@/utils/utils';
import IconFontIcon from '@/components/Common/IconFontIcon';
import type { VideoColumn } from '@/pages/VideoManage/components/useVideoColumnsMeta';
import { useVideoColumnsMeta } from '@/pages/VideoManage/components/useVideoColumnsMeta';
import { useProductsCallback, setOrderAdvanceSearchValues } from '@/pages/Order/components/utils';
import CreatorHandleCell from '@/pages/TikTok/Live/components/CreatorHandleCell';
import { getAchievedShops, getNumberDom } from '@/pages/TikTok/utils/utils';
import { useVideoDetailModal } from '@/pages/VideoManage/components/VideoDetailModal';
import { history } from '@@/core/history';
import FallbackImage from '@/components/FallbackImage';
import useTkTeamStatusRequest from '@/hooks/useTkShopTeam';
import {
  tkshopVideoCancelSyncPost,
  tkshopVideoMarkSyncPost,
  tkshopVideoPagePost,
} from '@/services/api-TKShopAPI/TkshopVideoController';
import DMModal from '@/components/Common/Modal/DMModal';
import DMFormItem, { DMFormItemContext } from '@/components/Common/DMFormItem';
import type { GhostModalWrapperComponentProps } from '@/mixins/modal';
import { GhostModalCaller } from '@/mixins/modal';
import HelpLink from '@/components/HelpLink';
import buttonStyles from '@/style/button.less';
import { useRequest } from '@@/plugin-request/request';
import { QUOTA_EXCEEDED } from '@/constants/ErrorCode';
import DMConfirm from '@/components/Common/DMConfirm';
import { getCurrentTeamId } from '@/hooks/useCurrentTeam';
import ROI from '@/pages/TikTok/Live/components/ROI';
import CopyableText from '@/components/Common/CopyableText';
import VideoStatChartModal from '@/pages/VideoManage/components/VideoStatChartModal';
import ConversionRateInteractiveComponent from './ConversionRate';
import { transformOrderType } from '@/pages/Order/components/OrderAdvanceSearchModal';

function setNotAlertStorage(teamId: any) {
  localStorage.setItem(`video-auto-sync-not-alert-${teamId}`, 'Y');
}
function getNotAlertStorage(teamId: any) {
  return localStorage.getItem(`video-auto-sync-not-alert-${teamId}`) || 'N';
}

type VideoAutoSyncResultModalProps = {
  onSubmit?: () => Promise<void>;
  onSearch: () => void;
  count: number;
  quota: number;
  used: number;
  onCancel?: () => void;
};
const VideoAutoSyncResultModal = (
  props: GhostModalWrapperComponentProps & VideoAutoSyncResultModalProps,
) => {
  const { onSubmit, quota, used, modalProps, onSearch, count, onCancel } = props;
  const [open, setOpen] = useState(true);
  const [form] = Form.useForm();
  const [checked, setChecked] = useState(false);
  const [forceSync, setForceSync] = useState(true);
  const { run: submit, loading } = useRequest(
    async () => {
      if (onSubmit) {
        await onSubmit();
        setOpen(false);
      }
    },
    {
      manual: true,
      refreshDeps: [checked],
    },
  );
  useEffect(() => {
    if (!open) {
      if (checked) {
        setNotAlertStorage(getCurrentTeamId());
      }
    }
  }, [checked, open]);
  const footer = useMemo(() => {
    if (!!onSubmit) {
      return (
        <Space>
          <Button
            className={buttonStyles.successBtn}
            onClick={() => {
              onSearch();
              setOpen(false);
            }}
          >
            查看已关注的视频
          </Button>
          <Button type={'primary'} disabled={!forceSync} onClick={submit} loading={loading}>
            确定
          </Button>
          <Button
            onClick={() => {
              setOpen(false);
              onCancel?.();
            }}
          >
            取消
          </Button>
        </Space>
      );
    }
    return (
      <Space>
        <Button
          className={buttonStyles.successBtn}
          onClick={() => {
            onSearch();
            setOpen(false);
          }}
        >
          查看已关注的视频
        </Button>
        <Button
          type={'primary'}
          onClick={() => {
            setOpen(false);
            onCancel?.();
          }}
        >
          知道了
        </Button>
      </Space>
    );
  }, [forceSync, loading, onCancel, onSearch, onSubmit, submit]);
  const title = useMemo(() => {
    if (onSubmit) {
      const remainQuota = quota - used;
      if (remainQuota <= 0) {
        return '可关注的视频配额已用完';
      }
      return `可关注的视频配额不够`;
    }
    return '视频关注成功';
  }, [onSubmit, quota, used]);
  return (
    <DMModal
      headless
      footer={footer}
      bodyStyle={{
        paddingBottom: 0,
      }}
      width={600}
      open={open}
      onCancel={() => {
        setOpen(false);
      }}
      {...modalProps}
    >
      <div style={{ display: 'flex', alignItems: 'flex-start', gap: 24, overflow: 'hidden' }}>
        <Typography.Link>
          <IconFontIcon iconName={'info_241'} size={48} />
        </Typography.Link>
        <div style={{ display: 'flex', flex: 1, flexDirection: 'column', gap: 12 }}>
          {title}
          <Form requiredMark={false} form={form}>
            <DMFormItemContext.Provider value={{ disableLabelMuted: false, labelWidth: 170 }}>
              <DMFormItem style={{ marginBottom: 12 }} shouldUpdate>
                {() => {
                  if (!onSubmit) {
                    return (
                      <Typography.Text type={'secondary'}>
                        关注后的视频将被纳入到自动更新体系
                        <HelpLink style={{ marginLeft: 16 }} href={'/help/tkshop2/videos'} />
                      </Typography.Text>
                    );
                  }
                  return (
                    <div>
                      <Checkbox
                        checked={forceSync}
                        onChange={(e) => {
                          setForceSync(e.target.checked);
                        }}
                      >
                        强制关注
                        {count === 1 ? '此' : ` ${Math.min(count, quota).toLocaleString()} 个`}
                        视频
                      </Checkbox>
                      <Typography.Text style={{ marginLeft: 16 }} type={'secondary'}>
                        （从关注列表中去除最早关注的视频）
                      </Typography.Text>
                    </div>
                  );
                }}
              </DMFormItem>
              <DMFormItem
                style={{ marginBottom: 12 }}
                label={I18N.t('特别关注的视频配额')}
                shouldUpdate
              >
                {() => {
                  if (quota === -1 || _.isNil(quota)) {
                    return '不限';
                  }
                  return `配额 ${quota}（已用 ${used}）`;
                }}
              </DMFormItem>
              <DMFormItem style={{ marginBottom: 12 }} label="特别关注视频的更新频率">
                每 24 小时更新 1 次
              </DMFormItem>
              {!onSubmit && (
                <Form.Item noStyle shouldUpdate>
                  <Checkbox
                    checked={checked}
                    onChange={(e) => {
                      setChecked(e.target.checked);
                    }}
                  >
                    {I18N.t('不再提示')}
                  </Checkbox>
                </Form.Item>
              )}
            </DMFormItemContext.Provider>
          </Form>
        </div>
      </div>
    </DMModal>
  );
};

function getUsedQuota() {
  return tkshopVideoPagePost({
    autoSync: true,
    pageSize: 1,
    pageNum: 1,
  }).then((res) => {
    return res?.data?.total || 0;
  });
}

export function useVideoAutoSyncCallback(
  onUpdate: (reset?: boolean) => void,
  onSearch: () => void,
) {
  const { data } = useTkTeamStatusRequest();
  const quota = data?.syncVideoQuota;

  return useCallback(
    async (ids: number[], checked: boolean) => {
      return new Promise<void>((resolve, reject) => {
        if (!ids?.length) {
          resolve();
          return;
        }
        const search = () => {
          onSearch();
          resolve();
        };
        if (checked) {
          tkshopVideoMarkSyncPost(
            {
              force: false,
            },
            {
              ids,
            },
            SkipErrorNotifyOption,
          )
            .then(async () => {
              onUpdate();
              const used_quota = await getUsedQuota();
              if (getNotAlertStorage(getCurrentTeamId()) === 'Y') {
                message.success(`关注成功（配额${quota}，已用${used_quota}）`);
              } else {
                GhostModalCaller(
                  <VideoAutoSyncResultModal
                    onSearch={search}
                    used={used_quota}
                    count={ids.length}
                    onCancel={reject}
                    quota={quota}
                  />,
                );
              }

              resolve();
            })
            .catch(async (e) => {
              const used_quota = await getUsedQuota();
              if (e.data.code === QUOTA_EXCEEDED) {
                // 配额溢出
                GhostModalCaller(
                  <VideoAutoSyncResultModal
                    onSearch={search}
                    count={ids.length}
                    used={used_quota}
                    onCancel={reject}
                    quota={quota}
                    onSubmit={async () => {
                      await tkshopVideoMarkSyncPost(
                        {
                          force: true,
                        },
                        {
                          ids,
                        },
                      )
                        .then(async () => {
                          onUpdate();
                          const used = await getUsedQuota();
                          if (getNotAlertStorage(getCurrentTeamId()) === 'Y') {
                            message.success(`关注成功（配额${quota}，已用${used}）`);
                          } else {
                            GhostModalCaller(
                              <VideoAutoSyncResultModal
                                onSearch={search}
                                used={used}
                                onCancel={reject}
                                count={ids.length}
                                quota={quota}
                              />,
                            );
                          }
                          resolve();
                        })
                        .catch(reject);
                    }}
                  />,
                );
              } else {
                message.error(e.message);
                reject();
              }
            });
        } else {
          tkshopVideoCancelSyncPost({ ids })
            .then(async () => {
              const used = await getUsedQuota();
              message.success(`取消关注成功（配额${quota}，已用${used}）`);
              resolve();
              onUpdate();
            })
            .catch(reject);
        }
      });
    },
    [onSearch, onUpdate, quota],
  );
}

function useVideoColumns(onUpdate: (reset?: boolean) => void, onSearch: () => void) {
  const { order, changeOrder } = useOrder(
    {
      key: 'lastSyncTime',
      ascend: false,
    },
    `tkshop_video_manage_order_20250709`,
  );
  const { columns: activeColumns, meta, update } = useVideoColumnsMeta();
  const { run: openVideoDetailModal } = useVideoDetailModal(() => {
    onUpdate(false);
  });
  const _addSyncCallback = useVideoAutoSyncCallback(onUpdate, onSearch);
  const { run: addSyncRequest, loading } = useRequest(
    async (ids: number[], checked: boolean) => {
      if (loading) {
        return;
      }
      await _addSyncCallback(ids, checked);
    },
    {
      manual: true,
    },
  );
  const getProductsDom = useProductsCallback({
    placeholder: (
      <Typography.Link
        onClick={() => {
          DMConfirm({
            width: 560,
            type: 'info',
            title: I18N.t('无法获取该视频的带货商品'),
            content: I18N.t('只有当产生明确的订单后，才能够知晓具体的带货商品'),
          });
        }}
      >
        {I18N.t('未知')}
      </Typography.Link>
    ),
  });
  const getColumn = useCallback(
    (
      dataIndex: VideoColumn,
    ): ProColumnType<API.TkshopVideoVo> & {
      sortable?: { order?: number };
      description?: string;
    } => {
      const target = getColumnByDataIndex(dataIndex, meta) || { title: dataIndex };
      const { title, description, sortable, resizable, width } = target;
      const base = {
        dataIndex,
        title,
        description,
        ellipsis: false,
        resizable,
        sortable,
        width,
      };
      switch (dataIndex) {
        case 'mediaAvatar': {
          return {
            ...base,
            render(_text, record) {
              return (
                <FallbackImage
                  src={record.mediaAvatar}
                  size={[30, 30]}
                  onPreview={() => {
                    openVideoDetailModal(record);
                  }}
                />
              );
            },
          };
        }
        case 'mediaName': {
          return {
            ...base,
            render(_dom, record) {
              const { mediaName } = record;
              return (
                <Typography.Link
                  ellipsis
                  target={'_blank'}
                  onClick={() => {
                    openVideoDetailModal(record);
                  }}
                >
                  {mediaName || I18N.t('未命名')}
                </Typography.Link>
              );
            },
          };
        }
        case 'products': {
          return {
            ...base,
            render(_dom, record) {
              return getProductsDom(record.products!);
            },
          };
        }
        case 'viewCnt':
        case 'likeCnt':
        case 'commentCnt':
        case 'favoriteCnt':
        case 'retweetCnt': {
          return {
            ...base,
            render(_dom, record) {
              const _value = record[dataIndex];
              const { lastSyncTime } = record;
              const inner = getNumberDom(_value);
              if (dataIndex === 'viewCnt') {
                return (
                  <Tooltip
                    title={
                      <div>
                        <div style={{ lineHeight: '30px' }}>
                          最近更新时间 :{' '}
                          {lastSyncTime ? dateFormat(lastSyncTime, 'MM-DD HH:mm') : '--'}
                        </div>
                        <div style={{}}>
                          <HelpLink
                            style={{ color: 'white', textDecoration: 'underline' }}
                            href="/tkshop2/videos"
                          >
                            <span>了解如何更新</span>
                          </HelpLink>
                        </div>
                      </div>
                    }
                  >
                    <Typography.Link ellipsis>{_.isNil(_value) ? '--' : inner}</Typography.Link>
                  </Tooltip>
                );
              }
              return inner;
            },
          };
        }
        case 'itemsSold': {
          return {
            ...base,
            render(_dom, record) {
              const { itemsSold } = record;
              if (!_.isNil(itemsSold)) {
                const inner = getNumberDom(itemsSold);
                return (
                  <Typography.Link
                    ellipsis
                    onClick={() => {
                      setOrderAdvanceSearchValues({
                        mediaId: record.mediaId,
                      });
                    }}
                  >
                    {inner}
                  </Typography.Link>
                );
              }
              return <Placeholder />;
            },
          };
        }
        case 'ordersPm': {
          return {
            ...base,
            render(_dom, record) {
              return <ConversionRateInteractiveComponent video={record} />;
            },
          };
        }
        case 'orderCount': {
          return {
            ...base,
            render(_dom, record) {
              const { orderCount } = record;
              if (!_.isNil(orderCount)) {
                const inner = getNumberDom(orderCount);
                return (
                  <Typography.Link
                    ellipsis
                    onClick={() => {
                      setOrderAdvanceSearchValues({
                        mediaId: record.mediaId,
                      });
                    }}
                  >
                    {inner}
                  </Typography.Link>
                );
              }
              return <Placeholder />;
            },
          };
        }
        case 'gmv': {
          return {
            ...base,
            render(_dom, record) {
              const { gmv, unit } = record;
              if (!_.isNil(gmv)) {
                const inner = getNumberDom(gmv, unit);
                return (
                  <Typography.Link
                    ellipsis
                    onClick={() => {
                      setOrderAdvanceSearchValues({
                        mediaId: record?.mediaId,
                      });
                    }}
                  >
                    {inner}
                  </Typography.Link>
                );
              }
              return <Placeholder />;
            },
          };
        }
        case 'estCommission': {
          return {
            ...base,
            render(_dom, record) {
              const { estCommission, unit } = record;
              if (!_.isNil(estCommission)) {
                const inner = getNumberDom(estCommission, unit);
                return (
                  <Typography.Link
                    ellipsis
                    onClick={() => {
                      setOrderAdvanceSearchValues({
                        mediaId: record?.mediaId,
                      });
                    }}
                  >
                    {inner}
                  </Typography.Link>
                );
              }
              return <Placeholder />;
            },
          };
        }
        case 'remark': {
          return {
            ...base,
            render(_dom, record) {
              const { remark } = record;
              return remark ? (
                <Typography.Text ellipsis={{ tooltip: remark }}>{remark}</Typography.Text>
              ) : (
                <Placeholder />
              );
            },
          };
        }
        case 'lastSyncTime': {
          return {
            ...base,
            render(_dom, record) {
              const time = record[dataIndex];
              return time ? (
                <Tooltip title={dateFormat(time)}>{dateFormat(time, 'MM-DD HH:mm')}</Tooltip>
              ) : (
                <Placeholder />
              );
            },
          };
        }
        case 'postTime': {
          return {
            ...base,
            render(_dom, record) {
              const time = record[dataIndex];
              return time ? (
                <Tooltip title={dateFormat(time)}>{dateFormat(time, 'YYYY-MM-DD')}</Tooltip>
              ) : (
                <Placeholder />
              );
            },
          };
        }
        case 'sampleRequestApplyIds': {
          return {
            ...base,
            render(_dom, record) {
              const { sampleRequestApplyIds } = record;
              if (!sampleRequestApplyIds?.length) {
                return <Placeholder />;
              }
              const inner = getNumberDom(sampleRequestApplyIds.length);
              return (
                <Typography.Link
                  ellipsis
                  onClick={() => {
                    setOrderAdvanceSearchValues({
                      mediaId: record.mediaId,
                      ...transformOrderType('sample_request'),
                    });
                  }}
                >
                  {inner}
                </Typography.Link>
              );
            },
          };
        }
        case 'option': {
          return {
            ...base,
            render(_dom, record) {
              const { autoSync } = record;
              return (
                <Space>
                  <Tooltip title={I18N.t('热力趋势')}>
                    <Typography.Link
                      onClick={() => {
                        GhostModalCaller(
                          <VideoStatChartModal
                            data={record}
                            onUpdate={() => {
                              onUpdate(false);
                            }}
                          />,
                        );
                      }}
                    >
                      <IconFontIcon iconName={'shujuqushi_24'} />
                    </Typography.Link>
                  </Tooltip>
                  <Tooltip title={autoSync ? '取消关注此视频' : '关注此视频'}>
                    <Typography.Link
                      onClick={() => {
                        addSyncRequest([record.id!], !autoSync);
                      }}
                    >
                      <IconFontIcon iconName={autoSync ? 'yishoucang_24' : 'shoucang_24'} />
                    </Typography.Link>
                  </Tooltip>
                </Space>
              );
            },
          };
        }
        case 'handle': {
          return {
            ...base,
            render(_dom, record) {
              const { creator } = record;
              if (!creator) {
                return <Placeholder />;
              }
              return <CreatorHandleCell refer={'store'} creator={creator!} />;
            },
          };
        }
        case 'creator_total_gmv': {
          return {
            ...base,
            render(_dom, record) {
              const { creator } = record;
              if (!creator) {
                return <Placeholder />;
              }
              return getNumberDom(creator.totalGmv, creator.unit);
            },
          };
        }
        case 'creator_follower_cnt': {
          return {
            ...base,
            render(_dom, record) {
              const { creator } = record;
              if (!creator) {
                return <Placeholder />;
              }
              return getNumberDom(creator.followerCnt);
            },
          };
        }
        case 'creator_roi': {
          return {
            ...base,
            render(_dom, record) {
              const { creator } = record;
              if (!creator) {
                return <Placeholder />;
              }
              return <ROI value={creator.roi} />;
            },
          };
        }
        case 'mediaId': {
          return {
            ...base,
            render(_dom, record) {
              const { mediaId } = record;
              return (
                <Typography.Text
                  ellipsis={{
                    tooltip: mediaId,
                    onEllipsis() {
                      // hack
                    },
                  }}
                >
                  <CopyableText text={mediaId} type={I18N.t('媒体ID')}>
                    {mediaId}
                  </CopyableText>
                </Typography.Text>
              );
            },
          };
        }
        case 'creator_remark': {
          return {
            ...base,
            render(_dom, record) {
              const { creator } = record;
              if (!creator) {
                return <Placeholder />;
              }
              return (
                <Typography.Text
                  ellipsis={{
                    tooltip: creator.remark,
                    onEllipsis() {
                      // hack
                    },
                  }}
                >
                  {creator.remark}
                </Typography.Text>
              );
            },
          };
        }
        case 'shopIds':
          return {
            ...base,
            render(_text, record) {
              const { shopIds } = record;
              return getAchievedShops({
                shopIds,
                onClick(shop) {
                  if (shop) {
                    history.push(`/team/${getCurrentTeamId()}/shop?keyword=${shop.name}`);
                  }
                },
              });
            },
          };
        case 'adCode': {
          return {
            ...base,
            render(_text, record) {
              const { adCode } = record;
              return adCode ? (
                <CopyableText type={I18N.t('投流码')} text={adCode}>
                  <Typography.Text
                    ellipsis={{
                      tooltip: {
                        title: adCode,
                      },
                    }}
                  >
                    {adCode}
                  </Typography.Text>
                </CopyableText>
              ) : (
                <Placeholder />
              );
            },
          };
        }
        default:
          return {
            ...base,
            render(_text, record) {
              if (!record) {
                return <Placeholder />;
              }
              const _inner = record[dataIndex];
              if (_.isNil(_inner)) {
                return <Placeholder />;
              }
              return (
                <Typography.Text
                  ellipsis={{
                    tooltip: _inner,
                  }}
                >
                  {_inner}
                </Typography.Text>
              );
            },
          };
      }
    },
    [addSyncRequest, getProductsDom, meta, openVideoDetailModal],
  );
  const keysShow: VideoColumn[] = useMemo(() => {
    return activeColumns.filter((key) => {
      return _.findIndex(meta, (i) => i.dataIndex === key) !== -1;
    });
  }, [meta, activeColumns]);
  const _columns = useMemo(() => {
    return keysShow.map((key) => {
      return getColumn(key);
    });
  }, [getColumn, keysShow]);
  const sorter = useMemo(() => {
    const sorts = meta
      .filter((i) => {
        return !!i.sortable;
      })
      .map((i) => {
        return {
          key: i.dataIndex as unknown as string,
          label: i.title as unknown as string,
        };
      });
    return <SortDropdown list={sorts} order={order} onChange={changeOrder} />;
  }, [meta, order, changeOrder]);
  const { columns, tableWidth, header, isInitialized } = useResizableColumns({
    fixWidth: 32,
    columns: _columns,
    order,
    changeOrder,
    scope: 'tkshop_Video_Manage',
  });
  return {
    columns,
    tableWidth,
    header,
    sorter,
    activeColumns,
    getColumn,
    meta,
    update,
    isInitialized,
    order,
    getSortParams() {
      return {
        sortField: order.key,
        sortOrder: order.ascend ? 'asc' : 'desc',
      };
    },
    changeOrder,
  };
}
export default useVideoColumns;
