import I18N from '@/i18n';
import { useCallback, useEffect, useMemo, useState } from 'react';
import EventEmitter from 'events';
import _ from 'lodash';
import { getCurrentTeamId } from '@/hooks/useCurrentTeam';

const eventEmitter = new EventEmitter();

export type VideoColumn =
  | keyof API.TkshopVideoVo
  | 'option'
  | 'handle'
  | 'creator_follower_cnt'
  | 'creator_total_gmv'
  | 'creator_roi'
  | 'creator_remark';

const DEFAULT_COLUMNS: VideoColumn[] = [
  'mediaAvatar',
  'mediaName',
  'handle',
  'adCode',
  'shopIds',
  'products',
  'viewCnt',
  'orderCount',
  'gmv',
  'ordersPm',
  'postTime',
  'lastSyncTime',
  'option',
];

export function useVideoColumnsMeta() {
  const storage_key = `tk_video_list_columns_${getCurrentTeamId()}_V20250804`;
  const [columns, setColumns] = useState<VideoColumn[]>(() => {
    let _columns = DEFAULT_COLUMNS;
    try {
      if (localStorage.getItem(storage_key)) {
        _columns = JSON.parse(
          localStorage.getItem(storage_key) || '[]',
        ) as unknown as VideoColumn[];
      }
    } catch (e) {
      console.log(e);
    }
    return _columns;
  });
  const meta = useMemo(() => {
    return [
      {
        title: '截图',
        dataIndex: 'mediaAvatar',
        disabled: true,
        fixed: true,
        width: 50,
        description: '视频截图',
      },

      {
        title: '标题',
        resizable: {
          minWidth: 140,
        },
        sortable: true,
        alwaysShow: true,
        dataIndex: 'mediaName',
        description: '视频标题',
      },
      {
        title: '发布时间',
        width: 100,
        sortable: true,
        dataIndex: 'postTime',
        description: '视频的发布时间',
      },
      {
        title: '更新时间',
        width: 100,
        sortable: true,
        dataIndex: 'lastSyncTime',
        description: '更新此视频播放量、点赞量等媒体信息的时间',
      },
      {
        title: I18N.t('达人'),
        dataIndex: 'handle',
        resizable: {
          minWidth: 140,
        },
        sortable: {},
        description: '发布视频的达人ID',
      },
      {
        title: I18N.t('合作店铺'),
        dataIndex: 'shopIds',
        resizable: {
          minWidth: 140,
        },
        description: '挂车商品所属店铺',
      },
      {
        title: I18N.t('带货商品'),
        dataIndex: 'products',
        resizable: {
          minWidth: 140,
        },
        description: '挂车商品',
      },
      {
        title: I18N.t('索样记录'),
        dataIndex: 'sampleRequestApplyIds',
        resizable: {
          minWidth: 140,
        },
        description: '和此视频相关的索样记录（可以为空）',
      },
      {
        title: I18N.t('播放量'),
        dataIndex: 'viewCnt',
        sortable: true,
        width: 100,
        ellipsis: true,
        description: '视频最新播放量',
      },
      {
        title: I18N.t('点赞量'),
        dataIndex: 'likeCnt',
        sortable: true,
        width: 100,
        description: '视频最新点赞量',
      },
      {
        title: I18N.t('千次播放转化率'),
        dataIndex: 'ordersPm',
        sortable: true,
        width: 130,
        description:
          '此视频每千次播放产生的订单数量，0.001代表1百万的播放量产生了1笔订单，数字越高代表视频越有投流价值',
      },
      {
        title: I18N.t('评论量'),
        dataIndex: 'commentCnt',
        width: 100,
        sortable: true,
        description: '视频最新评论量',
      },
      {
        title: I18N.t('收藏量'),
        dataIndex: 'favoriteCnt',
        width: 100,
        sortable: true,
        description: '视频最新收藏量',
      },
      {
        title: I18N.t('转发量'),
        width: 100,
        sortable: true,
        dataIndex: 'retweetCnt',
        description: '视频最新转发量',
      },
      {
        title: I18N.t('投流码'),
        dataIndex: 'adCode',
        sortable: true,
        resizable: {
          minWidth: 130,
        },
        description: '视频投流授权码',
      },
      {
        title: I18N.t('商品售出数量'),
        width: 120,
        sortable: true,
        dataIndex: 'itemsSold',
        description: '此视频产生的商品售出总数量',
      },
      {
        title: I18N.t('带货订单数'),
        width: 120,
        sortable: true,
        dataIndex: 'orderCount',
        description: '为我带货的订单数量',
      },
      {
        title: I18N.t('商品售出金额'),
        width: 120,
        sortable: true,
        dataIndex: 'gmv',
        description: '此视频产生的商品售出总金额',
      },
      {
        title: I18N.t('佣金'),
        width: 100,
        sortable: true,
        dataIndex: 'estCommission',
        description: '此视频产生的商品售出总佣金',
      },
      {
        title: I18N.t('媒体ID'),
        width: 190,
        dataIndex: 'mediaId',
        description: '视频的唯一标识ID',
      },
      {
        title: I18N.t('达人粉丝数'),
        width: 100,
        dataIndex: 'creator_follower_cnt',
        description: '发布此视频的达人粉丝数量',
      },
      {
        title: I18N.t('达人GMV'),
        width: 90,
        dataIndex: 'creator_total_gmv',
        description: '发布此视频的达人GMV',
      },
      {
        title: I18N.t('达人ROI'),
        width: 90,
        dataIndex: 'creator_roi',
        description: '发布此视频的达人ROI',
      },
      {
        title: I18N.t('达人备注'),
        resizable: {
          minWidth: 80,
        },
        dataIndex: 'creator_remark',
        description: '发布此视频的达人备注',
      },
      {
        title: I18N.t('备注'),
        resizable: {
          minWidth: 180,
        },
        dataIndex: 'remark',
        description: '此视频的备注',
      },
      {
        dataIndex: 'option',
        title: I18N.t('操作'),
        width: 80,
        disabled: true,
        description: '可执行的操作选项',
      },
    ];
  }, []);
  const changeColumns = useCallback(() => {
    let _columns = DEFAULT_COLUMNS;
    try {
      if (localStorage.getItem(storage_key)) {
        _columns = JSON.parse(localStorage.getItem(storage_key) || '[]');
      }
    } catch (e) {
      console.log(e);
    }
    setColumns(_columns);
  }, [storage_key]);
  useEffect(() => {
    eventEmitter.on('UPDATE', changeColumns);
    return () => {
      eventEmitter.off('UPDATE', changeColumns);
    };
  }, [changeColumns]);
  return {
    columns: columns.filter((key) => {
      return (
        _.findIndex(meta, (item) => {
          return item.dataIndex === key;
        }) !== -1
      );
    }),
    update: (cols: string[]) => {
      localStorage.setItem(storage_key, JSON.stringify(cols));
      eventEmitter.emit('UPDATE');
    },
    meta,
  };
}
