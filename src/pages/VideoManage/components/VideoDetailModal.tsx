import type { ReactNode } from 'react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import type { InputRef } from 'antd';
import { message } from 'antd';
import { Button, Space } from 'antd';
import { Col, Collapse, Row } from 'antd';
import { Form, Input, Tooltip, Typography } from 'antd';
import { useRequest } from '@@/plugin-request/request';
import { dateFormat, trimValues } from '@/utils/utils';
import DMModal from '@/components/Common/Modal/DMModal';
import DMFormItem, { DMFormItemContext } from '@/components/Common/DMFormItem';
import I18N from '@/i18n';
import Placeholder from '@/components/Common/Placeholder';
import styled from 'styled-components';
import CreatorHandleCell from '@/pages/TikTok/Live/components/CreatorHandleCell';
import _ from 'lodash';
import { getLinkForShow, getNumberDom } from '@/pages/TikTok/utils/utils';
import { useProductsCallback, setOrderAdvanceSearchValues } from '@/pages/Order/components/utils';
import DMConfirm, { DMLoading } from '@/components/Common/DMConfirm';
import colors from '@/style/color.less';
import type { GhostModalWrapperComponentProps } from '@/mixins/modal';
import { GhostModalCaller } from '@/mixins/modal';
import { useSyncVideoSelectedModal } from '@/pages/VideoManage/components/SyncVideoSelectedModal';
import {
  tkshopVideoByIdAdCodeGet,
  tkshopVideoDetailGet,
  tkshopVideoRemarkPut,
} from '@/services/api-TKShopAPI/TkshopVideoController';
import HelpLink from '@/components/HelpLink';
import { versionAlert } from '@/pages/VideoManage/components/utils';
import pMinDelay from 'p-min-delay';
import { StyledCollapse } from '@/style/styled';
import { getVersionValid } from '@/hooks/useVersionValid';
import FallbackImage from '@/components/FallbackImage';
import buttonStyles from '@/style/button.less';
import 'video.js/dist/video-js.css';
import CopyableText from '@/components/Common/CopyableText';
import IconFontIcon from '@/components/Common/IconFontIcon';
import copy from 'copy-to-clipboard';
import VideoStatChartModal from '@/pages/VideoManage/components/VideoStatChartModal';
import ReactVideoPlayerModal, {
  getVideoPageUrl,
} from '@/pages/VideoManage/components/ReactVideoPlayerModal';
import InputADCodeModal from '@/pages/VideoManage/components/InputADCodeModal';

const StyledForm = styled(Form)`
  .ant-typography-ellipsis {
    .ant-typography-copy {
      opacity: 0;
    }
    &:hover {
      .ant-typography-copy {
        opacity: 1;
      }
    }
  }
`;
const VideoPosterStyled = styled.div`
  position: relative;
  cursor: pointer;
  overflow: hidden;
  flex-direction: column;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    .action-wrapper {
      display: flex;
    }
  }

  .action-wrapper {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    background: rgba(0, 0, 0, 0.2);
  }

  .play-button {
    transition: all 0.3s linear;

    &:hover {
      transform: scale(1.2);
    }
  }
`;

const marginBottom = 8;
const VideoDetailModal = (
  props: GhostModalWrapperComponentProps & { data: API.TkshopVideoVo; onUpdate?: () => void },
) => {
  const { data: _data, onUpdate, modalProps } = props;
  const [visible, changeVisible] = useState(true);
  const [data, setData] = useState(_data);
  const updated = useRef(false);
  const onClose = useCallback(
    (needUpdate?: boolean) => {
      changeVisible(false);
      if (updated.current && needUpdate) {
        onUpdate?.();
      }
    },
    [onUpdate],
  );
  const { run } = useRequest(
    () => {
      return tkshopVideoDetailGet({
        creatorId: _data.creatorId!,
        mediaId: _data.mediaId!,
      }).then((res) => {
        setData(res.data!);
        updated.current = true;
        return res!;
      });
    },
    {
      manual: true,
      debounceInterval: 200,
    },
  );
  const { data: loadedSuccess } = useRequest(
    async () => {
      const res = await new Promise((resolve) => {
        if (!data.mediaAvatar) {
          resolve(false);
          return;
        }
        let img: any = new Image();
        img.onload = () => {
          resolve(true);
          img = null;
        };
        img.onerror = () => {
          resolve(false);
          img = null;
        };
        img.src = data.mediaAvatar;
      });
      return {
        data: res,
      };
    },
    {
      initialData: false,
      refreshDeps: [data],
    },
  );
  const [form] = Form.useForm();
  const ref = useRef<InputRef>();
  const { creator, postTime, mediaId, adCode } = data;
  const { run: submit, loading: submitting } = useRequest(
    async () => {
      const vals = await form.validateFields();
      const values = trimValues(vals);
      const { remark = '' } = values;
      await tkshopVideoRemarkPut({
        remark,
        ids: [data.id!],
      });
      updated.current = true;
      onClose(true);
    },
    {
      manual: true,
    },
  );
  const { run: openSyncModal, loading: opening } = useSyncVideoSelectedModal();
  const getProductsDom = useProductsCallback({
    placeholder: (
      <Typography.Link
        onClick={() => {
          DMConfirm({
            width: 560,
            type: 'info',
            title: I18N.t('无法获取该视频的带货商品'),
            content: I18N.t('只有当产生明确的订单后，才能够知晓具体的带货商品'),
          });
        }}
      >
        {I18N.t('未知')}
      </Typography.Link>
    ),
  });
  const getNumber = useCallback(
    (options: {
      value: number | undefined;
      unit?: string | undefined;
      render?: (inner: any) => ReactNode;
    }) => {
      const { value, unit, render } = options;
      if (_.isNil(value)) {
        return (
          <Tooltip
            title={
              <div>
                <span>
                  {I18N.t('通过执行“视频信息更新”流程，可抓取视频的播放量、点赞量等数据')}
                </span>
                <Typography.Link
                  style={{ color: 'inherit', marginLeft: 16 }}
                  onClick={() => {
                    if (opening) {
                      return;
                    }
                    openSyncModal(
                      [
                        {
                          id: data.id!,
                          mediaId: data.mediaId!,
                          creatorId: data.creatorId!,
                          handle: data.creator!.handle!,
                        },
                      ],
                      () => {
                        return data.mediaName || '未命名';
                      },
                    );
                    onClose(true);
                  }}
                >
                  {I18N.t('立即执行')}
                </Typography.Link>
              </div>
            }
          >
            <Typography.Link>{I18N.t('未知')}</Typography.Link>
          </Tooltip>
        );
      }
      const inner = getNumberDom(value, unit);
      if (render) {
        return render(inner);
      }

      return inner;
    },
    [
      opening,
      openSyncModal,
      data.id,
      data.mediaId,
      data.creatorId,
      data.creator,
      data.mediaName,
      onClose,
    ],
  );
  useEffect(() => {
    setTimeout(() => {
      ref.current?.focus();
    }, 300);
  }, []);
  const videoUrl = getVideoPageUrl(data);
  const openPlayer = useCallback(
    (mode: 'qrcode' | 'player') => {
      GhostModalCaller(<ReactVideoPlayerModal mode={mode} data={data} onUpdate={run} />);
    },
    [data, run],
  );
  const poster = useMemo(() => {
    if (!loadedSuccess) {
      return (
        <div
          style={{
            padding: 5,
            width: 270,
            height: 480,
            border: '1px solid #ddd',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <img src={'/404.svg'} width={96} height={96} />
          <Typography.Text type={'secondary'} style={{ textIndent: '2em', textAlign: 'justify' }}>
            通过执行“视频信息更新”流程，可抓取视频的播放量、点赞量等数据，并缓存视频的截图
            <HelpLink href={'/tkshop2/videos'} style={{ marginLeft: 16 }} />
          </Typography.Text>
        </div>
      );
    }
    return (
      <VideoPosterStyled>
        <FallbackImage src={data.mediaAvatar} size={[270, 480]} />
        <div className={'action-wrapper'}>
          <span
            title={I18N.t('播放')}
            onClick={() => {
              openPlayer('player');
            }}
          >
            <IconFontIcon className={'play-button'} iconName={'kaishi2_24'} size={96} />
          </span>
        </div>
      </VideoPosterStyled>
    );
  }, [data.mediaAvatar, loadedSuccess, openPlayer]);
  const footer = useMemo(() => {
    return (
      <Space>
        <Button
          className={buttonStyles.successBtn}
          onClick={() => {
            openPlayer('player');
          }}
        >
          {I18N.t('立即播放')}
        </Button>
        <Button
          disabled={!data.creator?.handle}
          className={buttonStyles.successBtn}
          onClick={() => {
            openPlayer('qrcode');
          }}
        >
          {I18N.t('在手机中播放')}
        </Button>
        <Button
          type={'primary'}
          onClick={() => {
            GhostModalCaller(
              <VideoStatChartModal
                data={data}
                onUpdate={() => {
                  onUpdate?.();
                }}
              />,
            );
            onClose(true);
          }}
        >
          {I18N.t('热力趋势')}
        </Button>
        <Button type={'primary'} onClick={submit} loading={submitting}>
          {I18N.t('确定')}
        </Button>
        <Button
          onClick={() => {
            onClose(true);
          }}
        >
          {I18N.t('取消')}
        </Button>
      </Space>
    );
  }, [data, onClose, onUpdate, openPlayer, submit, submitting]);
  const editADCodeCallback = useCallback(() => {
    GhostModalCaller(
      <InputADCodeModal
        value={data?.adCode}
        onSubmit={async (value) => {
          await tkshopVideoByIdAdCodeGet({
            adCode: value,
            id: data.id!,
          });
          run();
        }}
      />,
    );
  }, [data?.adCode, data.id, run]);
  const getEditADCodeBtn = useCallback(
    (type: 'text' | 'icon') => {
      if (type === 'icon') {
        return (
          <Typography.Link onClick={editADCodeCallback}>
            <IconFontIcon iconName={'edit_24'} />
          </Typography.Link>
        );
      }
      return (
        <Typography.Link onClick={editADCodeCallback}>
          <Space size={4}>
            <IconFontIcon iconName={'edit_24'} />
            <span>录入投流码</span>
          </Space>
        </Typography.Link>
      );
    },
    [editADCodeCallback],
  );

  return (
    <DMModal
      title={I18N.t('带货视频属性')}
      open={visible}
      width={860}
      onCancel={() => {
        onClose(true);
      }}
      footer={footer}
      bodyStyle={{
        paddingBottom: 0,
        paddingTop: 18,
      }}
      {...modalProps}
    >
      <StyledForm requiredMark={false} form={form}>
        <div
          style={{
            display: 'flex',
            gap: 10,
            alignItems: 'stretch',
            overflow: 'hidden',
          }}
        >
          <div
            style={{
              boxSizing: 'content-box',
              flex: '0 0 270px',
              padding: '6px 6px 24px 6px',
              overflow: 'hidden',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: 8,
            }}
          >
            {poster}
            {data?.mediaName ? (
              <Typography.Paragraph
                style={{ marginBottom: 0, color: colors.primaryColor, cursor: 'pointer' }}
                onClick={() => {
                  window.open(videoUrl);
                }}
                copyable={{
                  icon: (
                    <div
                      onClick={(e) => {
                        e.stopPropagation();
                        copy(data.mediaName!);
                        message.success(`${I18N.t('商品名称已复制到剪切板')}`);
                      }}
                    >
                      <IconFontIcon iconName="fuzhi_24" />
                    </div>
                  ),
                  tooltips: false,
                }}
                ellipsis={{
                  rows: 3,
                }}
              >
                {data.mediaName}
              </Typography.Paragraph>
            ) : (
              <Typography.Link
                onClick={() => {
                  window.open(videoUrl);
                }}
              >
                {I18N.t('未命名')}
              </Typography.Link>
            )}
          </div>
          <div
            style={{
              flex: 1,
              overflow: 'hidden',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'stretch',
            }}
          >
            <DMFormItemContext.Provider value={{ labelWidth: 80 }}>
              <Row gutter={[8, 0]}>
                <Col span={12}>
                  <DMFormItem label={I18N.t('达人')} style={{ marginBottom }}>
                    <CreatorHandleCell creator={creator} />
                  </DMFormItem>
                </Col>
                <Col span={12}>
                  <DMFormItem label={I18N.t('媒体ID')} style={{ marginBottom }}>
                    <CopyableText type={I18N.t('媒体ID')} text={mediaId}>
                      {mediaId}
                    </CopyableText>
                  </DMFormItem>
                </Col>
                <Col span={12}>
                  <DMFormItem label={I18N.t('发布时间')} style={{ marginBottom }}>
                    {postTime ? dateFormat(postTime) : <Placeholder />}
                  </DMFormItem>
                </Col>
                <Col span={24}>
                  <DMFormItem label={I18N.t('投流码')} style={{ marginBottom }}>
                    {adCode ? (
                      <CopyableText
                        tooltip={'复制投流码'}
                        actions={[
                          {
                            tooltip: '修改投流码',
                            key: 'edit',
                            node: getEditADCodeBtn('icon'),
                          },
                        ]}
                        type={I18N.t('投流码')}
                      >
                        {adCode}
                      </CopyableText>
                    ) : (
                      getEditADCodeBtn('text')
                    )}
                  </DMFormItem>
                </Col>
                <Col span={24}>
                  <DMFormItem label={I18N.t('链接')} style={{ marginBottom }} shouldUpdate>
                    {() => {
                      return (
                        <CopyableText text={videoUrl} type={I18N.t('链接地址')}>
                          <Typography.Link target={'_blank'} href={videoUrl}>
                            {getLinkForShow(videoUrl)}
                          </Typography.Link>
                        </CopyableText>
                      );
                    }}
                  </DMFormItem>
                </Col>
              </Row>
            </DMFormItemContext.Provider>
            <DMFormItemContext.Provider value={{ disableLabelMuted: false }}>
              <div style={{ flex: 1, overflow: 'hidden' }}>
                <StyledCollapse ghost defaultActiveKey={['media', 'sales']}>
                  <Collapse.Panel key={'media'} header={I18N.t('媒体信息')}>
                    <Row gutter={[8, 0]} wrap>
                      <Col span={12}>
                        <DMFormItem label={I18N.t('播放量')} style={{ marginBottom }} shouldUpdate>
                          {getNumber({ value: data.viewCnt })}
                        </DMFormItem>
                      </Col>
                      <Col span={12}>
                        <DMFormItem label={I18N.t('点赞量')} style={{ marginBottom }} shouldUpdate>
                          {getNumber({ value: data.likeCnt })}
                        </DMFormItem>
                      </Col>
                      <Col span={12}>
                        <DMFormItem label={I18N.t('收藏量')} style={{ marginBottom }} shouldUpdate>
                          {getNumber({ value: data.favoriteCnt })}
                        </DMFormItem>
                      </Col>
                      <Col span={12}>
                        <DMFormItem label={I18N.t('评论量')} style={{ marginBottom }} shouldUpdate>
                          {getNumber({ value: data.commentCnt })}
                        </DMFormItem>
                      </Col>
                      <Col span={12}>
                        <DMFormItem label={I18N.t('转发量')} style={{ marginBottom }} shouldUpdate>
                          {getNumber({ value: data.retweetCnt })}
                        </DMFormItem>
                      </Col>
                    </Row>
                  </Collapse.Panel>
                  <Collapse.Panel key={'sales'} header={I18N.t('带货数据')}>
                    <Row gutter={[8, 0]} wrap>
                      <Col span={12}>
                        <DMFormItem
                          label={I18N.t('带货商品')}
                          style={{ marginBottom }}
                          shouldUpdate
                        >
                          {getProductsDom(data.products!)}
                        </DMFormItem>
                      </Col>
                      <Col span={12}>
                        <DMFormItem
                          label={I18N.t('商品售出金额')}
                          style={{ marginBottom }}
                          shouldUpdate
                        >
                          {getNumber({
                            value: data.gmv,
                            unit: data.unit,
                            render: (inner) => {
                              return (
                                <Typography.Link
                                  ellipsis
                                  onClick={() => {
                                    onClose();
                                    setOrderAdvanceSearchValues({
                                      mediaId,
                                    });
                                  }}
                                >
                                  {inner}
                                </Typography.Link>
                              );
                            },
                          })}
                        </DMFormItem>
                      </Col>
                      <Col span={12}>
                        <DMFormItem
                          label={I18N.t('商品售出数量')}
                          style={{ marginBottom }}
                          shouldUpdate
                        >
                          {getNumber({
                            value: data.itemsSold,
                            render: (inner) => {
                              return (
                                <Typography.Link
                                  ellipsis
                                  onClick={() => {
                                    onClose();
                                    setOrderAdvanceSearchValues({
                                      mediaId,
                                    });
                                  }}
                                >
                                  {inner}
                                </Typography.Link>
                              );
                            },
                          })}
                        </DMFormItem>
                      </Col>
                      <Col span={12}>
                        <DMFormItem label={I18N.t('佣金')} style={{ marginBottom }} shouldUpdate>
                          {getNumber({
                            value: data.estCommission,
                            unit: data.unit,
                            render: (inner) => {
                              return (
                                <Typography.Link
                                  ellipsis
                                  onClick={() => {
                                    onClose();
                                    setOrderAdvanceSearchValues({
                                      mediaId,
                                    });
                                  }}
                                >
                                  {inner}
                                </Typography.Link>
                              );
                            },
                          })}
                        </DMFormItem>
                      </Col>
                    </Row>
                  </Collapse.Panel>
                </StyledCollapse>
              </div>
            </DMFormItemContext.Provider>
            <DMFormItem
              labelWidth={80}
              className={'remark-row'}
              label={'备注'}
              name={'remark'}
              initialValue={data.remark || ''}
              rules={[
                {
                  validator(rule, value) {
                    const val = (value || '').trim();
                    if (val.length > 1000) {
                      return Promise.reject(new Error('备注长度不能超过1000个字'));
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <Input.TextArea
                placeholder={I18N.t('对视频添加备注方便您日后查询（最多1000字）')}
                ref={ref}
                style={{ resize: 'none', height: 70 }}
              />
            </DMFormItem>
          </div>
        </div>
      </StyledForm>
    </DMModal>
  );
};
export default VideoDetailModal;

export function useVideoDetailModal(onUpdate?: () => void) {
  const result = useRequest(
    async (video: API.TkshopVideoVo) => {
      if (result.loading) {
        return;
      }
      if (!(await getVersionValid())) {
        versionAlert();
        return;
      }
      if (!video) {
        DMConfirm({
          type: 'info',
          title: I18N.t(`未获取到视频详情`),
        });
        return;
      }
      GhostModalCaller(<VideoDetailModal data={video} onUpdate={onUpdate} />);
    },
    {
      manual: true,
    },
  );
  return result;
}
export function useDetailOutsideVideo() {
  const result = useRequest(
    async (mediaId: string, creatorId: number) => {
      if (result.loading) {
        return;
      }
      if (!(await getVersionValid())) {
        versionAlert();
        return;
      }
      const loading = DMLoading({
        title: I18N.t('正在为您查询关联的视频信息'),
      });
      const detail = await pMinDelay(
        tkshopVideoDetailGet({
          creatorId,
          mediaId,
        }),
        400,
      )
        .then((res) => {
          loading.destroy();
          return res.data!;
        })
        .catch(() => {
          loading.destroy();
        });
      if (!detail) {
        DMConfirm({
          type: 'warn',
          title: I18N.t(`未获取到视频［${mediaId}］详情`),
        });
      } else {
        GhostModalCaller(<VideoDetailModal data={detail} />);
      }
    },
    { manual: true },
  );
  return result;
}
