declare module 'slash2';
declare module '*.css';
declare module '*.less';
declare module '*.scss';
declare module '*.sass';
declare module '*.svg';
declare module '*.png';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.gif';
declare module '*.bmp';
declare module '*.tiff';
declare module 'omit.js';
declare module 'numeral';
declare module '@antv/data-set';
declare module 'mockjs';
declare module 'react-fittext';
declare module 'bizcharts-plugin-slider';

// google analytics interface
type GAFieldsObject = {
  eventCategory: string;
  eventAction: string;
  eventLabel?: string;
  eventValue?: number;
  nonInteraction?: boolean;
};

interface ShopChannelVo extends API.ShopChannelTokenVo {
  currentEndpoint: string;
  transitPing: Record<string, number>;
}

interface Window {
  ga: (
    command: 'send',
    hitType: 'event' | 'pageview',
    fieldsObject: GAFieldsObject | string,
  ) => void;
  reloadAuthorized: () => void;
  routerBase: string;
  serverData: {
    sessionId: number;
    shopInfo: API.ShopDetailVo;
    ipDetail?: API.IpDetailAllVo;
    channelList: ShopChannelVo[];
    routerRules: API.ShopRouterVo[];
    accountLoginUrl: string;
    paymentPlatformInfo?: API.ShopPlatformRefVo;
    mailPlatformInfo?: API.ShopPlatformRefVo;
    createTime: string;
    clientIpLocation: API.IpLocationVo;
    exchangeRates: any;
    grantedFunctionCodes: string[];
    extensionCount: number;
    userInfo: API.UserDetailVo;
    transitList: API.TransitDto[];
    creditConfig: API.CreditConfig;
    browserEnv: Record<string, any>;
  };
  donkeyRequest?: (path: string, options: { [key: string]: any }) => Promise<unknown>;
}
declare const window: Window;

declare let ga: () => void;

// preview.pro.ant.design only do not use in your production ;
// preview.pro.ant.design 专用环境变量，请不要在你的项目中使用它。
declare let ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION: 'site' | undefined;

declare const REACT_APP_ENV: 'test' | 'dev' | 'pre' | false;

type Platform = 'Win32' | 'MacIntel' | 'Linux x86_64';

type Browser = 'Chrome' | 'Edge' | 'Firefox';
