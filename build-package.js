const fs = require('fs-extra');
const path = require('path');
const nugget = require('nugget');
const { execSync, spawn } = require('child_process');

/**
 * 构建安装包脚本
 */

const CHROMIUM_URL = process.env.CHROMIUM_URL || 'https://dev.thinkoncloud.cn/downloads/';
const APP_DOWNLOAD_URL = process.env.APP_DOWNLOAD_URL || 'https://dev.thinkoncloud.cn/downloads/';
const platform = process.env.PLATFORM || process.platform;
const BROWSER_BUILD_VERSION = process.env.BROWSER_BUILD_VERSION || 'latest';
const PORTAL_URL = process.env.PORTAL_URL || 'https://dev.thinkoncloud.cn';
const API_URL = process.env.API_URL || 'https://dev.thinkoncloud.cn';
const RELEASE_APP_VERSION = process.env.RELEASE_APP_VERSION || '1.0.8';
const BUILD_NUMBER = process.env.BUILD_NUMBER || '9999';
const platform_simple = {
  windows: 'win',
  win32: 'win',
  macos: 'mac',
  darwin: 'mac',
  mas: 'mac',
  linux: 'linux',
};
const platformCode = platform_simple[platform] ?? 'win';
const assetsPath = path.resolve(__dirname, './electron/assets');
const appDistPath = path.resolve(__dirname, './electron-dist');

// 编译 renderer 静态资源
console.log('开始编译 renderer 静态资源');
execSync(
  `cross-env RELEASE_APP_VERSION=${RELEASE_APP_VERSION} BUILD_NUMBER=${BUILD_NUMBER} PORTAL_URL=${PORTAL_URL} API_URL=${API_URL} npm run build:client`,
  {
    stdio: 'inherit',
  },
);
console.log('开始npm run build:browser');
execSync(`cross-env PORTAL_URL=${PORTAL_URL} API_URL=${API_URL} npm run build:browser`, {
  stdio: 'inherit',
});
// 编译浏览器扩展程序
console.log('开始编译浏览器扩展程序');
execSync('npm run ext:build');
// 编译 main 源码
console.log('开始编译 main 源码');
execSync(
  `cross-env RELEASE_APP_VERSION=${RELEASE_APP_VERSION} BUILD_NUMBER=${BUILD_NUMBER} PORTAL_URL=${PORTAL_URL} API_URL=${API_URL} webpack --config webpack.config.electron.js --progress=profile`,
  { stdio: 'inherit' },
);

new Promise((resolve) => {
  // 创建 assets 目录
  if (fs.existsSync(assetsPath)) {
    // 清空文件
    fs.emptyDirSync(assetsPath);
    resolve();
  } else {
    resolve(fs.mkdirSync(assetsPath, { recursive: true }));
  }
})
  .then(() => {
    // 下载 chromium
    return downloadArtifact(assetsPath);
  })
  .then(() => {
    // 修改 app version
    const json = fs.readJSONSync(path.resolve('./electron/package.json'));
    json.version = RELEASE_APP_VERSION;
    fs.writeJSONSync(path.resolve('./electron/package.json'), json, { spaces: '  ' });
    // 修改自动更新 url
    const builderJson = fs.readJSONSync(path.resolve('./electron/electron-builder.json'));
    builderJson.publish = [{ provider: 'generic', url: APP_DOWNLOAD_URL }];
    fs.writeJSONSync(path.resolve('./electron/electron-builder.json'), builderJson, {
      spaces: '  ',
    });
  })
  .then(() => {
    // 清空文件
    fs.emptyDirSync(appDistPath);
    // 打包安装程序
    const child = spawn(
      'node',
      [
        path.resolve('./node_modules/electron-builder/cli.js'),
        '--config',
        path.resolve('./electron/electron-builder.json'),
        `--${platformCode}`,
      ],
      {
        env: {
          ...process.env,
          ELECTRON_MIRROR: 'https://cdn.npmmirror.com/binaries/electron/v',
        },
      },
    );
    child.stdout.on('data', (data) => {
      process.stdout.write(data);
    });
    child.stderr.on('data', (err) => {
      process.stdout.write(err);
      process.exit(1);
    });
    child.on('exit', (data) => {
      process.stdout.write('Build process done!');
    });
  });

async function downloadArtifact(dest) {
  const name = `chrome-${platformCode}_${BROWSER_BUILD_VERSION}.zip`;
  const url = `${CHROMIUM_URL}${name}`;
  console.log(`Downloading ${url}`);
  let downloadError = false;
  await downloadWithRetry(url, dest).catch((err) => {
    console.log(`${url} could not be successfully downloaded.  Error was:`, err);
    downloadError = true;
  });
  if (!downloadError) {
    console.log(`Successfully downloaded ${name}.`);
  }
  if (!downloadError) {
    return true;
  }
  throw new Error('Download fail');
}

async function downloadWithRetry(url, directory) {
  let lastError;
  const downloadURL = `${url}`;
  for (let i = 0; i < 5; i++) {
    console.log(`Attempting to download ${url} - attempt #${i + 1}`);
    try {
      return await downloadFile(downloadURL, directory);
    } catch (err) {
      lastError = err;
      await new Promise((resolve, reject) => setTimeout(resolve, 30000));
    }
  }
  throw lastError;
}

async function downloadFile(url, directory) {
  return new Promise((resolve, reject) => {
    const nuggetOpts = {
      dir: directory,
      target: `chrome-${platformCode}.zip`,
    };
    nugget(url, nuggetOpts, (err) => {
      if (err) {
        reject(err);
      } else {
        resolve();
      }
    });
  });
}
