{"openapi": "3.0.3", "info": {"title": "Plugin API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "Plugin API", "description": "Plugin Controller"}, {"name": "Plugin Log API", "description": "Plugin Log Controller"}, {"name": "Plugin Param API", "description": "Plugin <PERSON>m Controller"}], "paths": {"/api/plugin/createBuyPluginPackRequest": {"post": {"tags": ["PluginController"], "summary": "创建购买插件的订单", "operationId": "pluginCreateBuyPluginPackRequestPost", "parameters": [{"name": "agreement", "in": "query", "description": "已经勾选阅读和同意使用协议，没什么用", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "balanceAmount", "in": "query", "description": "余额抵扣金额", "required": false, "style": "form", "schema": {"type": "number", "format": "bigdecimal"}}, {"name": "count", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "duration", "in": "query", "description": "购买时长，根据 periodUnit的值 有可能是月，周或天", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "immediatePay", "in": "query", "description": "是否立即支付（点稍候支付该属性传false）", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "payType", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}}, {"name": "periodUnit", "in": "query", "description": "购买周期：年,Buyout（永久）", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}}, {"name": "pluginId", "in": "query", "description": "插件ID", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "voucherAmount", "in": "query", "description": "代金券抵扣金额，不得大于代金券余额", "required": false, "style": "form", "schema": {"type": "number", "format": "bigdecimal"}}, {"name": "voucherId", "in": "query", "description": "要使用的代金券id", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CreateOrderResponse»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/plugin/createRenewPluginPackRequest": {"post": {"tags": ["PluginController"], "summary": "创建续费插件的订单", "operationId": "pluginCreateRenewPluginPackRequestPost", "parameters": [{"name": "agreement", "in": "query", "description": "已经勾选阅读和同意使用协议，没什么用", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "balanceAmount", "in": "query", "description": "余额抵扣金额", "required": false, "style": "form", "schema": {"type": "number", "format": "bigdecimal"}}, {"name": "count", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "duration", "in": "query", "description": "购买时长，根据 periodUnit的值 有可能是月，周或天", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "immediatePay", "in": "query", "description": "是否立即支付（点稍候支付该属性传false）", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "payType", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}}, {"name": "periodUnit", "in": "query", "description": "购买周期：年,Buyout（永久）", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}}, {"name": "pluginId", "in": "query", "description": "插件ID", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "voucherAmount", "in": "query", "description": "代金券抵扣金额，不得大于代金券余额", "required": false, "style": "form", "schema": {"type": "number", "format": "bigdecimal"}}, {"name": "voucherId", "in": "query", "description": "要使用的代金券id", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CreateOrderResponse»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/plugin/current": {"get": {"tags": ["PluginController"], "summary": "查询当前用户的插件信息", "operationId": "pluginCurrentGet", "parameters": [{"name": "pluginId", "in": "query", "description": "pluginId", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PluginInfo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/plugin/logs": {"get": {"tags": ["PluginLogController"], "summary": "查询日志", "operationId": "pluginLogsGet", "parameters": [{"name": "category", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "createTimeFrom", "in": "query", "description": "yyyy-MM-dd HH:mm:ss", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "createTimeTo", "in": "query", "description": "yyyy-MM-dd HH:mm:ss", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "pluginId", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«PluginLog»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "post": {"tags": ["PluginLogController"], "summary": "添加日志", "operationId": "pluginLogsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddPluginLogRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/plugin/param": {"post": {"tags": ["PluginParamController"], "summary": "创建插件参数", "operationId": "pluginParamPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePluginParamRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PluginParamDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/plugin/param/{id}": {"put": {"tags": ["PluginParamController"], "summary": "修改插件参数", "description": "仅可以修改名称和值", "operationId": "pluginParamByIdPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePluginParamRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PluginParamDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "delete": {"tags": ["PluginParamController"], "summary": "删除插件参数", "operationId": "pluginParamByIdDelete", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/plugin/params": {"get": {"tags": ["PluginParamController"], "summary": "查询参数列表", "operationId": "pluginParamsGet", "parameters": [{"name": "teamPackId", "in": "query", "description": "teamPackId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "paramType", "in": "query", "description": "paramType", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "name", "in": "query", "description": "name", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«PluginParamDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"AddPluginLogRequest": {"title": "AddPluginLogRequest", "type": "object", "properties": {"logs": {"type": "array", "items": {"$ref": "#/components/schemas/PluginLog"}}}}, "CreateOrderResponse": {"title": "CreateOrderResponse", "type": "object", "properties": {"bankAccount": {"type": "string"}, "bankAccountName": {"type": "string"}, "bankName": {"type": "string"}, "bankRemark": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "deductedPrice": {"type": "number", "description": "扣减金额", "format": "bigdecimal"}, "orderId": {"type": "integer", "format": "int64"}, "payOutContent": {"type": "string", "description": "支付输出的内容"}, "payOutType": {"type": "string", "description": "支付输出的内容类型"}, "payStatus": {"type": "string", "description": "如果不需要现金支付，该订单状态会直接变成已支付", "enum": ["CANCELED", "Created", "Locked", "PAID", "PartialRefund", "REFUNDED", "WAIT_CONFIRM"]}, "payType": {"type": "string", "description": "支付方式", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}, "realPrice": {"type": "number", "description": "需要现金支付的money", "format": "bigdecimal"}, "salesReduction": {"type": "number", "format": "bigdecimal"}, "serialNumber": {"type": "string"}}}, "CreatePluginParamRequest": {"title": "CreatePluginParamRequest", "type": "object", "properties": {"name": {"type": "string", "description": "名称（团队唯一）"}, "paramType": {"type": "string", "description": "参数类型"}, "teamPackId": {"type": "integer", "description": "关联套餐", "format": "int64"}, "value": {"type": "object", "description": "参数值"}}}, "PluginInfo": {"title": "PluginInfo", "type": "object", "properties": {"pack": {"$ref": "#/components/schemas/PluginTeamPackDto"}, "team": {"$ref": "#/components/schemas/TeamDto"}}}, "PluginLog": {"title": "PluginLog", "type": "object", "properties": {"category": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "data": {"type": "object"}, "id": {"type": "string", "description": "id"}, "pluginId": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}}}, "PluginParamDto": {"title": "PluginParamDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "paramType": {"type": "string"}, "paramValue": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "teamPackId": {"type": "integer", "format": "int64"}}}, "PluginTeamPackDto": {"title": "PluginTeamPackDto", "type": "object", "properties": {"autoRenew": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "goodsId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "onTrail": {"type": "boolean"}, "packId": {"type": "integer", "format": "int64"}, "presentCredit": {"type": "integer", "format": "int32"}, "renewPrice": {"type": "number", "format": "bigdecimal"}, "teamId": {"type": "integer", "format": "int64"}, "valid": {"type": "boolean"}, "validEndDate": {"type": "string", "format": "date-time"}}}, "TeamDto": {"title": "TeamDto", "type": "object", "properties": {"avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "deleteTime": {"type": "string", "format": "date-time"}, "domesticCloudEnabled": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "invalidTime": {"type": "string", "format": "date-time"}, "inviteCode": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "overseaCloudEnabled": {"type": "boolean"}, "paid": {"type": "boolean"}, "partnerId": {"type": "integer", "format": "int64"}, "payTime": {"type": "string", "format": "date-time"}, "repurchaseTime": {"type": "string", "format": "date-time"}, "repurchased": {"type": "boolean"}, "status": {"type": "string", "enum": ["Blocked", "Deleted", "Pending", "Ready"]}, "teamType": {"type": "string", "enum": ["crs", "gh", "krShop", "normal", "partner", "plugin", "tk", "tkshop"]}, "tenantId": {"type": "integer", "format": "int64"}, "testing": {"type": "boolean"}, "validateTime": {"type": "string", "format": "date-time"}, "validated": {"type": "boolean"}, "verified": {"type": "boolean"}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CreateOrderResponse»": {"title": "WebResult«CreateOrderResponse»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CreateOrderResponse"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«PluginLog»»": {"title": "WebResult«List«PluginLog»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/PluginLog"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«PluginParamDto»»": {"title": "WebResult«List«PluginParamDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/PluginParamDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PluginInfo»": {"title": "WebResult«PluginInfo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PluginInfo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PluginParamDto»": {"title": "WebResult«PluginParamDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PluginParamDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-device-token": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-device-token", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}