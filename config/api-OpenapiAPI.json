{"openapi": "3.0.3", "info": {"title": "Open API", "version": "App version 1.0,Spring Boot Version:2.4.3"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "1 OAuth Api", "description": "认证 API"}, {"name": "2 User Api", "description": "用户 API"}, {"name": "3 Task Api", "description": "任务 API"}, {"name": "4 Session Api", "description": "会话 API"}, {"name": "5 Account <PERSON><PERSON>", "description": "账户 API"}, {"name": "6 Fingerprint Api", "description": "指纹 API"}], "paths": {"/api/account": {"post": {"tags": ["账户API"], "summary": "创建账号", "operationId": "accountPost", "parameters": [{"name": "name", "in": "query", "description": "账号名称", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "platformType", "in": "query", "description": "账号平台", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "area", "in": "query", "description": "平台所在区域", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Argentina", "Australia", "Austria", "Belarus", "Belgium", "Bolivia", "Brazil", "Canada", "Chile", "China", "Colombia", "Costa_Rica", "Dominican", "Ecuador", "Egypt", "France", "Germany", "Global", "Guatemala", "Honduras", "HongKong", "India", "Indonesia", "Ireland", "Israel", "Italy", "Japan", "Kazakhstan", "Korea", "Malaysia", "Mexico", "Netherlands", "Nicaragua", "Panama", "Paraguay", "Peru", "Philippines", "Poland", "Portuguese", "Puerto_Rico", "Russia", "Salvador", "Saudi_Arabia", "Singapore", "Spain", "Sweden", "Switzerland", "Taiwan", "Thailand", "Turkey", "United_Arab_Emirates", "United_Kingdom", "United_States", "Uruguay", "Venezuela", "Vietnam"]}}, {"name": "type", "in": "query", "description": "账号类型", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Global", "Local", "None"]}}, {"name": "description", "in": "query", "description": "描述", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "stateless", "in": "query", "description": "是否无状态", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "tags", "in": "query", "description": "标签，可以用逗号分隔多个标签", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«AccountVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/account/byName": {"get": {"tags": ["账户API"], "summary": "根据名称获取账户详情", "operationId": "accountByNameGet", "parameters": [{"name": "name", "in": "query", "description": "账号名称", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«AccountVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/account/{id}": {"get": {"tags": ["账户API"], "summary": "获取账户详情", "operationId": "accountByIdGet", "parameters": [{"name": "id", "in": "path", "description": "账号ID", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«AccountVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/account/{id}/cookie": {"post": {"tags": ["账户API"], "summary": "给特定账号上传Cookie信息", "operationId": "accountByIdCookiePost", "parameters": [{"name": "id", "in": "path", "description": "账号ID", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportCookieRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/token": {"get": {"tags": ["认证API"], "summary": "获取请求token", "operationId": "tokenGet", "parameters": [{"name": "accessKeyId", "in": "query", "description": "accessKeyId", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "accessKeySecret", "in": "query", "description": "accessKeySecret", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "expireSeconds", "in": "query", "description": "超时时间，单位秒，取值范围：[120,7200]", "required": true, "style": "form", "schema": {"maximum": 7200, "exclusiveMaximum": false, "minimum": 120, "exclusiveMinimum": false, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«AkAccessToken»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/fingerprint/reportUrl": {"get": {"tags": ["指纹API"], "summary": "获取汇报Cookie和指纹的链接", "operationId": "fingerprintReportUrlGet", "parameters": [{"name": "accountId", "in": "query", "description": "账号ID", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "expireMinutes", "in": "query", "description": "URL有效期，单位分钟，默认10分钟，取值范围[1,120]", "required": false, "style": "form", "schema": {"maximum": 120, "exclusiveMaximum": false, "minimum": 1, "exclusiveMinimum": false, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«FingerprintReportUrl»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/session/closeByAccountId": {"put": {"tags": ["会话API"], "summary": "关闭账户所有打开的会话", "operationId": "sessionCloseByAccountIdPut", "parameters": [{"name": "accountId", "in": "query", "description": "账号ID", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "备注", "in": "query", "description": "备注", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«SessionVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/session/closeByAccountName": {"put": {"tags": ["会话API"], "summary": "根据账户名称关闭账户所有打开的会话", "operationId": "sessionCloseByAccountNamePut", "parameters": [{"name": "accountName", "in": "query", "description": "账号名称", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "备注", "in": "query", "description": "备注", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«SessionVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/session/open": {"post": {"tags": ["会话API"], "summary": "打开会话", "operationId": "sessionOpenPost", "parameters": [{"name": "accountId", "in": "query", "description": "账号ID", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "deviceId", "in": "query", "description": "设备标识，可选", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "remoteDebugPort", "in": "query", "description": "远程调试端口，可选。不填写或取值范围[1024,65535]", "required": false, "style": "form", "schema": {"maximum": 65535, "exclusiveMaximum": false, "minimum": 1024, "exclusiveMinimum": false, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«OpenapiTaskDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/session/openByAccountName": {"post": {"tags": ["会话API"], "summary": "根据账户名称打开会话", "operationId": "sessionOpenByAccountNamePost", "parameters": [{"name": "accountName", "in": "query", "description": "账号名称", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "deviceId", "in": "query", "description": "设备标识，可选", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "remoteDebugPort", "in": "query", "description": "远程调试端口，可选。不填写或取值范围[1024,65535]", "required": false, "style": "form", "schema": {"maximum": 65535, "exclusiveMaximum": false, "minimum": 1024, "exclusiveMinimum": false, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«OpenapiTaskDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/session/{sessionId}/close": {"put": {"tags": ["会话API"], "summary": "关闭会话", "operationId": "sessionBySessionIdClosePut", "parameters": [{"name": "sessionId", "in": "path", "description": "会话ID", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "remarks", "in": "query", "description": "备注,可选", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/sessions": {"get": {"tags": ["会话API"], "summary": "查询账户打开的会话", "operationId": "sessionsGet", "parameters": [{"name": "accountId", "in": "query", "description": "账号ID", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«SessionVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/sessions/byAccountName": {"get": {"tags": ["会话API"], "summary": "根据账户名称查询打开的会话", "operationId": "sessionsByAccountNameGet", "parameters": [{"name": "accountName", "in": "query", "description": "账号名称", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«SessionVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/task/{requestId}": {"get": {"tags": ["任务API"], "summary": "根据requestId查询任务", "operationId": "taskByRequestIdGet", "parameters": [{"name": "requestId", "in": "path", "description": "requestId", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«OpenapiTaskModel»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/user/current": {"get": {"tags": ["用户API"], "summary": "获取当前用户信息", "operationId": "userCurrentGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«UserVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}}, "components": {"schemas": {"AccountVo": {"title": "Account<PERSON>o", "type": "object", "properties": {"account": {"type": "string", "description": "账户账号"}, "allowExtension": {"type": "boolean", "description": "是否允许用户自行安装插件", "example": false}, "allowMonitor": {"type": "boolean", "description": "会话是否允许监视", "example": false}, "allowSkip": {"type": "boolean", "description": "是否允许跳过敏感操作", "example": false}, "autoFill": {"type": "boolean", "description": "是否自动代填", "example": false}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "creatorId": {"type": "integer", "description": "创建者", "format": "int64"}, "description": {"type": "string", "description": "描述"}, "domainPolicy": {"type": "string", "enum": ["Blacklist", "None", "Whitelist"]}, "downTraffic": {"type": "integer", "description": "下行流量", "format": "int64"}, "exclusive": {"type": "boolean", "description": "是否独占访问", "example": false}, "fingerprintId": {"type": "integer", "description": "绑定的指纹Id", "format": "int64"}, "fingerprintTemplateId": {"type": "integer", "description": "绑定的指纹模板Id（只有无状态账号才允许绑定指纹模板）", "format": "int64"}, "frontUrl": {"type": "string"}, "googleTranslateSpeed": {"type": "boolean"}, "homePage": {"type": "string"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "imageForbiddenSize": {"type": "integer", "format": "int64"}, "intranetEnabled": {"type": "boolean"}, "ipSwitchCheckInterval": {"type": "integer", "format": "int32"}, "ipSwitchStrategy": {"type": "string", "enum": ["Abort", "<PERSON><PERSON>", "Off"]}, "lastAccessTime": {"type": "string", "format": "date-time"}, "name": {"type": "string", "description": "账户名称"}, "operateStatus": {"type": "string", "enum": ["shared", "sharing", "sole", "transferring"]}, "operatingCategory": {"type": "string", "description": "经营品类", "enum": ["医药保健", "图书文具", "宠物用品", "家具建材", "家电电器", "工业用品", "户外运动", "手机数码", "手表眼镜", "护肤美妆", "母婴玩具", "汽车配件", "生活家居", "电商其他", "电脑平板", "艺术珠宝", "花园聚会", "计生情趣", "软件程序", "鞋服箱包", "音乐影视", "食品生鲜", "鲜花绿植"]}, "parentShopId": {"type": "integer", "format": "int64"}, "password": {"type": "string", "description": "密码"}, "platformId": {"type": "integer", "description": "平台ID", "format": "int64"}, "recordPolicy": {"type": "string", "enum": ["<PERSON><PERSON>", "Disabled", "Forced"]}, "resourcePolicy": {"type": "integer", "format": "int32"}, "securityPolicyEnabled": {"type": "boolean"}, "securityPolicyUpdateTime": {"type": "string", "format": "date-time"}, "sharePolicyId": {"type": "string"}, "stateless": {"type": "boolean"}, "syncPolicy": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "description": "团队ID", "format": "int64"}, "trafficAlertStrategy": {"type": "string", "enum": ["Abort", "Off"]}, "trafficAlertThreshold": {"type": "integer", "format": "int32"}, "trafficSaving": {"type": "boolean"}, "type": {"type": "string", "enum": ["Global", "Local", "None"]}, "upTraffic": {"type": "integer", "description": "上行流量", "format": "int64"}, "webSecurity": {"type": "boolean"}}}, "AkAccessToken": {"title": "AkAccessToken", "type": "object", "properties": {"expireTime": {"type": "string", "description": "Token超时时间", "format": "date-time"}, "token": {"type": "string", "description": "登录的token"}}}, "FingerprintReportUrl": {"title": "FingerprintReportUrl", "type": "object", "properties": {"expireTime": {"type": "string", "format": "date-time"}, "reportUrl": {"type": "string"}}}, "OpenapiTaskDto": {"title": "OpenapiTaskDto", "type": "object", "properties": {"accessKeyId": {"type": "string", "description": "调用者AKID"}, "action": {"type": "string", "description": "任务操作"}, "bizData": {"type": "string", "description": "业务参数"}, "createTime": {"type": "string", "description": "任务创建时间", "format": "date-time"}, "creator": {"type": "integer", "description": "任务创建者ID", "format": "int64"}, "done": {"type": "boolean", "description": "是否已经完成", "example": false}, "finishTime": {"type": "string", "description": "任务完成时间", "format": "date-time"}, "id": {"type": "integer", "description": "任务ID", "format": "int64"}, "name": {"type": "string", "description": "任务名称"}, "progress": {"type": "integer", "description": "进度（如果支持）", "format": "int32"}, "remarks": {"type": "string", "description": "任务关联资源ID（如果支持）"}, "requestId": {"type": "string", "description": "请求ID"}, "resourceId": {"type": "integer", "description": "任务关联资源ID（如果支持）", "format": "int64"}, "resourceType": {"type": "string", "description": "任务关联资源类型（如果支持）", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GiftCardPack", "Invoice", "Ip", "IpPool", "IppIp", "None", "Orders", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TkCreator", "TkTeamPack", "TrafficPack", "TunnelVps", "Users", "View", "Voucher"]}, "status": {"type": "string", "description": "任务状态"}, "teamId": {"type": "integer", "description": "所属团队ID", "format": "int64"}}}, "OpenapiTaskModel": {"title": "OpenapiTaskModel", "type": "object", "properties": {"accessKeyId": {"type": "string", "description": "调用者AKID"}, "action": {"type": "string", "description": "任务操作"}, "bizData": {"type": "string", "description": "业务参数"}, "createTime": {"type": "string", "description": "任务创建时间", "format": "date-time"}, "creator": {"type": "integer", "description": "任务创建者ID", "format": "int64"}, "done": {"type": "boolean", "description": "是否已经完成", "example": false}, "finishTime": {"type": "string", "description": "任务完成时间", "format": "date-time"}, "id": {"type": "integer", "description": "任务ID", "format": "int64"}, "name": {"type": "string", "description": "任务名称"}, "progress": {"type": "integer", "description": "进度（如果支持）", "format": "int32"}, "remarks": {"type": "string", "description": "任务关联资源ID（如果支持）"}, "requestId": {"type": "string", "description": "请求ID"}, "resource": {"type": "object", "description": "关联资源"}, "resourceId": {"type": "integer", "description": "任务关联资源ID（如果支持）", "format": "int64"}, "resourceType": {"type": "string", "description": "任务关联资源类型（如果支持）", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GiftCardPack", "Invoice", "Ip", "IpPool", "IppIp", "None", "Orders", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TkCreator", "TkTeamPack", "TrafficPack", "TunnelVps", "Users", "View", "Voucher"]}, "status": {"type": "string", "description": "任务状态"}, "teamId": {"type": "integer", "description": "所属团队ID", "format": "int64"}}}, "ReportCookieRequest": {"title": "ReportCookieRequest", "type": "object", "properties": {"cookies": {"type": "array", "items": {"$ref": "#/components/schemas/ShopCookieVo"}}}}, "SessionVo": {"title": "SessionVo", "type": "object", "properties": {"bucketId": {"type": "integer", "format": "int64"}, "clientIp": {"type": "string"}, "closeTime": {"type": "string", "format": "date-time"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "deviceId": {"type": "string"}, "downTraffic": {"type": "integer", "format": "int64"}, "hbTimeout": {"type": "boolean"}, "heartbeatTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "ipId": {"type": "integer", "format": "int64"}, "openapiTaskId": {"type": "integer", "format": "int64"}, "proxyId": {"type": "integer", "format": "int64"}, "recordLocked": {"type": "boolean"}, "recordStatus": {"type": "string", "enum": ["Deleted", "Undefined", "<PERSON><PERSON>"]}, "remoteDebugPort": {"type": "integer", "format": "int32"}, "shopId": {"type": "integer", "format": "int64"}, "spyScr": {"type": "boolean"}, "status": {"type": "string", "enum": ["CLOSE", "CREATING", "READY"]}, "teamId": {"type": "integer", "format": "int64"}, "transitId": {"type": "integer", "format": "int64"}, "upTraffic": {"type": "integer", "format": "int64"}}}, "ShopCookieVo": {"title": "ShopCookieVo", "type": "object", "properties": {"domain": {"type": "string"}, "expires": {"type": "number", "format": "double"}, "httpOnly": {"type": "boolean"}, "name": {"type": "string"}, "path": {"type": "string"}, "priority": {"type": "string"}, "sameParty": {"type": "boolean"}, "sameSite": {"type": "string"}, "secure": {"type": "boolean"}, "updateTime": {"type": "string", "format": "date-time"}, "value": {"type": "string"}}}, "UserVo": {"title": "UserVo", "type": "object", "properties": {"avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "email": {"type": "string", "description": "邮箱"}, "gender": {"type": "string", "enum": ["FEMALE", "MALE", "UNSPECIFIC"]}, "id": {"type": "integer", "format": "int64"}, "nickname": {"type": "string"}, "partnerId": {"type": "integer", "format": "int64"}, "phone": {"type": "string", "description": "手机"}, "residentCity": {"type": "string"}, "signature": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}, "tenant": {"type": "integer", "format": "int64"}, "userType": {"type": "string", "enum": ["NORMAL", "PARTNER"]}, "weixin": {"type": "string"}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«AccountVo»": {"title": "WebResult«AccountVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/AccountVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«AkAccessToken»": {"title": "WebResult«AkAccessToken»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/AkAccessToken"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«FingerprintReportUrl»": {"title": "WebResult«FingerprintReportUrl»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/FingerprintReportUrl"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«OpenapiTaskModel»»": {"title": "WebResult«List«OpenapiTaskModel»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/OpenapiTaskModel"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«SessionVo»»": {"title": "WebResult«List«SessionVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SessionVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«OpenapiTaskDto»": {"title": "WebResult«OpenapiTaskDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/OpenapiTaskDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«UserVo»": {"title": "WebResult«UserVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/UserVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}}