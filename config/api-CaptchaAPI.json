{"openapi": "3.0.3", "info": {"title": "Captcha API", "version": "App version 1.0"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "Captcha API", "description": "<PERSON><PERSON> <PERSON>"}], "paths": {"/api/captcha/check": {"get": {"tags": ["<PERSON><PERSON><PERSON>ontroller"], "summary": "校验验证码", "operationId": "captchaCheckGet", "parameters": [{"name": "<PERSON><PERSON>a", "in": "query", "description": "<PERSON><PERSON>a", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/captcha/get": {"get": {"tags": ["<PERSON><PERSON><PERSON>ontroller"], "summary": "获取captcha图片", "operationId": "captchaGetGet", "parameters": [{"name": "token", "in": "query", "description": "captcha token", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "width", "in": "query", "description": "图片宽度", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "height", "in": "query", "description": "图片高度", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "token not valid", "content": {"image/jpeg": {"schema": {"$ref": "Error-ModelName{namespace='java.lang', name='Void'}"}}}}, "400": {"description": "token not valid"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "429": {"description": "Too Many Requests"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/captcha/getBase64": {"get": {"tags": ["<PERSON><PERSON><PERSON>ontroller"], "summary": "获取captcha图片（base64格式）", "operationId": "captchaGetBase64Get", "parameters": [{"name": "token", "in": "query", "description": "captcha token", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "width", "in": "query", "description": "图片宽度", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "height", "in": "query", "description": "图片高度", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "the CaptchaBase64Vo", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CaptchaBase64Vo»"}}}}, "400": {"description": "token not valid"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "429": {"description": "Too Many Requests"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/captcha/token": {"get": {"tags": ["<PERSON><PERSON><PERSON>ontroller"], "summary": "获取一个captcha token", "operationId": "captchaTokenGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CaptchaTokenVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"CaptchaBase64Vo": {"title": "CaptchaBase64Vo", "type": "object", "properties": {"base64": {"type": "string", "description": "图片的base64"}}}, "CaptchaTokenVo": {"title": "CaptchaTokenVo", "type": "object", "properties": {"token": {"type": "string", "description": "用于验证captcha的token"}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CaptchaBase64Vo»": {"title": "WebResult«CaptchaBase64Vo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CaptchaBase64Vo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CaptchaTokenVo»": {"title": "WebResult«CaptchaTokenVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CaptchaTokenVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-device-token": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-device-token", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}