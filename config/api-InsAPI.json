{"openapi": "3.0.3", "info": {"title": "Kakao API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "Ins 用户 API", "description": "Ins User Controller"}], "paths": {"/api/ins/user/acquire": {"put": {"tags": ["InsUserController"], "summary": "认领达人", "operationId": "insUserAcquirePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ins/user/allocate": {"put": {"tags": ["InsUserController"], "summary": "分配达人", "operationId": "insUserAllocatePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsCreatorAllocateRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ins/user/cancelAllocate": {"put": {"tags": ["InsUserController"], "summary": "取消分配", "operationId": "insUserCancelAllocatePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ins/user/count": {"get": {"tags": ["InsUserController"], "summary": "统计团队达人的数量", "operationId": "insUserCountGet", "parameters": [{"name": "containsTag", "in": "query", "description": "包含指定标签。取false时，tagLc只能是OR", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "createTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "createTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "filterByFavorite", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "followerCountFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followerCountTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followingCountFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followingCountTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "handle", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "hasResponsibleUser", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "isBusiness", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "isVerified", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "lastSyncTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "mediaCountFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "mediaCountTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "messageStatusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, {"name": "responsibleUserId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "statusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, {"name": "tagIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "tagLc", "in": "query", "description": "标签的逻辑条件，支持OR|AND", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«long»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ins/user/delete": {"post": {"tags": ["InsUserController"], "summary": "删除达人", "operationId": "insUserDeletePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ins/user/deleteByQuery": {"post": {"tags": ["InsUserController"], "summary": "删除达人（按查询）", "operationId": "insUserDeleteByQueryPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FindInsUserRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ins/user/fields": {"get": {"tags": ["InsUserController"], "summary": "达人可导出的字段元信息", "operationId": "insUserFieldsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkCreatorFiledVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ins/user/page": {"get": {"tags": ["InsUserController"], "summary": "分页查询团队ins用户(不返回总数）", "operationId": "insUserPageGet", "parameters": [{"name": "containsTag", "in": "query", "description": "包含指定标签。取false时，tagLc只能是OR", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "createTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "createTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "filterByFavorite", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "followerCountFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followerCountTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followingCountFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followingCountTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "handle", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "hasResponsibleUser", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "isBusiness", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "isVerified", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "lastSyncTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "mediaCountFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "mediaCountTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "messageStatusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "responsibleUserId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "statusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, {"name": "tagIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "tagLc", "in": "query", "description": "标签的逻辑条件，支持OR|AND", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«InsTeamUserDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ins/user/pageResponsible": {"get": {"tags": ["InsUserController"], "summary": "分页查询我认领的达人", "operationId": "insUserPageResponsibleGet", "parameters": [{"name": "containsTag", "in": "query", "description": "包含指定标签。取false时，tagLc只能是OR", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "createTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "createTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "filterByFavorite", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "followerCountFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followerCountTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followingCountFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followingCountTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "handle", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "hasResponsibleUser", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "isBusiness", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "isVerified", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "lastSyncTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "mediaCountFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "mediaCountTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "messageStatusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "responsibleUserId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "statusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, {"name": "tagIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "tagLc", "in": "query", "description": "标签的逻辑条件，支持OR|AND", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«InsTeamUserDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ins/user/release": {"put": {"tags": ["InsUserController"], "summary": "取消认领达人", "operationId": "insUserReleasePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ins/user/responsibleUserList": {"get": {"tags": ["InsUserController"], "summary": "getResponsibleUserList", "operationId": "insUserResponsibleUserListGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«long»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ins/user/syncBatch": {"post": {"tags": ["InsUserController"], "summary": "批量同步（创建）ins用户", "operationId": "insUserSyncBatchPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsUserDocumentBatch"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«InsTeamUserDto»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ins/user/tag": {"post": {"tags": ["InsUserController"], "summary": "给指定Ins用户打标签", "operationId": "insUserTagPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ins/user/untag": {"post": {"tags": ["InsUserController"], "summary": "取消指定Ins用户的特定标签", "operationId": "insUserUntagPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ins/user/updateMessageStatus": {"put": {"tags": ["InsUserController"], "summary": "批量更新达人是否关注后私信", "operationId": "insUserUpdateMessageStatusPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsUserUpdateMessageStatusRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ins/user/updateStatus": {"put": {"tags": ["InsUserController"], "summary": "批量更新达人状态", "operationId": "insUserUpdateStatusPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsUserUpdateStatusRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«GhCreatorDto»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ins/user/{id}": {"get": {"tags": ["InsUserController"], "summary": "查询达人详情", "operationId": "insUserByIdGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«InsTeamUserDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"CommonIdsRequest": {"title": "CommonIdsRequest", "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "FindInsUserRequest": {"title": "FindInsUserRequest", "type": "object", "properties": {"containsTag": {"type": "boolean", "description": "包含指定标签。取false时，tagLc只能是OR", "example": false}, "createTimeFrom": {"type": "string", "format": "date-time"}, "createTimeTo": {"type": "string", "format": "date-time"}, "filterByFavorite": {"type": "boolean"}, "followerCountFrom": {"type": "integer", "format": "int32"}, "followerCountTo": {"type": "integer", "format": "int32"}, "followingCountFrom": {"type": "integer", "format": "int32"}, "followingCountTo": {"type": "integer", "format": "int32"}, "handle": {"type": "string"}, "hasResponsibleUser": {"type": "boolean"}, "isBusiness": {"type": "boolean"}, "isVerified": {"type": "boolean"}, "lastSyncTimeFrom": {"type": "string", "format": "date-time"}, "lastSyncTimeTo": {"type": "string", "format": "date-time"}, "mediaCountFrom": {"type": "integer", "format": "int32"}, "mediaCountTo": {"type": "integer", "format": "int32"}, "messageStatusList": {"type": "array", "items": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, "responsibleUserId": {"type": "integer", "format": "int64"}, "statusList": {"type": "array", "items": {"type": "string", "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, "tagIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "tagLc": {"type": "string", "description": "标签的逻辑条件，支持OR|AND", "enum": ["AND", "NOT", "OR"]}}}, "GhCreatorDto": {"title": "GhCreatorDto", "type": "object", "properties": {"alias": {"type": "string"}, "anchor": {"type": "boolean"}, "available": {"type": "string", "enum": ["Available", "Unavailable", "Unset"]}, "avatar": {"type": "string"}, "avgLikes": {"type": "number", "format": "double"}, "avgRevenue": {"type": "number", "format": "double"}, "avgViewers": {"type": "number", "format": "double"}, "backstageShopId": {"type": "integer", "format": "int64"}, "commerceUser": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "string"}, "deleteRemark": {"type": "string"}, "elite": {"type": "boolean"}, "email": {"type": "string"}, "followerCnt": {"type": "integer", "format": "int32"}, "gaming": {"type": "boolean"}, "general": {"type": "boolean"}, "handle": {"type": "string"}, "hasNewMsg": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "importTime": {"type": "string", "format": "date-time"}, "importType": {"type": "string", "enum": ["Collected", "Imported"]}, "lastGameRank": {"type": "number", "format": "double"}, "lastHourRevenue": {"type": "number", "format": "double"}, "lastInteractTime": {"type": "string", "format": "date-time"}, "lastInteractType": {"type": "string", "enum": ["ImportCreator", "Invite", "SendMsg", "SyncMsg", "UpdateInfo"]}, "lastLikes": {"type": "integer", "format": "int32"}, "lastLiveTime": {"type": "string", "format": "date-time"}, "lastPopularRank": {"type": "number", "format": "double"}, "lastRevenue": {"type": "number", "format": "double"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "lastViewers": {"type": "integer", "format": "int32"}, "lastWeekRevenue": {"type": "number", "format": "double"}, "liveRate": {"type": "number", "format": "double"}, "liveRewardsMobileAccountId": {"type": "integer", "format": "int64"}, "liveRewardsShopId": {"type": "integer", "format": "int64"}, "maxGameRank": {"type": "number", "format": "double"}, "maxHourRevenue": {"type": "number", "format": "double"}, "maxLikes": {"type": "integer", "format": "int32"}, "maxPopularRank": {"type": "number", "format": "double"}, "maxRevenue": {"type": "number", "format": "double"}, "maxViewers": {"type": "integer", "format": "int32"}, "maxWeekRevenue": {"type": "number", "format": "double"}, "messageStatus": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}, "mobile": {"type": "string"}, "mobileAccountId": {"type": "integer", "format": "int64"}, "officialShopId": {"type": "integer", "format": "int64"}, "platformType": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}, "pmShopId": {"type": "integer", "format": "int64"}, "region": {"type": "string"}, "responsibleUserId": {"type": "integer", "format": "int64"}, "showcase": {"type": "boolean"}, "status": {"type": "string", "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}, "teamId": {"type": "integer", "format": "int64"}, "ttSeller": {"type": "boolean"}, "unionShopId": {"type": "integer", "format": "int64"}, "videoCnt": {"type": "integer", "format": "int32"}, "videoLikes": {"type": "integer", "format": "int32"}, "whatsapp": {"type": "string"}}}, "GhCreatorRequest": {"title": "GhCreatorRequest", "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "InsCreatorAllocateRequest": {"title": "InsCreatorAllocateRequest", "type": "object", "properties": {"creatorIds": {"type": "array", "items": {"type": "string"}}, "handles": {"type": "array", "items": {"type": "string"}}, "ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "responsibleUserId": {"type": "integer", "format": "int64"}, "tag": {"type": "string"}}}, "InsTeamUserDetailVo": {"title": "InsTeamUserDetailVo", "type": "object", "properties": {"accountType": {"type": "integer", "format": "int32"}, "avatar": {"type": "string"}, "biography": {"type": "string"}, "cityName": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "string"}, "followerCount": {"type": "integer", "format": "int32"}, "followingCount": {"type": "integer", "format": "int32"}, "fullName": {"type": "string"}, "handle": {"type": "string"}, "hasNewMsg": {"type": "boolean"}, "hyperlink": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "isBusiness": {"type": "boolean"}, "isVerified": {"type": "boolean"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "mediaCount": {"type": "integer", "format": "int32"}, "messageStatus": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}, "mobileAccountId": {"type": "integer", "format": "int64"}, "pmShopId": {"type": "integer", "format": "int64"}, "responsibleUser": {"$ref": "#/components/schemas/UserDto"}, "responsibleUserId": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}, "tags": {"type": "array", "description": "标签", "items": {"$ref": "#/components/schemas/TagDto"}}, "teamId": {"type": "integer", "format": "int64"}}}, "InsTeamUserDto": {"title": "InsTeamUserDto", "type": "object", "properties": {"accountType": {"type": "integer", "format": "int32"}, "avatar": {"type": "string"}, "biography": {"type": "string"}, "cityName": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "string"}, "followerCount": {"type": "integer", "format": "int32"}, "followingCount": {"type": "integer", "format": "int32"}, "fullName": {"type": "string"}, "handle": {"type": "string"}, "hasNewMsg": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "isBusiness": {"type": "boolean"}, "isVerified": {"type": "boolean"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "mediaCount": {"type": "integer", "format": "int32"}, "messageStatus": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}, "mobileAccountId": {"type": "integer", "format": "int64"}, "pmShopId": {"type": "integer", "format": "int64"}, "responsibleUserId": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}, "teamId": {"type": "integer", "format": "int64"}}}, "InsUserDocument": {"title": "InsUserDocument", "type": "object", "properties": {"accountType": {"type": "integer", "format": "int32"}, "avatar": {"type": "string"}, "biography": {"type": "string"}, "categories": {"type": "array", "items": {"type": "string"}}, "cityName": {"type": "string"}, "creatorId": {"type": "string", "description": "pk"}, "followerCount": {"type": "integer", "format": "int32"}, "followingCount": {"type": "integer", "format": "int32"}, "fullName": {"type": "string"}, "handle": {"type": "string", "description": "username字段"}, "isBusiness": {"type": "boolean"}, "isVerified": {"type": "boolean"}, "mediaCount": {"type": "integer", "format": "int32"}, "messageStatus": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}}, "InsUserDocumentBatch": {"title": "InsUserDocumentBatch", "type": "object", "properties": {"users": {"type": "array", "items": {"$ref": "#/components/schemas/InsUserDocument"}}}}, "InsUserUpdateMessageStatusRequest": {"title": "InsUserUpdateMessageStatusRequest", "type": "object", "properties": {"creatorIds": {"type": "array", "items": {"type": "string"}}, "handles": {"type": "array", "items": {"type": "string"}}, "ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "messageStatus": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}}, "InsUserUpdateStatusRequest": {"title": "InsUserUpdateStatusRequest", "type": "object", "properties": {"creatorIds": {"type": "array", "items": {"type": "string"}}, "handles": {"type": "array", "items": {"type": "string"}}, "ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "status": {"type": "string", "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}}, "PageResult«InsTeamUserDetailVo»": {"title": "PageResult«InsTeamUserDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/InsTeamUserDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "TagCreatorRequest": {"title": "TagCreatorRequest", "type": "object", "properties": {"handles": {"type": "array", "items": {"type": "string"}}, "tags": {"type": "array", "items": {"type": "string"}}}}, "TagDto": {"title": "TagDto", "type": "object", "properties": {"bizCode": {"type": "string"}, "color": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "expireTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "resourceType": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}, "system": {"type": "boolean"}, "tag": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}}}, "TkCreatorFiledVo": {"title": "TkCreatorFiledVo", "type": "object", "properties": {"desc": {"type": "string", "description": "字段描述（可能为null）"}, "filedType": {"type": "string", "enum": ["CreatorColumn", "CreatorContact", "CreatorProp", "CreatorStat", "ProductColumn"]}, "force": {"type": "boolean", "description": "方案必须包含", "example": false}, "key": {"type": "string", "description": "字段Key"}, "label": {"type": "string", "description": "字段名称"}, "path": {"type": "string"}}}, "UserDto": {"title": "UserDto", "type": "object", "properties": {"avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "gender": {"type": "string", "enum": ["FEMALE", "MALE", "UNSPECIFIC"]}, "id": {"type": "integer", "format": "int64"}, "nickname": {"type": "string"}, "partnerId": {"type": "integer", "format": "int64"}, "residentCity": {"type": "string"}, "signature": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}, "tenant": {"type": "integer", "format": "int64"}, "userType": {"type": "string", "enum": ["NORMAL", "PARTNER", "SHADOW"]}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«InsTeamUserDetailVo»": {"title": "WebResult«InsTeamUserDetailVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/InsTeamUserDetailVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«GhCreatorDto»»": {"title": "WebResult«List«GhCreatorDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/GhCreatorDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«InsTeamUserDto»»": {"title": "WebResult«List«InsTeamUserDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/InsTeamUserDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkCreatorFiledVo»»": {"title": "WebResult«List«TkCreatorFiledVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkCreatorFiledVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«long»»": {"title": "WebResult«List«long»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«InsTeamUserDetailVo»»": {"title": "WebResult«PageResult«InsTeamUserDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«InsTeamUserDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«int»": {"title": "WebResult«int»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«long»": {"title": "WebResult«long»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "integer", "format": "int64"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-device-token": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-device-token", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}