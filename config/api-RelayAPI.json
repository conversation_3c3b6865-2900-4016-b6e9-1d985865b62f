{"openapi": "3.0.3", "info": {"title": "Relay API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "IPGO API", "description": "IPGO Controller"}, {"name": "Proxy API", "description": "Proxy Controller"}, {"name": "Proxy Remote API（供Proxy调用）", "description": "Proxy Remote Controller"}, {"name": "Remote ApiWorker Service", "description": "Remote Api Worker Service Impl"}, {"name": "Remote Proxy Service", "description": "Remote Tunnel Service Impl"}, {"name": "Tunnel transit API", "description": "Tunnel Transit Controller"}], "paths": {"/api/ipgo/page": {"get": {"tags": ["IPGOController"], "summary": "分页查询IPGO", "operationId": "ipgoPageGet", "parameters": [{"name": "hostName", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "ip", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "osName", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["create_time", "host_name", "inner_ip", "os_name", "public_ip", "update_time", "uptime"]}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "status", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["DELETED", "READY", "STOPPED"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«GatewayDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ipgo/{id}/forceReportIp": {"get": {"tags": ["IPGOController"], "summary": "强制IPGO汇报IP", "operationId": "ipgoByIdForceReportIpGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ipgo/{id}/import": {"put": {"tags": ["IPGOController"], "summary": "设置IPGO的导入IP列表", "operationId": "ipgoByIdImportPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ImportIpgoIpRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ipgo/{id}/restart": {"get": {"tags": ["IPGOController"], "summary": "重启IPGO", "operationId": "ipgoByIdRestartGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ipgo/{id}/uninstall": {"get": {"tags": ["IPGOController"], "summary": "卸载IPGO", "operationId": "ipgoByIdUninstallGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "confirm", "in": "query", "description": "confirm", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/proxy/installScript": {"get": {"tags": ["ProxyController"], "summary": "获取安装脚本", "operationId": "proxyInstallScriptGet", "parameters": [{"name": "expireSeconds", "in": "query", "description": "超时时间：秒", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«InstallScriptVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/proxy/list": {"get": {"tags": ["ProxyController"], "summary": "查询Proxy列表", "operationId": "proxyListGet", "parameters": [{"name": "teamId", "in": "query", "description": "所属团队", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "provider", "in": "query", "description": "云厂商类型", "required": false, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}}, {"name": "region", "in": "query", "description": "云厂商区域", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "status", "in": "query", "description": "状态", "required": false, "style": "form", "schema": {"type": "string", "enum": ["DELETED", "READY", "STOPPED"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«GatewayDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/proxy/template": {"post": {"tags": ["ProxyController"], "summary": "创建一个批量导入Proxy的模版", "operationId": "proxyTemplatePost", "parameters": [{"name": "teamId", "in": "query", "description": "所属团队", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ProxyTemplateVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/proxy/tunnelToken": {"post": {"tags": ["ProxyController"], "summary": "创建一个Proxy的连接Token", "operationId": "proxyTunnelTokenPost", "parameters": [{"name": "teamId", "in": "query", "description": "所属团队", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "provider", "in": "query", "description": "云厂商类型", "required": false, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}}, {"name": "region", "in": "query", "description": "云厂商区域", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "encoded", "in": "query", "description": "是否编码", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "expireSeconds", "in": "query", "description": "超时时间（秒），0表示永不超时", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ProxyTokenCodeVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/proxy/versionInfo": {"get": {"tags": ["ProxyController"], "summary": "获取网关版本和下载信息", "operationId": "proxyVersionInfoGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ProxyVersionInfo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/proxy/{proxyId}/tunnelConnStat": {"get": {"tags": ["ProxyController"], "summary": "获取通道连接统计", "operationId": "proxyByProxyIdTunnelConnStatGet", "parameters": [{"name": "proxyId", "in": "path", "description": "proxyId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TunnelConnStat»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/proxy/remote/importIp": {"post": {"tags": ["ProxyRemoteController"], "summary": "Proxy导入远程IP", "operationId": "proxyRemoteImportIpPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportIpRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ReportIpResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/proxy/remote/metas": {"get": {"tags": ["ProxyRemoteController"], "summary": "获取proxy元信息，包括中转列表和团队信息", "operationId": "proxyRemoteMetasGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ProxyMeta»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/proxy/remote/remoteIp": {"get": {"tags": ["ProxyRemoteController"], "summary": "获取远程IP", "operationId": "proxyRemoteRemoteIpGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "delete": {"tags": ["ProxyRemoteController"], "summary": "Proxy删除远程IP", "operationId": "proxyRemoteRemoteIpDelete", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportIpRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ReportIpResult»"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/proxy/remote/reportIp": {"post": {"tags": ["ProxyRemoteController"], "summary": "Proxy汇报远程IP", "operationId": "proxyRemoteReportIpPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportIpRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ReportIpResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/proxy/remote/templateTunnelToken": {"get": {"tags": ["ProxyRemoteController"], "summary": "根据模版获取一个Proxy Token", "operationId": "proxyRemoteTemplateTunnelTokenGet", "parameters": [{"name": "provider", "in": "query", "description": "云厂商类型", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "region", "in": "query", "description": "云厂商区域", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "instanceId", "in": "query", "description": "主机instanceId", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "expireSeconds", "in": "query", "description": "超时（秒）", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CheckCodeResult»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/proxy/remote/tunnelToken/byCode": {"get": {"tags": ["ProxyRemoteController"], "summary": "根据接入码获取Proxy Token", "operationId": "proxyRemoteTunnelTokenByCodeGet", "parameters": [{"name": "code", "in": "query", "description": "code", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CheckCodeResult»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/apiWorker/{identifier}/ipp/checkParams": {"post": {"tags": ["RemoteApiWorkerServiceImpl"], "summary": "检查IP池的参数", "operationId": "remoteApiWorkerByIdentifierIppCheckParamsPost", "parameters": [{"name": "identifier", "in": "path", "description": "identifier", "required": true, "style": "simple", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProduceIpRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/apiWorker/{identifier}/ipp/prepareProduceIpSpec": {"post": {"tags": ["RemoteApiWorkerServiceImpl"], "summary": "让ip池生产调用规范", "operationId": "remoteApiWorkerByIdentifierIppPrepareProduceIpSpecPost", "parameters": [{"name": "identifier", "in": "path", "description": "identifier", "required": true, "style": "simple", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProduceIpRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ProduceIpSpecVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/apiWorker/{identifier}/ipp/produce": {"post": {"tags": ["RemoteApiWorkerServiceImpl"], "summary": "让ip池生成一批IP", "operationId": "remoteApiWorkerByIdentifierIppProducePost", "parameters": [{"name": "identifier", "in": "path", "description": "identifier", "required": true, "style": "simple", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProduceIpRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ProduceIpResultVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/address": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "当前服务tunnel地址", "operationId": "remoteTunnelAddressGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TunnelAddress»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/blockProxy": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "阻塞Proxy事件线程（用于测试）", "operationId": "remoteTunnelBlockProxyGet", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "seconds", "in": "query", "description": "seconds", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/connectedPeers": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取目标端的已经连接的端", "operationId": "remoteTunnelConnectedPeersGet", "parameters": [{"name": "target", "in": "query", "description": "target", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpcConnectedPeers»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/createProxyPendingToken": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取一个等待启动的IPGO的Token", "operationId": "remoteTunnelCreateProxyPendingTokenGet", "parameters": [{"name": "teamId", "in": "query", "description": "teamId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "creatorId", "in": "query", "description": "creatorId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "provider", "in": "query", "description": "provider", "required": true, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}}, {"name": "region", "in": "query", "description": "region", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "expireSeconds", "in": "query", "description": "expireSeconds", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "orderItemId", "in": "query", "description": "orderItemId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ProxyTokenVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/dumpThread": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取目标端的线程信息", "operationId": "remoteTunnelDumpThreadGet", "parameters": [{"name": "target", "in": "query", "description": "target", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/exitProxy": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "通知Proxy停止（用于测试）", "operationId": "remoteTunnelExitProxyGet", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "exitCode", "in": "query", "description": "exitCode", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/gatewayEndpoint": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取网关升级网址前缀", "operationId": "remoteTunnelGatewayEndpointGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/gitInfo": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取目标端的GIT信息", "operationId": "remoteTunnelGitInfoGet", "parameters": [{"name": "target", "in": "query", "description": "target", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GitInfoVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/gitSpy": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取目标端的Spy信息", "operationId": "remoteTunnelGitSpyGet", "parameters": [{"name": "target", "in": "query", "description": "target", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "path", "in": "query", "description": "path", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "filter", "in": "query", "description": "filter", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "page", "in": "query", "description": "page", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "size", "in": "query", "description": "size", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«SpyVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/probeRemoteIp": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "探测remoteIp", "operationId": "remoteTunnelProbeRemoteIpPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoteProbeRemoteIpRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ProxyProbeWithRemoteIp»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/proxy/changePassword": {"put": {"tags": ["RemoteTunnelServiceImpl"], "summary": "IPGO修改特定用户的登录秘密", "operationId": "remoteTunnelProxyChangePasswordPut", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "user", "in": "query", "description": "user", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "password", "in": "query", "description": "password", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/proxy/changePwdLogin": {"put": {"tags": ["RemoteTunnelServiceImpl"], "summary": "IPGO修改Linux系统是否允许秘密登录", "operationId": "remoteTunnelProxyChangePwdLoginPut", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "enabled", "in": "query", "description": "enabled", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/proxy/connectTransit": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "触发Proxy连接最快中转2", "description": "从特定分组中连接，而不是所有", "operationId": "remoteTunnelProxyConnectTransitPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "forceReconnect", "in": "query", "description": "forceReconnect", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "transitId", "in": "query", "description": "transitId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "jumpIfNeed", "in": "query", "description": "jumpIfNeed", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/proxy/forceReportIp": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "触发Proxy一次IP汇报", "operationId": "remoteTunnelProxyForceReportIpPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/proxy/forceUpdate": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "强制更新", "operationId": "remoteTunnelProxyForceUpdatePost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/proxy/getTimestamp": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取Proxy时间戳", "operationId": "remoteTunnelProxyGetTimestampPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«long»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/proxy/pingFasttestAndConnectTransit": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "触发Proxy连接最快中转", "operationId": "remoteTunnelProxyPingFasttestAndConnectTransitPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "forceReconnect", "in": "query", "description": "forceReconnect", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "domestic", "in": "query", "description": "domestic", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TransitConnectionInfo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/proxy/pingFasttestAndConnectTransit2": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "触发Proxy连接最快中转2", "description": "从特定分组中连接，而不是所有", "operationId": "remoteTunnelProxyPingFasttestAndConnectTransit2Post", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "forceReconnect", "in": "query", "description": "forceReconnect", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "transitIds", "in": "query", "description": "transitIds", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TransitConnectionInfo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/proxy/pingFasttestAndConnectTransitById": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "触发Proxy连接最快中转", "operationId": "remoteTunnelProxyPingFasttestAndConnectTransitByIdPost", "parameters": [{"name": "proxyId", "in": "query", "description": "proxyId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "forceReconnect", "in": "query", "description": "forceReconnect", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "domestic", "in": "query", "description": "domestic", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TransitConnectionInfo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/proxy/probeTcp": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "通过中转（或门户）探测代理", "operationId": "remoteTunnelProxyProbeTcpPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "host", "in": "query", "description": "host", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "port", "in": "query", "description": "port", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ProbeResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/proxy/probeToTransit": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "通过Proxy探测中转连接性", "operationId": "remoteTunnelProxyProbeToTransitPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "localIp", "in": "query", "description": "localIp", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "transitId", "in": "query", "description": "transitId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ProxyProbeWithRemoteIp»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/proxy/runCmd": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取Proxy时间戳", "operationId": "remoteTunnelProxyRunCmdPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "cmds", "in": "query", "description": "cmds", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "timeout", "in": "query", "description": "timeout", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "charset", "in": "query", "description": "charset", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ExecResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/proxy/startDf2Server": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "启动一个DF2协议的Server", "operationId": "remoteTunnelProxyStartDf2ServerPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "bindPort", "in": "query", "description": "bindPort", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "contextPath", "in": "query", "description": "contextPath", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "ignoreLocalBindAddress", "in": "query", "description": "ignoreLocalBindAddress", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "sign<PERSON><PERSON>", "in": "query", "description": "sign<PERSON><PERSON>", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "https", "in": "query", "description": "https", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/proxy/startProxyForwardServer": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "启动一个代理服务", "operationId": "remoteTunnelProxyStartProxyForwardServerPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StartProxyForwardVo"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«StartProxyForwardResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/proxy/startSocks5Server": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "启动一个Raw Socks5 server", "operationId": "remoteTunnelProxyStartSocks5ServerPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "bindPort", "in": "query", "description": "bindPort", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "auth", "in": "query", "description": "auth", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "username", "in": "query", "description": "username", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "password", "in": "query", "description": "password", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/proxy/stopBindServer": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "停止一个Tcp Server监听", "operationId": "remoteTunnelProxyStopBindServerPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "bindPort", "in": "query", "description": "bindPort", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/proxy/stopProxyForwardServers": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "停止代理服务", "operationId": "remoteTunnelProxyStopProxyForwardServersPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StopProxyForwardVo"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/proxy/storeSshKey": {"put": {"tags": ["RemoteTunnelServiceImpl"], "summary": "IPGO替换用户的登录ssh公钥", "operationId": "remoteTunnelProxyStoreSshKeyPut", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "user", "in": "query", "description": "user", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "ssh<PERSON>ey", "in": "query", "description": "ssh<PERSON>ey", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/proxy/updateProxyForwardServerAuth": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "修改代理服务", "operationId": "remoteTunnelProxyUpdateProxyForwardServerAuthPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProxyForwardAuth"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/proxy/userData": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取Proxy主机启动脚本userdata", "operationId": "remoteTunnelProxyUserDataGet", "parameters": [{"name": "ipgoScriptType", "in": "query", "description": "ipgoScriptType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["initialize", "install"]}}, {"name": "provider", "in": "query", "description": "provider", "required": true, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}}, {"name": "regionId", "in": "query", "description": "regionId", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "tcpFp", "in": "query", "description": "tcpFp", "required": true, "style": "form", "schema": {"type": "boolean"}}, {"name": "platform", "in": "query", "description": "platform", "required": true, "style": "form", "schema": {"type": "string", "enum": ["android", "linux", "macos", "unknown", "web", "windows", "windows7"]}}, {"name": "teamId", "in": "query", "description": "teamId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "creatorId", "in": "query", "description": "creatorId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "base64", "in": "query", "description": "base64", "required": true, "style": "form", "schema": {"type": "boolean"}}, {"name": "orderItemId", "in": "query", "description": "orderItemId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/restartProxy": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "重启proxy（用于测试）", "operationId": "remoteTunnelRestartProxyGet", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/startTunnelTrace": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "开启TunnelTrace", "operationId": "remoteTunnelStartTunnelTraceGet", "parameters": [{"name": "target", "in": "query", "description": "target", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "module", "in": "query", "description": "module", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "minites", "in": "query", "description": "minites", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GitInfoVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/stopProxy": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "通知Proxy停止（之后可以手工启动）", "operationId": "remoteTunnelStopProxyGet", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/stopTunnelTrace": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "关闭TunnelTrace", "operationId": "remoteTunnelStopTunnelTraceGet", "parameters": [{"name": "target", "in": "query", "description": "target", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "module", "in": "query", "description": "module", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GitInfoVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/systemInfo": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取目标端的系统信息", "operationId": "remoteTunnelSystemInfoGet", "parameters": [{"name": "target", "in": "query", "description": "target", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«Map»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/template": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "创建一个批量导入Key", "operationId": "remoteTunnelTemplatePost", "parameters": [{"name": "teamId", "in": "query", "description": "teamId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ProxyTemplateVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/template/getOrCreate": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取一个有效的批量导入Key（如果不存在则创建）", "operationId": "remoteTunnelTemplateGetOrCreateGet", "parameters": [{"name": "teamId", "in": "query", "description": "teamId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ProxyTemplateVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/templates": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "创建一个批量导入Key模版", "operationId": "remoteTunnelTemplatesGet", "parameters": [{"name": "teamId", "in": "query", "description": "teamId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«GatewayTemplateDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/transit/probeProxy": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "通过中转（或门户）探测代理", "operationId": "remoteTunnelTransitProbeProxyPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProbeProxyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/transit/probeTcp": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "通过中转（或门户）探测代理", "operationId": "remoteTunnelTransitProbeTcpPost", "parameters": [{"name": "transitId", "in": "query", "description": "transitId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "host", "in": "query", "description": "host", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "port", "in": "query", "description": "port", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ProbeResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/tunnel/disconnectToTarget": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "通知tunnel端断开到特定目标的连接", "operationId": "remoteTunnelTunnelDisconnectToTargetPost", "parameters": [{"name": "source", "in": "query", "description": "source", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "target", "in": "query", "description": "target", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ForceDisconnectResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/tunnel/disconnectToTargetByType": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "通知tunnel端断开到所有特定类型目标的连接", "operationId": "remoteTunnelTunnelDisconnectToTargetByTypePost", "parameters": [{"name": "source", "in": "query", "description": "source", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "type", "in": "query", "description": "type", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ForceDisconnectResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/tunnel/routerL1": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取Tunnel一级路由", "operationId": "remoteTunnelTunnelRouterL1Get", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«Map«string,string»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/tunnel/routerL2": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取Tunnel二级路由", "operationId": "remoteTunnelTunnelRouterL2Get", "parameters": [{"name": "target", "in": "query", "description": "target", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TunnelAddress»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/tunnelConnStat": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取目标端的连接统计信息", "operationId": "remoteTunnelTunnelConnStatGet", "parameters": [{"name": "target", "in": "query", "description": "target", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TunnelConnStat»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/tunnel/uninstallProxy": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "通知Proxy卸载（删除cred文件并停止）", "operationId": "remoteTunnelUninstallProxyGet", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tunnel/pingTransits": {"get": {"tags": ["TunnelTransitController"], "summary": "getTransitInfos", "operationId": "tunnelPingTransitsGet", "parameters": [{"name": "token", "in": "query", "description": "token", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json;charset=UTF-8": {"schema": {"$ref": "#/components/schemas/WebResult«Map»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"CheckCodeResult": {"title": "CheckCodeResult", "type": "object", "properties": {"endpoint": {"type": "string"}, "gatewayId": {"type": "integer", "format": "int64"}, "identifier": {"type": "string"}, "team": {"$ref": "#/components/schemas/ProxyTeamVo"}, "teamId": {"type": "integer", "format": "int64"}, "teamName": {"type": "string"}, "token": {"type": "string"}, "transits": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/TransitInfo"}}}}, "DynamicForwardStat": {"title": "DynamicForwardStat", "type": "object", "properties": {"pipes": {"type": "integer", "format": "int32"}, "sessions": {"type": "integer", "format": "int32"}}}, "ExecResult": {"title": "ExecResult", "type": "object", "properties": {"cmd": {"type": "array", "items": {"type": "string"}}, "error": {"type": "string"}, "exitCode": {"type": "integer", "format": "int32"}, "out": {"type": "string"}}}, "FieldInfo": {"title": "FieldInfo", "type": "object", "properties": {"hashCode": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "path": {"type": "string"}, "printable": {"type": "boolean"}, "size": {"type": "integer", "format": "int32"}, "valueClass": {"type": "string"}, "valueString": {"type": "string"}}}, "FieldsInfo": {"title": "FieldsInfo", "type": "object", "properties": {"clazz": {"type": "string"}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/FieldInfo"}}}}, "ForceDisconnectResult": {"title": "ForceDisconnectResult", "type": "object", "properties": {"disconnectedAddresses": {"type": "array", "items": {"$ref": "#/components/schemas/TunnelAddress"}}}}, "GatewayDetailVo": {"title": "GatewayDetailVo", "type": "object", "properties": {"associatedId": {"type": "integer", "format": "int64"}, "associatedType": {"type": "string"}, "buildTime": {"type": "string", "format": "date-time"}, "cloudProvider": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}, "commitId": {"type": "string"}, "cores": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "creator": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "dfPort": {"type": "integer", "format": "int32"}, "domestic": {"type": "boolean"}, "gatewayIps": {"type": "array", "items": {"$ref": "#/components/schemas/GatewayIpDto"}}, "hostName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "identifier": {"type": "string"}, "innerIp": {"type": "string"}, "instanceId": {"type": "string"}, "ipVersion": {"type": "string", "enum": ["All", "Custom", "IPv4", "IPv6", "None"]}, "memory": {"type": "integer", "format": "int32"}, "orderItemId": {"type": "integer", "format": "int64"}, "osArch": {"type": "string"}, "osName": {"type": "string"}, "osVersion": {"type": "string"}, "publicIp": {"type": "string"}, "region": {"type": "string"}, "remoteIp": {"type": "string"}, "secretKey": {"type": "string"}, "status": {"type": "string", "enum": ["DELETED", "READY", "STOPPED"]}, "teamId": {"type": "integer", "format": "int64"}, "templateId": {"type": "integer", "format": "int64"}, "updateEnabled": {"type": "boolean"}, "updateTime": {"type": "string", "format": "date-time"}, "uptime": {"type": "string", "format": "date-time"}, "version": {"type": "string"}, "vpsId": {"type": "integer", "format": "int64"}}}, "GatewayDto": {"title": "GatewayDto", "type": "object", "properties": {"associatedId": {"type": "integer", "format": "int64"}, "associatedType": {"type": "string"}, "buildTime": {"type": "string", "format": "date-time"}, "cloudProvider": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}, "commitId": {"type": "string"}, "cores": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "creator": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "dfPort": {"type": "integer", "format": "int32"}, "domestic": {"type": "boolean"}, "hostName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "identifier": {"type": "string"}, "innerIp": {"type": "string"}, "instanceId": {"type": "string"}, "ipVersion": {"type": "string", "enum": ["All", "Custom", "IPv4", "IPv6", "None"]}, "memory": {"type": "integer", "format": "int32"}, "orderItemId": {"type": "integer", "format": "int64"}, "osArch": {"type": "string"}, "osName": {"type": "string"}, "osVersion": {"type": "string"}, "publicIp": {"type": "string"}, "region": {"type": "string"}, "remoteIp": {"type": "string"}, "secretKey": {"type": "string"}, "status": {"type": "string", "enum": ["DELETED", "READY", "STOPPED"]}, "teamId": {"type": "integer", "format": "int64"}, "templateId": {"type": "integer", "format": "int64"}, "updateEnabled": {"type": "boolean"}, "updateTime": {"type": "string", "format": "date-time"}, "uptime": {"type": "string", "format": "date-time"}, "version": {"type": "string"}, "vpsId": {"type": "integer", "format": "int64"}}}, "GatewayIpDto": {"title": "GatewayIpDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "gatewayId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "iface": {"type": "string"}, "imported": {"type": "boolean"}, "innerIp": {"type": "string"}, "invalidTime": {"type": "string", "format": "date-time"}, "ip": {"type": "string"}, "ipId": {"type": "integer", "format": "int64"}, "ipv6": {"type": "boolean"}, "locale": {"type": "string"}, "macAddr": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "timezone": {"type": "string"}, "valid": {"type": "boolean"}}}, "GatewayTemplateDto": {"title": "GatewayTemplateDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "identifier": {"type": "string"}, "importType": {"type": "string", "enum": ["Platform", "User"]}, "secretKey": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "token": {"type": "string"}, "valid": {"type": "boolean"}}}, "GitInfoVo": {"title": "GitInfoVo", "type": "object", "properties": {"branch": {"type": "string"}, "buildNumber": {"type": "string"}, "buildTime": {"type": "string", "format": "date-time"}, "buildVersion": {"type": "string"}, "commitIdAbbrev": {"type": "string"}, "commitIdFull": {"type": "string"}, "commitTime": {"type": "string", "format": "date-time"}}}, "ImportIpgoIpRequest": {"title": "ImportIpgoIpRequest", "type": "object", "properties": {"importedIps": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "ipVersion": {"type": "string", "enum": ["All", "Custom", "IPv4", "IPv6", "None"]}, "unimportedIps": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "InstallScriptVo": {"title": "InstallScriptVo", "type": "object", "properties": {"batScript": {"type": "string"}, "endpoint": {"type": "string"}, "importKey": {"type": "string"}, "linuxDownloadUrl": {"type": "string"}, "psScript": {"type": "string"}, "shScript": {"type": "string"}, "windowsDownloadUrl": {"type": "string"}}}, "IpLocation": {"title": "IpLocation", "type": "object", "properties": {"city": {"type": "string"}, "cityEn": {"type": "string"}, "continent": {"type": "string"}, "continentCode": {"type": "string"}, "continentEn": {"type": "string"}, "country": {"type": "string"}, "countryEn": {"type": "string"}, "countyCode": {"type": "string"}, "geonameId": {"type": "integer", "format": "int32"}, "inEu": {"type": "boolean"}, "ip": {"type": "string"}, "latitude": {"type": "number", "format": "double"}, "locale": {"type": "string"}, "longitude": {"type": "number", "format": "double"}, "postalCode": {"type": "string"}, "province": {"type": "string"}, "provinceCode": {"type": "string"}, "provinceEn": {"type": "string"}, "timezone": {"type": "string"}}}, "IpProfile": {"title": "IpProfile", "type": "object", "properties": {"checkDelay": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "iface": {"type": "string"}, "innerIp": {"type": "string"}, "ipv6": {"type": "boolean"}, "locale": {"type": "string"}, "macAddr": {"type": "string"}, "remoteIp": {"type": "string"}, "timezone": {"type": "string"}}}, "IppParamVo": {"title": "IppParamVo", "type": "object", "properties": {"paramKey": {"type": "string"}, "paramType": {"type": "string", "enum": ["ApiEndpoint", "ApiEndpointRaw", "A<PERSON>H<PERSON><PERSON>", "ApiHttpMethod", "ApiPassword", "Api<PERSON>ueryP<PERSON><PERSON>", "ApiSignKey", "ApiUsername", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GlobalWhitelist", "MinApiInterval", "MinApiNum", "ProxyPassword", "ProxyType", "ProxyUsername", "Proxy<PERSON><PERSON><PERSON><PERSON>", "Unknown"]}, "paramValue": {"type": "string"}}}, "ObjectInfo": {"title": "ObjectInfo", "type": "object", "properties": {"clazz": {"type": "string"}, "key": {"type": "string"}, "keyUrl": {"type": "string"}, "toString": {"type": "string"}}}, "PageResult«GatewayDetailVo»": {"title": "PageResult«GatewayDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/GatewayDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PeerConnStat": {"title": "PeerConnStat", "type": "object", "properties": {"clientDfSessionStat": {"$ref": "#/components/schemas/DynamicForwardStat"}, "peerAddress": {"$ref": "#/components/schemas/TunnelAddress"}, "serverDfSessionStat": {"$ref": "#/components/schemas/DynamicForwardStat"}}}, "ProbeProxyRequest": {"title": "ProbeProxyRequest", "type": "object", "properties": {"parserType": {"type": "string", "enum": ["ifconfigme", "ipsb", "lumtest", "transit"]}, "proxyConfig": {"$ref": "#/components/schemas/ProxyConfig"}, "timeout": {"type": "integer", "format": "int32"}, "transitEndpoint": {"type": "string"}, "transitId": {"type": "integer", "format": "int64"}}}, "ProbeResult": {"title": "ProbeResult", "type": "object", "properties": {"connectTime": {"type": "integer", "format": "int64"}, "error": {"type": "string"}, "originalError": {"type": "string"}, "reachable": {"type": "boolean"}, "remoteIpEndpoint": {"type": "string"}, "success": {"type": "boolean"}, "testingTime": {"type": "integer", "format": "int64"}}}, "ProduceIpRequest": {"title": "ProduceIpRequest", "type": "object", "properties": {"connectTimeout": {"type": "integer", "format": "int32"}, "ippId": {"type": "integer", "format": "int64"}, "method": {"type": "string"}, "num": {"type": "integer", "format": "int32"}, "params": {"type": "array", "items": {"$ref": "#/components/schemas/IppParamVo"}}, "provider": {"type": "string"}, "readTimeout": {"type": "integer", "format": "int32"}}}, "ProduceIpResultVo": {"title": "ProduceIpResultVo", "type": "object", "properties": {"ips": {"type": "array", "items": {"$ref": "#/components/schemas/RotatingIp"}}, "remainQuantity": {"type": "integer", "format": "int32"}}}, "ProduceIpSpecVo": {"title": "ProduceIpSpecVo", "type": "object", "properties": {"headers": {"type": "object", "additionalProperties": {"type": "string"}, "description": "需要追加的请求头"}, "method": {"type": "string", "description": "请求方法，比如GET/POST等"}, "url": {"type": "string", "description": "请求URL，携带参数"}}}, "ProxyConfig": {"title": "ProxyConfig", "type": "object", "properties": {"host": {"type": "string"}, "ipVersion": {"type": "string", "enum": ["Auto", "IPv4", "IPv6"]}, "password": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "proxyType": {"type": "string", "enum": ["http", "httpTunnel", "https", "socks4", "socks5", "ssh"]}, "sshKey": {"type": "string"}, "username": {"type": "string"}}}, "ProxyMeta": {"title": "ProxyMeta", "type": "object", "properties": {"endpoint": {"type": "string"}, "team": {"$ref": "#/components/schemas/ProxyTeamVo"}, "transits": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/TransitInfo"}}}}, "ProxyProbeWithRemoteIp": {"title": "ProxyProbeWithRemoteIp", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "connectTime": {"type": "integer", "format": "int64"}, "error": {"type": "string"}, "handshakeTime": {"type": "integer", "format": "int64"}, "originalError": {"type": "string"}, "output": {"type": "string"}, "proto": {"type": "string"}, "reachable": {"type": "boolean"}, "remoteIp": {"type": "string"}, "remoteIpEndpoint": {"type": "string"}, "success": {"type": "boolean"}, "testingTime": {"type": "integer", "format": "int64"}}}, "ProxyTeamVo": {"title": "ProxyTeamVo", "type": "object", "properties": {"domestic": {"type": "boolean"}, "gatewayId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "teamName": {"type": "string"}}}, "ProxyTemplateVo": {"title": "ProxyTemplateVo", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "identifier": {"type": "string"}, "importType": {"type": "string", "enum": ["Platform", "User"]}, "teamId": {"type": "integer", "format": "int64"}, "token": {"type": "string"}, "valid": {"type": "boolean"}}}, "ProxyTokenCodeVo": {"title": "ProxyTokenCodeVo", "type": "object", "properties": {"code": {"type": "string", "description": "接入码"}, "expireTime": {"type": "string", "description": "过期时间", "format": "date-time"}, "identifier": {"type": "string", "description": "标识"}, "token": {"type": "string", "description": "Proxy授权码"}, "tokenEncoded": {"type": "boolean", "description": "token是否编码", "example": false}}}, "ProxyTokenVo": {"title": "ProxyTokenVo", "type": "object", "properties": {"expireTime": {"type": "string", "description": "过期时间", "format": "date-time"}, "identifier": {"type": "string", "description": "标识"}, "token": {"type": "string", "description": "Proxy授权码"}, "tokenEncoded": {"type": "boolean", "description": "token是否编码", "example": false}}}, "ProxyVersionInfo": {"title": "ProxyVersionInfo", "type": "object", "properties": {"latestVersion": {"type": "string"}, "linuxDownloadUrl": {"type": "string"}, "windowsDownloadUrl": {"type": "string"}}}, "RemoteProbeRemoteIpRequest": {"title": "RemoteProbeRemoteIpRequest", "type": "object", "properties": {"pc": {"$ref": "#/components/schemas/ProxyConfig"}, "timeout": {"type": "integer", "format": "int32"}, "url": {"type": "string"}}}, "ReportIpRequest": {"title": "ReportIpRequest", "type": "object", "properties": {"autoImportNewIp": {"type": "boolean"}, "autoRemoveIp": {"type": "boolean"}, "autoUpdateIp": {"type": "boolean"}, "ips": {"type": "array", "items": {"$ref": "#/components/schemas/IpProfile"}}}}, "ReportIpResult": {"title": "ReportIpResult", "type": "object", "properties": {"ips": {"type": "array", "items": {"$ref": "#/components/schemas/ReportedIpVo"}}}}, "ReportedIpVo": {"title": "ReportedIpVo", "type": "object", "properties": {"bindTeam": {"type": "integer", "format": "int64"}, "bindTeamName": {"type": "string"}, "checkDelay": {"type": "integer", "format": "int32"}, "domestic": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "iface": {"type": "string"}, "innerIp": {"type": "string"}, "ipv6": {"type": "boolean"}, "locale": {"type": "string"}, "location": {"$ref": "#/components/schemas/IpLocation"}, "locationId": {"type": "integer", "format": "int64"}, "macAddr": {"type": "string"}, "message": {"type": "string"}, "previousIp": {"type": "string"}, "remoteIp": {"type": "string"}, "status": {"type": "string", "enum": ["BoundByOtherTeam", "Imported", "NotImported"]}, "timezone": {"type": "string"}}}, "RotatingIp": {"title": "RotatingIp", "type": "object", "properties": {"city": {"type": "string"}, "cityCode": {"type": "string"}, "countryCode": {"type": "string"}, "expireTime": {"type": "string", "format": "date-time"}, "host": {"type": "string"}, "isp": {"type": "string"}, "outboundIp": {"type": "string"}, "password": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "provinceCode": {"type": "string"}, "proxyType": {"type": "string"}, "tunnel": {"type": "boolean"}, "username": {"type": "string"}}}, "RpcConnectedPeers": {"title": "RpcConnectedPeers", "type": "object", "properties": {"addresses": {"type": "array", "items": {"$ref": "#/components/schemas/TunnelAddress"}}, "tunnelAddress": {"$ref": "#/components/schemas/TunnelAddress"}}}, "SpyListBeansVo": {"title": "SpyListBeansVo", "type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "keys": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectInfo"}}}}, "SpyObjectVo": {"title": "SpyObjectVo", "type": "object", "properties": {"classes": {"type": "array", "items": {"$ref": "#/components/schemas/FieldsInfo"}}}}, "SpyVo": {"title": "SpyVo", "type": "object", "properties": {"beans": {"$ref": "#/components/schemas/SpyListBeansVo"}, "object": {"$ref": "#/components/schemas/SpyObjectVo"}}}, "StartProxyForwardResult": {"title": "StartProxyForwardResult", "type": "object", "properties": {"alreadyStarted": {"type": "boolean"}, "bindIp": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "proxyId": {"type": "integer", "format": "int64"}}}, "StartProxyForwardVo": {"title": "StartProxyForwardVo", "type": "object", "properties": {"auth": {"type": "boolean"}, "bindIp": {"type": "string"}, "enabled": {"type": "boolean"}, "forwardIp": {"type": "string"}, "password": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "proxyId": {"type": "integer", "format": "int64"}, "proxyType": {"type": "string"}, "username": {"type": "string"}, "whitelist": {"type": "array", "items": {"type": "string"}}, "whitelistEnabled": {"type": "boolean"}}}, "StopProxyForwardVo": {"title": "StopProxyForwardVo", "type": "object", "properties": {"ports": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}, "TransitConnectionInfo": {"title": "TransitConnectionInfo", "type": "object", "properties": {"domestic": {"type": "boolean"}, "endpoints": {"type": "string"}, "favorite": {"type": "boolean"}, "forceReconnect": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "ping": {"type": "integer", "format": "int32"}, "working": {"type": "boolean"}}}, "TransitInfo": {"title": "TransitInfo", "type": "object", "properties": {"domestic": {"type": "boolean"}, "endpoints": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "working": {"type": "boolean"}}}, "TunnelAddress": {"title": "TunnelAddress", "type": "object", "properties": {"identity": {"type": "string"}, "type": {"type": "string"}}}, "TunnelConnStat": {"title": "TunnelConnStat", "type": "object", "properties": {"clientDfSessionStat": {"$ref": "#/components/schemas/DynamicForwardStat"}, "peerTypeStatMap": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}}, "serverDfSessionStat": {"$ref": "#/components/schemas/DynamicForwardStat"}, "statMap": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/PeerConnStat"}}}}, "UpdateProxyForwardAuth": {"title": "UpdateProxyForwardAuth", "type": "object", "properties": {"auth": {"type": "boolean"}, "password": {"type": "string"}, "proxyId": {"type": "integer", "format": "int64"}, "username": {"type": "string"}, "whitelist": {"type": "array", "items": {"type": "string"}}, "whitelistEnabled": {"type": "boolean"}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CheckCodeResult»": {"title": "WebResult«CheckCodeResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CheckCodeResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ExecResult»": {"title": "WebResult«ExecResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ExecResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ForceDisconnectResult»": {"title": "WebResult«ForceDisconnectResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ForceDisconnectResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GitInfoVo»": {"title": "WebResult«GitInfoVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GitInfoVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«InstallScriptVo»": {"title": "WebResult«InstallScriptVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/InstallScriptVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«GatewayDto»»": {"title": "WebResult«List«GatewayDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/GatewayDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«GatewayTemplateDto»»": {"title": "WebResult«List«GatewayTemplateDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/GatewayTemplateDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TunnelAddress»»": {"title": "WebResult«List«TunnelAddress»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TunnelAddress"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«Map«string,string»»": {"title": "WebResult«Map«string,string»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object", "additionalProperties": {"type": "string"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«Map»": {"title": "WebResult«Map»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«GatewayDetailVo»»": {"title": "WebResult«PageResult«GatewayDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«GatewayDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ProbeResult»": {"title": "WebResult«ProbeResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ProbeResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ProduceIpResultVo»": {"title": "WebResult«ProduceIpResultVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ProduceIpResultVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ProduceIpSpecVo»": {"title": "WebResult«ProduceIpSpecVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ProduceIpSpecVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ProxyMeta»": {"title": "WebResult«ProxyMeta»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ProxyMeta"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ProxyProbeWithRemoteIp»": {"title": "WebResult«ProxyProbeWithRemoteIp»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ProxyProbeWithRemoteIp"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ProxyTemplateVo»": {"title": "WebResult«ProxyTemplateVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ProxyTemplateVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ProxyTokenCodeVo»": {"title": "WebResult«ProxyTokenCodeVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ProxyTokenCodeVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ProxyTokenVo»": {"title": "WebResult«ProxyTokenVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ProxyTokenVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ProxyVersionInfo»": {"title": "WebResult«ProxyVersionInfo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ProxyVersionInfo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ReportIpResult»": {"title": "WebResult«ReportIpResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ReportIpResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RpcConnectedPeers»": {"title": "WebResult«RpcConnectedPeers»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RpcConnectedPeers"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«SpyVo»": {"title": "WebResult«SpyVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/SpyVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«StartProxyForwardResult»": {"title": "WebResult«StartProxyForwardResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/StartProxyForwardResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TransitConnectionInfo»": {"title": "WebResult«TransitConnectionInfo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TransitConnectionInfo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TunnelAddress»": {"title": "WebResult«TunnelAddress»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TunnelAddress"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TunnelConnStat»": {"title": "WebResult«TunnelConnStat»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TunnelConnStat"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«long»": {"title": "WebResult«long»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "integer", "format": "int64"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«string»": {"title": "WebResult«string»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "string"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-device-token": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-device-token", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}