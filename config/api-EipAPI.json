{"openapi": "3.0.3", "info": {"title": "Eip API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "EIP主机管理", "description": "<PERSON>ip Instance Controller"}, {"name": "客户密钥对API", "description": "Key Pair Controller"}, {"name": "客户自有云平台账户管理", "description": "Customer Cloud Controller"}], "paths": {"/api/customer/cloud": {"post": {"tags": ["CustomerCloudController"], "summary": "添加云账号", "operationId": "customerCloudPost", "parameters": [{"name": "provider", "in": "query", "description": "provider", "required": true, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}}, {"name": "accessKeyId", "in": "query", "description": "accessKeyId", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "accessKeySecret", "in": "query", "description": "accessKeySecret", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "name", "in": "query", "description": "name", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "description", "in": "query", "description": "description", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«OperatingCloudDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/customer/cloud/check": {"get": {"tags": ["CustomerCloudController"], "summary": "测试云账号AK", "operationId": "customerCloudCheckGet", "parameters": [{"name": "provider", "in": "query", "description": "provider", "required": true, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}}, {"name": "accessKeyId", "in": "query", "description": "accessKeyId", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "accessKeySecret", "in": "query", "description": "accessKeySecret", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "cloudName", "in": "query", "description": "cloudName", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«AccountInfo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/customer/cloud/page": {"get": {"tags": ["CustomerCloudController"], "summary": "查询云账号", "operationId": "customerCloudPageGet", "parameters": [{"name": "provider", "in": "query", "description": "provider", "required": false, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«CustomerCloudVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/customer/cloud/{cloudId}": {"get": {"tags": ["CustomerCloudController"], "summary": "查询某个云账号", "operationId": "customerCloudByCloudIdGet", "parameters": [{"name": "cloudId", "in": "path", "description": "cloudId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CustomerCloudVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["CustomerCloudController"], "summary": "修改云账号", "operationId": "customerCloudByCloudIdPut", "parameters": [{"name": "cloudId", "in": "path", "description": "cloudId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "accessKeyId", "in": "query", "description": "accessKeyId", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "accessKeySecret", "in": "query", "description": "accessKeySecret", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "name", "in": "query", "description": "name", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "description", "in": "query", "description": "description", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«OperatingCloudDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "delete": {"tags": ["CustomerCloudController"], "summary": "删除云账号", "operationId": "customerCloudByCloudIdDelete", "parameters": [{"name": "cloudId", "in": "path", "description": "cloudId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/customer/cloud/{cloudId}/keypair": {"put": {"tags": ["CustomerCloudController"], "summary": "修改云账号的密钥对", "operationId": "customerCloudByCloudIdKeypairPut", "parameters": [{"name": "cloudId", "in": "path", "description": "cloudId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "keyId", "in": "query", "description": "keyId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/eip/instance/{id}/accessInfo": {"get": {"tags": ["EipInstanceController"], "summary": "获取实例登录账户密码（或ssh key）", "operationId": "eipInstanceByIdAccessInfoGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«AccountCredentialVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/eip/instance/{id}/decryptWindowsPassword": {"get": {"tags": ["EipInstanceController"], "summary": "Aws Windows解密Windows密码", "operationId": "eipInstanceByIdDecryptWindowsPasswordGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "privateKeyPem", "in": "query", "description": "privateKeyPem", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/customer/keypair": {"post": {"tags": ["KeyPairController"], "summary": "创建密钥对", "description": "会返回未加密的公私钥字符串", "operationId": "customerKeypairPost", "parameters": [{"name": "cloudId", "in": "query", "description": "关联云账号", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«KeyPairDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/customer/keypair/byPubkey": {"post": {"tags": ["KeyPairController"], "summary": "上传公钥", "operationId": "customerKeypairByPubkeyPost", "parameters": [{"name": "pubkey", "in": "query", "description": "通常是以'ssh-rsa AAAA'开头的一段ssh授权登录公钥", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "cloudId", "in": "query", "description": "关联云账号", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«KeyPairDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/customer/keypair/{keyId}": {"get": {"tags": ["KeyPairController"], "summary": "查询密钥对", "operationId": "customerKeypairByKeyIdGet", "parameters": [{"name": "keyId", "in": "path", "description": "keyId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«KeyPairDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/customer/keypair/{keyId}/check": {"get": {"tags": ["KeyPairController"], "summary": "验证私钥是否匹配", "operationId": "customerKeypairByKeyIdCheckGet", "parameters": [{"name": "keyId", "in": "path", "description": "keyId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "privateKeyPem", "in": "query", "description": "privateKeyPem", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«boolean»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"AccountCredentialVo": {"title": "AccountCredentialVo", "type": "object", "properties": {"account": {"type": "string"}, "keyPair": {"$ref": "#/components/schemas/KeyPairDto"}, "lastUpdatedTime": {"type": "string", "format": "date-time"}, "password": {"type": "string"}}}, "AccountInfo": {"title": "AccountInfo", "type": "object", "properties": {"account": {"type": "string"}, "arn": {"type": "string"}, "userId": {"type": "string"}}}, "CustomerCloudVo": {"title": "CustomerCloudVo", "type": "object", "properties": {"accessKeyId": {"type": "string"}, "accessKeySecret": {"type": "string"}, "account": {"type": "string"}, "description": {"type": "string"}, "extra": {"type": "string"}, "hostCount": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "ipCount": {"type": "integer", "format": "int32"}, "keyId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "ownerArn": {"type": "string"}, "provider": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}, "teamId": {"type": "integer", "format": "int64"}, "valid": {"type": "boolean"}}}, "KeyPairDto": {"title": "KeyPairDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "fingerprint": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "importType": {"type": "string", "enum": ["Platform", "User"]}, "name": {"type": "string"}, "privateKey": {"type": "string"}, "publicKey": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}}}, "OperatingCloudDto": {"title": "OperatingCloudDto", "type": "object", "properties": {"accessKeyId": {"type": "string"}, "accessKeySecret": {"type": "string"}, "account": {"type": "string"}, "description": {"type": "string"}, "extra": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "keyId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "ownerArn": {"type": "string"}, "provider": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}, "teamId": {"type": "integer", "format": "int64"}, "valid": {"type": "boolean"}}}, "PageResult«CustomerCloudVo»": {"title": "PageResult«CustomerCloudVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerCloudVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«AccountCredentialVo»": {"title": "WebResult«AccountCredentialVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/AccountCredentialVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«AccountInfo»": {"title": "WebResult«AccountInfo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/AccountInfo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CustomerCloudVo»": {"title": "WebResult«CustomerCloudVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CustomerCloudVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«KeyPairDto»": {"title": "WebResult«KeyPairDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/KeyPairDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«OperatingCloudDto»": {"title": "WebResult«OperatingCloudDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/OperatingCloudDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«CustomerCloudVo»»": {"title": "WebResult«PageResult«CustomerCloudVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«CustomerCloudVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«boolean»": {"title": "WebResult«boolean»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "boolean"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«string»": {"title": "WebResult«string»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "string"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-device-token": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-device-token", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}