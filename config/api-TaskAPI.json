{"openapi": "3.0.3", "info": {"title": "Task API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "任务中心API", "description": "Task Front Controller"}, {"name": "任务进度API", "description": "Task Backend Controller"}, {"name": "排队任务API", "description": "Queued Task Controller"}], "paths": {"/api/queueTask/rank": {"get": {"tags": ["QueuedTaskController"], "summary": "查询任务的排队顺序", "description": "如果为null，则表示任务已经开始了", "operationId": "queueTaskRankGet", "parameters": [{"name": "queueType", "in": "query", "description": "queueType", "required": false, "style": "form", "schema": {"type": "string", "enum": ["CloudRpaTask"]}}, {"name": "targetId", "in": "query", "description": "目标任务ID，比如rpa_task.id", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RankVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/task/{taskId}/finished": {"put": {"tags": ["TaskBackendController"], "summary": "任务结束", "operationId": "taskByTaskIdFinishedPut", "parameters": [{"name": "taskId", "in": "path", "description": "taskId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "progress", "in": "query", "description": "progress", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "status", "in": "query", "description": "status", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Abort", "Fail", "Pending", "Running", "Success", "Timeout"]}}, {"name": "remarks", "in": "query", "description": "remarks", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/task/{taskId}/progress": {"put": {"tags": ["TaskBackendController"], "summary": "更新任务进度", "operationId": "taskByTaskIdProgressPut", "parameters": [{"name": "taskId", "in": "path", "description": "taskId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "progress", "in": "query", "description": "progress", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/task/finished": {"delete": {"tags": ["TaskFrontController"], "summary": "删除已经结束的任务", "operationId": "taskFinishedDelete", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/task/page": {"get": {"tags": ["TaskFrontController"], "summary": "查询任务列表", "operationId": "taskPageGet", "parameters": [{"name": "taskType", "in": "query", "description": "taskType", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "createTimeFrom", "in": "query", "description": "createTimeFrom", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "createTimeTo", "in": "query", "description": "createTimeTo", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "finishTimeFrom", "in": "query", "description": "finishTimeFrom", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "finishTimeTo", "in": "query", "description": "finishTimeTo", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TaskDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/task/task": {"post": {"tags": ["TaskFrontController"], "summary": "为创建任务", "operationId": "taskTaskPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTaskRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«long»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/task/{taskId}": {"get": {"tags": ["TaskFrontController"], "summary": "查看任务的详情", "operationId": "taskByTaskIdGet", "parameters": [{"name": "taskId", "in": "path", "description": "taskId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TaskDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "delete": {"tags": ["TaskFrontController"], "summary": "删除特定任务", "operationId": "taskByTaskIdDelete", "parameters": [{"name": "taskId", "in": "path", "description": "taskId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/task/{taskId}/abort": {"put": {"tags": ["TaskFrontController"], "summary": "终止特定任务", "operationId": "taskByTaskIdAbortPut", "parameters": [{"name": "taskId", "in": "path", "description": "taskId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/task/{taskId}/result": {"get": {"tags": ["TaskFrontController"], "summary": "查看任务的执行结果", "operationId": "taskByTaskIdResultGet", "parameters": [{"name": "taskId", "in": "path", "description": "taskId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TaskResult»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"CreateTaskRequest": {"title": "CreateTaskRequest", "type": "object", "properties": {"detailVo": {"type": "object", "description": "任务详情，用于展示"}, "name": {"type": "string", "description": "任务名称"}, "status": {"type": "string", "enum": ["Abort", "Fail", "Pending", "Running", "Success", "Timeout"]}, "targetId": {"type": "integer", "description": "任务关联的目标ID", "format": "int64"}, "taskType": {"type": "string", "description": "任务类型", "enum": ["BatchUpdateBandwidth", "CleanColdTable", "CleanMongo", "CleanTable", "CopyTkCreatorToClient", "CrsTransferOrder", "DbTransfer", "DeleteIps", "FireOpsMessage", "ImportAccounts", "ImportIp", "ImportIppIps", "ImportIps", "ImportShop", "OpenaiChatGenerate", "OpenaiChatTranslate", "ProbeBatchLaunchInstance", "ProbeIps", "RebootIp", "RefreshClash", "RefreshExtensions", "RepairGhLiveTime", "RepairKolLiveRate", "RepairKolLiveTime", "RepairOps", "RepairTkCreatorFollower", "ResetJdEip", "ReviseIpHostLocation", "ShardTableOps", "ShardTeamTableSql", "SshChangePort", "SshCommands", "SshCommandsBatchLaunchInstance", "SyncKolCreator", "SyncKolRegionMap", "TkSendEmail", "TransferShardTable", "TransferTable", "TransferTagResource", "TransferTkCreator", "UpgradeGhMessage", "UploadAiKnowledge", "UploadDiskFile", "UserRefreshIp"]}, "timeout": {"type": "integer", "description": "任务超时,0表示永不超时。单位：秒", "format": "int32"}}}, "PageResult«TaskDto»": {"title": "PageResult«TaskDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TaskDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "RankVo": {"title": "RankVo", "type": "object", "properties": {"rank": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "waitSeconds": {"type": "integer", "format": "int32"}}}, "TaskDetailVo": {"title": "TaskDetailVo", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "detail": {"type": "string"}, "finishTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "progress": {"type": "integer", "format": "int32"}, "remarks": {"type": "string"}, "result": {"$ref": "#/components/schemas/TaskResult"}, "status": {"type": "string", "enum": ["Abort", "Fail", "Pending", "Running", "Success", "Timeout"]}, "targetId": {"type": "integer", "format": "int64"}, "taskType": {"type": "string", "enum": ["BatchUpdateBandwidth", "CleanColdTable", "CleanMongo", "CleanTable", "CopyTkCreatorToClient", "CrsTransferOrder", "DbTransfer", "DeleteIps", "FireOpsMessage", "ImportAccounts", "ImportIp", "ImportIppIps", "ImportIps", "ImportShop", "OpenaiChatGenerate", "OpenaiChatTranslate", "ProbeBatchLaunchInstance", "ProbeIps", "RebootIp", "RefreshClash", "RefreshExtensions", "RepairGhLiveTime", "RepairKolLiveRate", "RepairKolLiveTime", "RepairOps", "RepairTkCreatorFollower", "ResetJdEip", "ReviseIpHostLocation", "ShardTableOps", "ShardTeamTableSql", "SshChangePort", "SshCommands", "SshCommandsBatchLaunchInstance", "SyncKolCreator", "SyncKolRegionMap", "TkSendEmail", "TransferShardTable", "TransferTable", "TransferTagResource", "TransferTkCreator", "UpgradeGhMessage", "UploadAiKnowledge", "UploadDiskFile", "UserRefreshIp"]}, "teamId": {"type": "integer", "format": "int64"}}}, "TaskDto": {"title": "TaskDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "detail": {"type": "string"}, "finishTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "progress": {"type": "integer", "format": "int32"}, "remarks": {"type": "string"}, "status": {"type": "string", "enum": ["Abort", "Fail", "Pending", "Running", "Success", "Timeout"]}, "targetId": {"type": "integer", "format": "int64"}, "taskType": {"type": "string", "enum": ["BatchUpdateBandwidth", "CleanColdTable", "CleanMongo", "CleanTable", "CopyTkCreatorToClient", "CrsTransferOrder", "DbTransfer", "DeleteIps", "FireOpsMessage", "ImportAccounts", "ImportIp", "ImportIppIps", "ImportIps", "ImportShop", "OpenaiChatGenerate", "OpenaiChatTranslate", "ProbeBatchLaunchInstance", "ProbeIps", "RebootIp", "RefreshClash", "RefreshExtensions", "RepairGhLiveTime", "RepairKolLiveRate", "RepairKolLiveTime", "RepairOps", "RepairTkCreatorFollower", "ResetJdEip", "ReviseIpHostLocation", "ShardTableOps", "ShardTeamTableSql", "SshChangePort", "SshCommands", "SshCommandsBatchLaunchInstance", "SyncKolCreator", "SyncKolRegionMap", "TkSendEmail", "TransferShardTable", "TransferTable", "TransferTagResource", "TransferTkCreator", "UpgradeGhMessage", "UploadAiKnowledge", "UploadDiskFile", "UserRefreshIp"]}, "teamId": {"type": "integer", "format": "int64"}}}, "TaskResult": {"title": "TaskResult", "type": "object", "properties": {"id": {"type": "string"}, "result": {"type": "object"}, "taskId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TaskDto»»": {"title": "WebResult«PageResult«TaskDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TaskDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RankVo»": {"title": "WebResult«RankVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RankVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TaskDetailVo»": {"title": "WebResult«TaskDetailVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TaskDetailVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TaskResult»": {"title": "WebResult«TaskResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TaskResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«int»": {"title": "WebResult«int»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«long»": {"title": "WebResult«long»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "integer", "format": "int64"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-device-token": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-device-token", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}