{"openapi": "3.0.3", "info": {"title": "Goods API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "Goods Webhook API", "description": "Goods Webhook Controller"}, {"name": "商品API", "description": "Goods Controller"}], "paths": {"/api/goods/credit/config": {"get": {"tags": ["GoodsController"], "summary": "获取花瓣配置", "operationId": "goodsCreditConfigGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CreditConfig»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/goods/ip/cities": {"get": {"tags": ["GoodsController"], "summary": "获取IP售卖城市列表", "operationId": "goodsIpCitiesGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RegionVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/goods/ip/countrywideConfig": {"get": {"tags": ["GoodsController"], "summary": "获取Ip商品按国家划分的配置", "operationId": "goodsIpCountrywideConfigGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«IpGoodsCountrywideConfig»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["GoodsController"], "summary": "获取Ip商品按国家划分的配置", "operationId": "goodsIpCountrywideConfigPut", "parameters": [{"name": "countryCodes", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/goods/ip/regions": {"get": {"tags": ["GoodsController"], "summary": "获取IP方案及价格", "operationId": "goodsIpRegionsGet", "parameters": [{"name": "goodsType", "in": "query", "description": "可以查询多种商品类型，用逗号连接", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "networkType", "in": "query", "description": "可以查询多种网络类型，用逗号连接", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "dynamic", "in": "query", "description": "是否动态", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«IpRegionVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/goods/ip/regionsByFilter": {"post": {"tags": ["GoodsController"], "summary": "过滤IP商品列表", "operationId": "goodsIpRegionsByFilterPost", "parameters": [{"name": "category", "in": "query", "description": "category", "required": false, "style": "form", "schema": {"type": "string", "enum": ["daily", "manyAccount", "rdp", "sensitive"]}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/IpGoodsFilterVo"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«IpRegionVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/goods/provider/info": {"put": {"tags": ["GoodsController"], "summary": "修改IP供应商信息", "operationId": "goodsProviderInfoPut", "parameters": [{"name": "provider", "in": "query", "description": "provider", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "description", "in": "query", "description": "description", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "homeUrl", "in": "query", "description": "homeUrl", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "promoUrl", "in": "query", "description": "promoUrl", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "rangeType", "in": "query", "description": "rangeType", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Domestic", "Global", "Oversea"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/goods/providers": {"get": {"tags": ["GoodsController"], "summary": "获取商品供应商列表", "operationId": "goodsProvidersGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«GoodsProviderDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/goods/{goodsId}/checkStock": {"get": {"tags": ["GoodsController"], "summary": "checkGoodsStock", "operationId": "goodsByGoodsIdCheckStockGet", "parameters": [{"name": "goodsId", "in": "path", "description": "goodsId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/webhook/ip/regionsByFilter": {"post": {"tags": ["GoodsWebhookController"], "summary": "过滤IP商品列表", "operationId": "webhookIpRegionsByFilterPost", "parameters": [{"name": "category", "in": "query", "description": "category", "required": false, "style": "form", "schema": {"type": "string", "enum": ["daily", "manyAccount", "rdp", "sensitive"]}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/IpGoodsFilterVo"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«IpRegionVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/webhook/op/goodsList": {"get": {"tags": ["GoodsWebhookController"], "summary": "获取商品列表", "operationId": "webhookOpGoodsListGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GoodsPriceVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/webhook/op/goodsList/v61": {"get": {"tags": ["GoodsWebhookController"], "summary": "获取商品列表", "operationId": "webhookOpGoodsListV61Get", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GoodsPriceVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"CreditConfig": {"title": "CreditConfig", "type": "object", "properties": {"initPresentAmount": {"type": "integer", "format": "int64"}, "inviteUserAmount": {"type": "integer", "format": "int32"}, "phoneRewardedAmount": {"type": "integer", "format": "int32"}, "promotedPresentAmount": {"type": "integer", "format": "int64"}, "sessionAbortRemainCredit": {"type": "integer", "format": "int64"}, "sessionAlertRemainCredit": {"type": "integer", "format": "int64"}, "speedLimit1": {"type": "integer", "format": "int32"}, "speedLimit2": {"type": "integer", "format": "int32"}, "wechatRewardedAmount": {"type": "integer", "format": "int32"}}}, "DiscountsVo": {"title": "DiscountsVo", "type": "object", "properties": {"amount": {"type": "integer", "description": "赠送数量或折扣百分比或阶梯折扣百分比", "format": "int32"}, "discountCode": {"type": "string", "description": "打折code"}, "discountType": {"type": "string", "description": "打折还是赠送", "enum": ["Discount", "LadderPrice", "Present"]}, "periodUnit": {"type": "string", "description": "周期或数量单位", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "remarks": {"type": "string", "description": "备注"}, "threshold": {"type": "integer", "description": "期数或数量", "format": "int32"}}}, "GoodsDto": {"title": "GoodsDto", "type": "object", "properties": {"arch": {"type": "string"}, "bandwidth": {"type": "integer", "format": "int32"}, "buyoutPrice": {"type": "number", "format": "bigdecimal"}, "city": {"type": "string"}, "cost": {"type": "number", "format": "bigdecimal"}, "countryCode": {"type": "string"}, "cpu": {"type": "integer", "format": "int32"}, "currency": {"type": "string", "enum": ["CREDIT", "RMB", "USD"]}, "dayCost": {"type": "number", "format": "bigdecimal"}, "dayPrice": {"type": "number", "format": "bigdecimal"}, "dayTraffic": {"type": "number", "format": "double"}, "description": {"type": "string"}, "disk": {"type": "integer", "format": "int32"}, "diskCategory": {"type": "string"}, "dynamic": {"type": "boolean"}, "goodsType": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}, "id": {"type": "integer", "format": "int64"}, "initialQuantity": {"type": "integer", "format": "int32"}, "instanceType": {"type": "string"}, "ipv6": {"type": "boolean"}, "listPrice": {"type": "number", "format": "bigdecimal"}, "mem": {"type": "number", "format": "double"}, "name": {"type": "string"}, "networkType": {"type": "string", "enum": ["cloudIdc", "mobile", "proxyIdc", "residential", "unknown", "unknownIdc"]}, "onSale": {"type": "string", "enum": ["Disabled", "Offline", "Online"]}, "perfLevel": {"type": "string", "enum": ["CostEffective", "HighlyConcurrent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "None", "RemoteLogin", "Unlim<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "pipeType": {"type": "string", "enum": ["None", "Proxy", "Tunnel", "TunnelFailToProxy"]}, "platform": {"type": "string", "enum": ["android", "linux", "macos", "unknown", "web", "windows", "windows7"]}, "price": {"type": "number", "format": "bigdecimal"}, "provider": {"type": "string"}, "region": {"type": "string"}, "remoteLogin": {"type": "boolean"}, "resourceId": {"type": "integer", "format": "int64"}, "sortNo": {"type": "integer", "format": "int32"}, "tcpfp": {"type": "boolean"}, "traffic": {"type": "number", "format": "double"}, "trafficCurrency": {"type": "string", "enum": ["CREDIT", "RMB", "USD"]}, "trafficPrice": {"type": "number", "format": "bigdecimal"}, "updateTime": {"type": "string", "format": "date-time"}, "weekCost": {"type": "number", "format": "bigdecimal"}, "weekPrice": {"type": "number", "format": "bigdecimal"}, "weekTraffic": {"type": "number", "format": "double"}}}, "GoodsPriceVo": {"title": "GoodsPriceVo", "type": "object", "properties": {"creditConfig": {"$ref": "#/components/schemas/CreditConfig"}, "ipGoodsPriceRegions": {"type": "array", "items": {"$ref": "#/components/schemas/IpGoodsPriceRegionVo"}}, "rpaExecute": {"$ref": "#/components/schemas/GoodsDto"}, "rpaMobile": {"$ref": "#/components/schemas/GoodsWithDiscountDto"}, "rpaVouchers": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsDto"}}, "shop": {"$ref": "#/components/schemas/GoodsWithDiscountDto"}, "storage": {"$ref": "#/components/schemas/GoodsWithDiscountDto"}, "teamMember": {"$ref": "#/components/schemas/GoodsWithDiscountDto"}, "teamMobile": {"$ref": "#/components/schemas/GoodsWithDiscountDto"}, "transitTraffic": {"$ref": "#/components/schemas/GoodsDto"}}}, "GoodsProviderDto": {"title": "GoodsProviderDto", "type": "object", "properties": {"description": {"type": "string"}, "homeUrl": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "pipeType": {"type": "string", "enum": ["None", "Proxy", "Tunnel", "TunnelFailToProxy"]}, "promoUrl": {"type": "string"}, "provider": {"type": "string"}, "rangeType": {"type": "string", "enum": ["Domestic", "Global", "Oversea"]}, "sticky": {"type": "boolean"}}}, "GoodsUsabilityVo": {"title": "GoodsUsabilityVo", "type": "object", "properties": {"arch": {"type": "string"}, "bandwidth": {"type": "integer", "format": "int32"}, "buyoutPrice": {"type": "number", "format": "bigdecimal"}, "city": {"type": "string"}, "cost": {"type": "number", "format": "bigdecimal"}, "countryCode": {"type": "string"}, "cpu": {"type": "integer", "format": "int32"}, "currency": {"type": "string", "enum": ["CREDIT", "RMB", "USD"]}, "dayCost": {"type": "number", "format": "bigdecimal"}, "dayPrice": {"type": "number", "format": "bigdecimal"}, "dayTraffic": {"type": "number", "format": "double"}, "description": {"type": "string"}, "disk": {"type": "integer", "format": "int32"}, "diskCategory": {"type": "string"}, "dynamic": {"type": "boolean"}, "goodsType": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}, "id": {"type": "integer", "format": "int64"}, "initialQuantity": {"type": "integer", "format": "int32"}, "instanceType": {"type": "string"}, "ipv6": {"type": "boolean"}, "listPrice": {"type": "number", "format": "bigdecimal"}, "mem": {"type": "number", "format": "double"}, "name": {"type": "string"}, "networkType": {"type": "string", "enum": ["cloudIdc", "mobile", "proxyIdc", "residential", "unknown", "unknownIdc"]}, "onSale": {"type": "string", "enum": ["Disabled", "Offline", "Online"]}, "perfLevel": {"type": "string", "enum": ["CostEffective", "HighlyConcurrent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "None", "RemoteLogin", "Unlim<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "pipeType": {"type": "string", "enum": ["None", "Proxy", "Tunnel", "TunnelFailToProxy"]}, "platform": {"type": "string", "enum": ["android", "linux", "macos", "unknown", "web", "windows", "windows7"]}, "platformUsabilityList": {"type": "array", "items": {"$ref": "#/components/schemas/PlatformUsabilityVo"}}, "price": {"type": "number", "format": "bigdecimal"}, "provider": {"type": "string"}, "region": {"type": "string"}, "remoteLogin": {"type": "boolean"}, "resourceId": {"type": "integer", "format": "int64"}, "sortNo": {"type": "integer", "format": "int32"}, "tcpfp": {"type": "boolean"}, "traffic": {"type": "number", "format": "double"}, "trafficCurrency": {"type": "string", "enum": ["CREDIT", "RMB", "USD"]}, "trafficPrice": {"type": "number", "format": "bigdecimal"}, "updateTime": {"type": "string", "format": "date-time"}, "weekCost": {"type": "number", "format": "bigdecimal"}, "weekPrice": {"type": "number", "format": "bigdecimal"}, "weekTraffic": {"type": "number", "format": "double"}}}, "GoodsWithDiscountDto": {"title": "GoodsWithDiscountDto", "type": "object", "properties": {"arch": {"type": "string"}, "bandwidth": {"type": "integer", "format": "int32"}, "buyoutPrice": {"type": "number", "format": "bigdecimal"}, "city": {"type": "string"}, "cost": {"type": "number", "format": "bigdecimal"}, "countryCode": {"type": "string"}, "cpu": {"type": "integer", "format": "int32"}, "currency": {"type": "string", "enum": ["CREDIT", "RMB", "USD"]}, "dayCost": {"type": "number", "format": "bigdecimal"}, "dayPrice": {"type": "number", "format": "bigdecimal"}, "dayTraffic": {"type": "number", "format": "double"}, "description": {"type": "string"}, "discounts": {"type": "array", "items": {"$ref": "#/components/schemas/DiscountsVo"}}, "disk": {"type": "integer", "format": "int32"}, "diskCategory": {"type": "string"}, "dynamic": {"type": "boolean"}, "goodsType": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}, "id": {"type": "integer", "format": "int64"}, "initialQuantity": {"type": "integer", "format": "int32"}, "instanceType": {"type": "string"}, "ipv6": {"type": "boolean"}, "listPrice": {"type": "number", "format": "bigdecimal"}, "mem": {"type": "number", "format": "double"}, "name": {"type": "string"}, "networkType": {"type": "string", "enum": ["cloudIdc", "mobile", "proxyIdc", "residential", "unknown", "unknownIdc"]}, "onSale": {"type": "string", "enum": ["Disabled", "Offline", "Online"]}, "perfLevel": {"type": "string", "enum": ["CostEffective", "HighlyConcurrent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "None", "RemoteLogin", "Unlim<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "pipeType": {"type": "string", "enum": ["None", "Proxy", "Tunnel", "TunnelFailToProxy"]}, "platform": {"type": "string", "enum": ["android", "linux", "macos", "unknown", "web", "windows", "windows7"]}, "price": {"type": "number", "format": "bigdecimal"}, "provider": {"type": "string"}, "region": {"type": "string"}, "remoteLogin": {"type": "boolean"}, "resourceId": {"type": "integer", "format": "int64"}, "sortNo": {"type": "integer", "format": "int32"}, "tcpfp": {"type": "boolean"}, "traffic": {"type": "number", "format": "double"}, "trafficCurrency": {"type": "string", "enum": ["CREDIT", "RMB", "USD"]}, "trafficPrice": {"type": "number", "format": "bigdecimal"}, "updateTime": {"type": "string", "format": "date-time"}, "weekCost": {"type": "number", "format": "bigdecimal"}, "weekPrice": {"type": "number", "format": "bigdecimal"}, "weekTraffic": {"type": "number", "format": "double"}}}, "IpGoodsCountrywideConfig": {"title": "IpGoodsCountrywideConfig", "type": "object", "properties": {"countryCodes": {"type": "array", "items": {"type": "string"}}}}, "IpGoodsFilterVo": {"title": "IpGoodsFilterVo", "type": "object", "properties": {"city": {"type": "string"}, "countryCode": {"type": "string"}, "dynamic": {"type": "boolean"}, "excludedCity": {"type": "string"}, "goodsTypes": {"type": "array", "items": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}}, "ipv6": {"type": "boolean"}, "networkTypes": {"type": "array", "items": {"type": "string", "enum": ["cloudIdc", "mobile", "proxyIdc", "residential", "unknown", "unknownIdc"]}}, "perfLevels": {"type": "array", "items": {"type": "string", "enum": ["CostEffective", "HighlyConcurrent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "None", "RemoteLogin", "Unlim<PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, "platforms": {"type": "array", "items": {"type": "string", "enum": ["android", "linux", "macos", "unknown", "web", "windows", "windows7"]}}, "priceRanges": {"type": "array", "items": {"$ref": "#/components/schemas/PriceRangeVo"}}, "provider": {"type": "string"}, "region": {"type": "string"}}}, "IpGoodsPriceItem": {"title": "IpGoodsPriceItem", "type": "object", "properties": {"monthPrice": {"type": "number", "format": "bigdecimal"}, "networkType": {"type": "string", "enum": ["cloudIdc", "mobile", "proxyIdc", "residential", "unknown", "unknownIdc"]}, "weekPrice": {"type": "number", "format": "bigdecimal"}}}, "IpGoodsPriceRegionVo": {"title": "IpGoodsPriceRegionVo", "type": "object", "properties": {"area": {"type": "string", "description": "IP区域", "enum": ["中东", "中国", "亚太", "北美", "南极洲", "南美", "欧洲", "非洲"]}, "areaEn": {"type": "string"}, "city": {"type": "string", "description": "城市"}, "cityEn": {"type": "string"}, "country": {"type": "string", "description": "国家"}, "countryCode": {"type": "string", "description": "国家代码"}, "countryEn": {"type": "string", "description": "国家英文"}, "goodsList": {"type": "array", "items": {"$ref": "#/components/schemas/IpGoodsPriceItem"}}, "orderNo": {"type": "integer", "description": "排序字段", "format": "int32"}}}, "IpRegionVo": {"title": "IpRegionVo", "type": "object", "properties": {"area": {"type": "string", "description": "IP区域", "enum": ["中东", "中国", "亚太", "北美", "南极洲", "南美", "欧洲", "非洲"]}, "areaEn": {"type": "string"}, "city": {"type": "string", "description": "城市"}, "cityEn": {"type": "string"}, "country": {"type": "string", "description": "国家"}, "countryCode": {"type": "string", "description": "国家代码"}, "countryEn": {"type": "string", "description": "国家英文"}, "goods": {"type": "array", "description": "当前区域商品列表", "items": {"$ref": "#/components/schemas/GoodsUsabilityVo"}}, "importType": {"type": "string", "description": "种类", "enum": ["Platform", "User"]}, "orderNo": {"type": "integer", "description": "排序字段", "format": "int32"}, "provider": {"type": "string", "description": "供应商", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}, "region": {"type": "string", "description": "区域ID"}}}, "PlatformUsabilityVo": {"title": "PlatformUsabilityVo", "type": "object", "properties": {"area": {"type": "string", "enum": ["Argentina", "Australia", "Austria", "Belarus", "Belgium", "Bolivia", "Brazil", "Canada", "Chile", "China", "Colombia", "Costa_Rica", "Dominican", "Ecuador", "Egypt", "France", "Germany", "Global", "Guatemala", "Honduras", "HongKong", "India", "Indonesia", "Ireland", "Israel", "Italy", "Japan", "Kazakhstan", "Korea", "Malaysia", "Mexico", "Netherlands", "Nicaragua", "Panama", "Paraguay", "Peru", "Philippines", "Poland", "Portuguese", "Puerto_Rico", "Russia", "Salvador", "Saudi_Arabia", "Singapore", "Spain", "Sweden", "Switzerland", "Taiwan", "Thailand", "Turkey", "United_Arab_Emirates", "United_Kingdom", "United_States", "Uruguay", "Venezuela", "Vietnam"]}, "platformType": {"type": "string"}, "usable": {"type": "boolean"}}}, "PriceRangeVo": {"title": "PriceRangeVo", "type": "object", "properties": {"from": {"type": "number", "format": "double"}, "period": {"type": "string", "enum": ["Month", "Week"]}, "to": {"type": "number", "format": "double"}}}, "RegionVo": {"title": "RegionVo", "type": "object", "properties": {"area": {"type": "string", "description": "IP区域", "enum": ["中东", "中国", "亚太", "北美", "南极洲", "南美", "欧洲", "非洲"]}, "areaEn": {"type": "string"}, "city": {"type": "string", "description": "城市"}, "cityEn": {"type": "string"}, "country": {"type": "string", "description": "国家"}, "countryCode": {"type": "string", "description": "国家代码"}, "countryEn": {"type": "string", "description": "国家英文"}, "orderNo": {"type": "integer", "description": "排序字段", "format": "int32"}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CreditConfig»": {"title": "WebResult«CreditConfig»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CreditConfig"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GoodsPriceVo»": {"title": "WebResult«GoodsPriceVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GoodsPriceVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«IpGoodsCountrywideConfig»": {"title": "WebResult«IpGoodsCountrywideConfig»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/IpGoodsCountrywideConfig"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«GoodsProviderDto»»": {"title": "WebResult«List«GoodsProviderDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsProviderDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«IpRegionVo»»": {"title": "WebResult«List«IpRegionVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/IpRegionVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«RegionVo»»": {"title": "WebResult«List«RegionVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RegionVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-device-token": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-device-token", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}