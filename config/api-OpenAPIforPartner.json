{"openapi": "3.0.3", "info": {"title": "Partner OpenAPI", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "Partner Api", "description": "伙伴基本API"}, {"name": "Partner Team Api", "description": "伙伴签约团队管理 API"}, {"name": "Partner User Api", "description": "伙伴签约用户管理 API"}], "paths": {"/api/openapi/partner/current": {"get": {"tags": ["伙伴基本API"], "summary": "获取当前伙伴", "operationId": "openapiPartnerCurrentGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PartnerDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/openapi/partner/isv-token": {"post": {"tags": ["伙伴基本API"], "summary": "用当前AK创建签名的token，用于访问持有当前AK的第三方系统", "description": "请在request payload中传入需要签名的信息。系统默认会添加partnerId。teamId、userId在调用者传入时，也会被签名。请注意request payload不能超过500字符", "operationId": "openapiPartnerIsvTokenPost", "parameters": [{"name": "userId", "in": "query", "description": "签约用户ID", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "teamId", "in": "query", "description": "签约团队ID", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "expireSeconds", "in": "query", "description": "超时时间，单位秒，取值范围：[120秒,7天]", "required": true, "style": "form", "schema": {"maximum": 604800, "exclusiveMaximum": false, "minimum": 120, "exclusiveMinimum": false, "type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«AkAccessToken»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/openapi/partner/user-login-token": {"post": {"tags": ["伙伴基本API"], "summary": "给签约用户创建登录Token，用于访问花漾的系统", "operationId": "openapiPartnerUserLoginTokenPost", "parameters": [{"name": "userId", "in": "query", "description": "签约用户ID", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "expireSeconds", "in": "query", "description": "超时时间，单位秒，取值范围：[30分钟,7天]", "required": true, "style": "form", "schema": {"maximum": 604800, "exclusiveMaximum": false, "minimum": 1800, "exclusiveMinimum": false, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«AkAccessToken»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/openapi/partner/team": {"post": {"tags": ["伙伴签约团队管理API"], "summary": "创建签约团队", "description": "注意，团队创建者必须是签约用户", "operationId": "openapiPartnerTeamPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTeamRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TeamDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/openapi/partner/team/{teamId}/basic": {"put": {"tags": ["伙伴签约团队管理API"], "summary": "修改签约团队基本信息", "operationId": "openapiPartnerTeamByTeamIdBasicPut", "parameters": [{"name": "teamId", "in": "path", "description": "teamId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "name", "in": "query", "description": "name", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/openapi/partner/team/{teamId}/member/{userId}/role": {"put": {"tags": ["伙伴签约团队管理API"], "summary": "修改成员的角色", "operationId": "openapiPartnerTeamByTeamIdMemberByUserIdRolePut", "parameters": [{"name": "teamId", "in": "path", "description": "teamId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "userId", "in": "path", "description": "userId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "role", "in": "query", "description": "role", "required": true, "style": "form", "schema": {"type": "string", "enum": ["boss", "manager", "staff", "superadmin"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/openapi/partner/team/{teamId}/memberIds": {"get": {"tags": ["伙伴签约团队管理API"], "summary": "查询团队成员ID列表", "operationId": "openapiPartnerTeamByTeamIdMemberIdsGet", "parameters": [{"name": "teamId", "in": "path", "description": "teamId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«long»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/openapi/partner/team/{teamId}/members/join": {"put": {"tags": ["伙伴签约团队管理API"], "summary": "添加成员", "description": "返回成功加入团队的成员数目", "operationId": "openapiPartnerTeamByTeamIdMembersJoinPut", "parameters": [{"name": "teamId", "in": "path", "description": "teamId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddTeamMemberRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/openapi/partner/team/{teamId}/members/leave": {"put": {"tags": ["伙伴签约团队管理API"], "summary": "踢出成员", "operationId": "openapiPartnerTeamByTeamIdMembersLeavePut", "parameters": [{"name": "teamId", "in": "path", "description": "teamId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeamMemberRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/openapi/partner/teams": {"get": {"tags": ["伙伴签约团队管理API"], "summary": "查询伙伴签约团队", "operationId": "openapiPartnerTeamsGet", "parameters": [{"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "sortFiled", "in": "query", "description": "sortFiled", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "description": "sortOrder", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TeamDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/openapi/partner/user": {"post": {"tags": ["伙伴签约用户管理API"], "summary": "创建签约用户", "description": "注意，邮箱和手机号必填其中一项，且全局唯一，所以不能跟花漾已有用户的信息冲突。", "operationId": "openapiPartnerUserPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«UserVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/openapi/partner/user/{userId}/basic": {"put": {"tags": ["伙伴签约用户管理API"], "summary": "修改用户昵称和签名", "operationId": "openapiPartnerUserByUserIdBasicPut", "parameters": [{"name": "userId", "in": "path", "description": "userId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "nickname", "in": "query", "description": "nickname", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "signature", "in": "query", "description": "signature", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/openapi/partner/user/{userId}/login": {"put": {"tags": ["伙伴签约用户管理API"], "summary": "修改用户手机号或邮箱", "description": "手机号和邮箱如果都修改为null，则这个用户将无法登陆（除非绑定了微信）。原来的手机号或邮箱，可以创建新的用户", "operationId": "openapiPartnerUserByUserIdLoginPut", "parameters": [{"name": "userId", "in": "path", "description": "userId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "phone", "in": "query", "description": "phone", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "email", "in": "query", "description": "email", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/openapi/partner/user/{userId}/password": {"put": {"tags": ["伙伴签约用户管理API"], "summary": "修改用户密码", "operationId": "openapiPartnerUserByUserIdPasswordPut", "parameters": [{"name": "userId", "in": "path", "description": "userId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "password", "in": "query", "description": "password", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/openapi/partner/user/{userId}/status": {"put": {"tags": ["伙伴签约用户管理API"], "summary": "修改用户状态：是否禁用", "description": "禁用后的用户无法登录", "operationId": "openapiPartnerUserByUserIdStatusPut", "parameters": [{"name": "userId", "in": "path", "description": "userId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "block", "in": "query", "description": "block", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/openapi/partner/users": {"get": {"tags": ["伙伴签约用户管理API"], "summary": "查询伙伴签约用户", "operationId": "openapiPartnerUsersGet", "parameters": [{"name": "teamId", "in": "query", "description": "所属签约团队", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "sortFiled", "in": "query", "description": "sortFiled", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "description": "sortOrder", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«UserVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}}, "components": {"schemas": {"AddTeamMemberRequest": {"title": "AddTeamMemberRequest", "type": "object", "properties": {"roleCode": {"type": "string", "enum": ["boss", "manager", "staff", "superadmin"]}, "userIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "AkAccessToken": {"title": "AkAccessToken", "type": "object", "properties": {"expireTime": {"type": "string", "description": "Token超时时间", "format": "date-time"}, "token": {"type": "string", "description": "登录或请求的token"}}}, "CreateTeamRequest": {"title": "CreateTeamRequest", "type": "object", "properties": {"creatorId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}}}, "CreateUserRequest": {"title": "CreateUserRequest", "type": "object", "properties": {"areaCode": {"type": "string", "description": "手机号区号"}, "email": {"type": "string", "description": "邮箱"}, "gender": {"type": "string", "description": "性别", "enum": ["FEMALE", "MALE", "UNSPECIFIC"]}, "nickname": {"type": "string", "description": "昵称。必填"}, "password": {"type": "string", "description": "登录密码。非必填，设置之后才可以用邮箱+密码登录。长度不小于6位。"}, "phone": {"type": "string", "description": "手机号"}, "signature": {"type": "string", "description": "个性签名"}}}, "PageResult«TeamDto»": {"title": "PageResult«TeamDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TeamDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«UserVo»": {"title": "PageResult«UserVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/UserVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PartnerDto": {"title": "PartnerDto", "type": "object", "properties": {"bankAccount": {"type": "string"}, "bankName": {"type": "string"}, "bankNo": {"type": "string"}, "contactName": {"type": "string"}, "contactPhone": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "fullName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "managerId": {"type": "integer", "format": "int64"}, "oemSupport": {"type": "boolean"}, "openapiSupport": {"type": "boolean"}, "organizedTeamAccountQuota": {"type": "integer", "format": "int32"}, "organizedTeamUserQuota": {"type": "integer", "format": "int32"}, "password": {"type": "string"}, "role": {"type": "string", "enum": ["Broker", "Organizer"]}, "shortName": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}, "teamId": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}}}, "TeamDto": {"title": "TeamDto", "type": "object", "properties": {"avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "deleteTime": {"type": "string", "format": "date-time"}, "domesticCloudEnabled": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "invalidTime": {"type": "string", "format": "date-time"}, "inviteCode": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "overseaCloudEnabled": {"type": "boolean"}, "paid": {"type": "boolean"}, "partnerId": {"type": "integer", "format": "int64"}, "payTime": {"type": "string", "format": "date-time"}, "repurchaseTime": {"type": "string", "format": "date-time"}, "repurchased": {"type": "boolean"}, "status": {"type": "string", "enum": ["Blocked", "Deleted", "Pending", "Ready"]}, "teamType": {"type": "string", "enum": ["crs", "gh", "krShop", "normal", "partner", "plugin", "tk", "tkshop"]}, "tenantId": {"type": "integer", "format": "int64"}, "testing": {"type": "boolean"}, "validateTime": {"type": "string", "format": "date-time"}, "validated": {"type": "boolean"}, "verified": {"type": "boolean"}}}, "TeamMemberRequest": {"title": "TeamMemberRequest", "type": "object", "properties": {"userIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "UserVo": {"title": "UserVo", "type": "object", "properties": {"account": {"type": "string", "description": "账号"}, "avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "email": {"type": "string", "description": "邮箱"}, "gender": {"type": "string", "enum": ["FEMALE", "MALE", "UNSPECIFIC"]}, "id": {"type": "integer", "format": "int64"}, "nickname": {"type": "string"}, "partnerId": {"type": "integer", "format": "int64"}, "phone": {"type": "string", "description": "手机"}, "residentCity": {"type": "string"}, "signature": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}, "tenant": {"type": "integer", "format": "int64"}, "userType": {"type": "string", "enum": ["NORMAL", "PARTNER", "SHADOW"]}, "weixin": {"type": "string"}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«AkAccessToken»": {"title": "WebResult«AkAccessToken»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/AkAccessToken"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«long»»": {"title": "WebResult«List«long»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TeamDto»»": {"title": "WebResult«PageResult«TeamDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TeamDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«UserVo»»": {"title": "WebResult«PageResult«UserVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«UserVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PartnerDto»": {"title": "WebResult«PartnerDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PartnerDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TeamDto»": {"title": "WebResult«TeamDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TeamDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«UserVo»": {"title": "WebResult«UserVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/UserVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«int»": {"title": "WebResult«int»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}}