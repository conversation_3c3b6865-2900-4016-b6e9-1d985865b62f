{"openapi": "3.0.3", "info": {"title": "robot Webhook API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "robot Webhook API", "description": "Webhook Robot Controller"}], "paths": {"/api/robot/webhook": {"post": {"tags": ["WebhookRobotController"], "summary": "添加webhook", "operationId": "robotWebhookPost", "parameters": [{"name": "provider", "in": "query", "description": "provider", "required": true, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "feishu"]}}, {"name": "url", "in": "query", "description": "url", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "name", "in": "query", "description": "name", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "description", "in": "query", "description": "description", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "needSign", "in": "query", "description": "needSign", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "secret", "in": "query", "description": "secret", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«WebhookDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/robot/webhook/notifies": {"get": {"tags": ["WebhookRobotController"], "summary": "查询各类事件的webhook配置", "operationId": "robotWebhookNotifiesGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«WebhookNotifyVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/robot/webhook/notifiy": {"post": {"tags": ["WebhookRobotController"], "summary": "给事件添加webhook通知", "operationId": "robotWebhookNotifiyPost", "parameters": [{"name": "eventType", "in": "query", "description": "eventType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["COMMON", "IpExpire", "IpRenewFailed", "<PERSON><PERSON><PERSON>ill<PERSON><PERSON><PERSON>", "IpWillExpire", "MONITOR_ALERT", "SYS_CLOUD_INVALID_INSTANCE_TYPE", "SYS_MONEY_PAID", "SYS_PROXY_IP_LACK_OF_STOCK"]}}, {"name": "webhookId", "in": "query", "description": "webhookId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«WebhookNotifyDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/robot/webhook/notify/{notifyId}": {"delete": {"tags": ["WebhookRobotController"], "summary": "删除通知配置", "operationId": "robotWebhookNotifyByNotifyIdDelete", "parameters": [{"name": "notifyId", "in": "path", "description": "notifyId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}]}}, "/api/robot/webhook/{webhookId}": {"delete": {"tags": ["WebhookRobotController"], "summary": "删除通知配置", "operationId": "robotWebhookByWebhookIdDelete", "parameters": [{"name": "webhookId", "in": "path", "description": "webhookId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}]}}, "/api/robot/webhooks": {"get": {"tags": ["WebhookRobotController"], "summary": "查询配置的所有webhooks", "operationId": "robotWebhooksGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«WebhookDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}}, "components": {"schemas": {"WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«WebhookDto»»": {"title": "WebResult«List«WebhookDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/WebhookDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«WebhookNotifyVo»»": {"title": "WebResult«List«WebhookNotifyVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/WebhookNotifyVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«WebhookDto»": {"title": "WebResult«WebhookDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/WebhookDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«WebhookNotifyDto»": {"title": "WebResult«WebhookNotifyDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/WebhookNotifyDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebhookDto": {"title": "WebhookDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "creator": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "needSign": {"type": "boolean"}, "provider": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "feishu"]}, "scope": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ipp", "Openapi", "Partner", "Portal"]}, "secret": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "url": {"type": "string"}}}, "WebhookNotifyDto": {"title": "WebhookNotifyDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "eventType": {"type": "string", "enum": ["COMMON", "IpExpire", "IpRenewFailed", "<PERSON><PERSON><PERSON>ill<PERSON><PERSON><PERSON>", "IpWillExpire", "MONITOR_ALERT", "SYS_CLOUD_INVALID_INSTANCE_TYPE", "SYS_MONEY_PAID", "SYS_PROXY_IP_LACK_OF_STOCK"]}, "id": {"type": "integer", "format": "int64"}, "orderNum": {"type": "integer", "format": "int32"}, "scope": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ipp", "Openapi", "Partner", "Portal"]}, "teamId": {"type": "integer", "format": "int64"}, "webhookId": {"type": "integer", "format": "int64"}}}, "WebhookNotifyVo": {"title": "WebhookNotifyVo", "type": "object", "properties": {"eventName": {"type": "string", "description": "事件名称"}, "eventType": {"type": "string", "description": "事件类型", "enum": ["COMMON", "IpExpire", "IpRenewFailed", "<PERSON><PERSON><PERSON>ill<PERSON><PERSON><PERSON>", "IpWillExpire", "MONITOR_ALERT", "SYS_CLOUD_INVALID_INSTANCE_TYPE", "SYS_MONEY_PAID", "SYS_PROXY_IP_LACK_OF_STOCK"]}, "webhooks": {"type": "array", "description": "启用的Webhooks", "items": {"$ref": "#/components/schemas/WebhookDto"}}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}}