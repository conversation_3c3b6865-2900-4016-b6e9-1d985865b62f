{"openapi": "3.0.3", "info": {"title": "Document API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "Document API", "description": "Document Controller"}], "paths": {"/api/document/create": {"post": {"tags": ["DocumentController"], "summary": "创建文档", "operationId": "documentCreatePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDocumentRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«DocumentDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/document/page": {"get": {"tags": ["DocumentController"], "summary": "分页查询文档（不包含正文）", "operationId": "documentPageGet", "parameters": [{"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "status", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Deleted", "Draft", "Released"]}}, {"name": "type", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Email"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«DocumentBriefVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/document/{id}": {"get": {"tags": ["DocumentController"], "summary": "获取文档详情", "operationId": "documentByIdGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«DocumentDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["DocumentController"], "summary": "修改文档", "operationId": "documentByIdPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDocumentRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«DocumentDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "delete": {"tags": ["DocumentController"], "summary": "删除文档", "operationId": "documentByIdDelete", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«DocumentDto»"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/document/{id}/attachment": {"post": {"tags": ["DocumentController"], "summary": "记录上传的附件", "operationId": "documentByIdAttachmentPost", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDocumentAttachmentRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/document/{id}/ossUploadToken": {"get": {"tags": ["DocumentController"], "summary": "获取附件上传的的STS Token", "operationId": "documentByIdOssUploadTokenGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«StsPostSignature»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/document/{id}/status": {"put": {"tags": ["DocumentController"], "summary": "修改文档状态", "operationId": "documentByIdStatusPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "status", "in": "query", "description": "status", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Deleted", "Draft", "Released"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«DocumentDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"CreateDocumentAttachmentRequest": {"title": "CreateDocumentAttachmentRequest", "type": "object", "properties": {"files": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentAttachmentItem"}}}}, "CreateDocumentRequest": {"title": "CreateDocumentRequest", "type": "object", "properties": {"content": {"type": "string"}, "status": {"type": "string", "enum": ["Deleted", "Draft", "Released"]}, "title": {"type": "string"}, "type": {"type": "string", "enum": ["Email"]}}}, "DocumentAttachmentDto": {"title": "DocumentAttachmentDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "documentId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "path": {"type": "string"}, "size": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "type": {"type": "string"}, "url": {"type": "string"}}}, "DocumentAttachmentItem": {"title": "DocumentAttachmentItem", "type": "object", "properties": {"path": {"type": "string", "description": "路径"}, "size": {"type": "integer", "description": "文件大小", "format": "int64"}, "type": {"type": "string", "description": "文件类型"}, "url": {"type": "string", "description": "公共访问链接"}}}, "DocumentBriefVo": {"title": "DocumentBriefVo", "type": "object", "properties": {"category": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "link": {"type": "string"}, "sortNo": {"type": "integer", "format": "int32"}, "status": {"type": "string", "enum": ["Deleted", "Draft", "Released"]}, "teamId": {"type": "integer", "format": "int64"}, "title": {"type": "string"}, "type": {"type": "string", "enum": ["Email"]}, "updateTime": {"type": "string", "format": "date-time"}, "updaterId": {"type": "integer", "format": "int64"}}}, "DocumentDetailVo": {"title": "DocumentDetailVo", "type": "object", "properties": {"attachments": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentAttachmentDto"}}, "category": {"type": "string"}, "content": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "link": {"type": "string"}, "sortNo": {"type": "integer", "format": "int32"}, "status": {"type": "string", "enum": ["Deleted", "Draft", "Released"]}, "teamId": {"type": "integer", "format": "int64"}, "title": {"type": "string"}, "type": {"type": "string", "enum": ["Email"]}, "updateTime": {"type": "string", "format": "date-time"}, "updaterId": {"type": "integer", "format": "int64"}}}, "DocumentDto": {"title": "DocumentDto", "type": "object", "properties": {"category": {"type": "string"}, "content": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "link": {"type": "string"}, "sortNo": {"type": "integer", "format": "int32"}, "status": {"type": "string", "enum": ["Deleted", "Draft", "Released"]}, "teamId": {"type": "integer", "format": "int64"}, "title": {"type": "string"}, "type": {"type": "string", "enum": ["Email"]}, "updateTime": {"type": "string", "format": "date-time"}, "updaterId": {"type": "integer", "format": "int64"}}}, "PageResult«DocumentBriefVo»": {"title": "PageResult«DocumentBriefVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentBriefVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "StsPostSignature": {"title": "StsPostSignature", "type": "object", "properties": {"accessKeyId": {"type": "string"}, "accessKeySecret": {"type": "string"}, "bucketName": {"type": "string"}, "expiration": {"type": "string"}, "fileVal": {"type": "string"}, "policy": {"type": "string"}, "provider": {"type": "string"}, "region": {"type": "string"}, "securityToken": {"type": "string"}, "serverTime": {"type": "string", "format": "date-time"}, "url": {"type": "string"}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«DocumentDetailVo»": {"title": "WebResult«DocumentDetailVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/DocumentDetailVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«DocumentDto»": {"title": "WebResult«DocumentDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/DocumentDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«DocumentBriefVo»»": {"title": "WebResult«PageResult«DocumentBriefVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«DocumentBriefVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«StsPostSignature»": {"title": "WebResult«StsPostSignature»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/StsPostSignature"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-device-token": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-device-token", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}