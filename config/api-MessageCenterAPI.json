{"openapi": "3.0.3", "info": {"title": "Message Center API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "default-remote-messenger-service", "description": "Default Remote Messenger Service"}, {"name": "消息中心相关API", "description": "Message Center Controller"}, {"name": "费用中心远程调用", "description": "Message Center Remote Service Impl"}], "paths": {"/api/remote/messenger/reloadSmsProvider": {"get": {"tags": ["DefaultRemoteMessengerService"], "summary": "reloadSmsProvider", "operationId": "remoteMessengerReloadSmsProviderGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ReloadSmsProviderResultVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/msg-center/broadcasts": {"get": {"tags": ["MessageCenterController"], "summary": "获取系统广播", "operationId": "msgCenterBroadcastsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«BroadcastMsgVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/msg-center/markAllHasRead": {"put": {"tags": ["MessageCenterController"], "summary": "标记某人在团队所有消息为已读", "operationId": "msgCenterMarkAllHasReadPut", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/msg-center/markHasRead": {"put": {"tags": ["MessageCenterController"], "summary": "标记某些消息为已读。ids必须为对应团队的消息，不可以跨团队", "operationId": "msgCenterMarkHasReadPut", "parameters": [{"name": "ids", "in": "query", "description": "ids", "required": true, "style": "form", "explode": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/msg-center/messages": {"get": {"tags": ["MessageCenterController"], "summary": "获取消息列表", "operationId": "msgCenterMessagesGet", "parameters": [{"name": "pageNum", "in": "query", "description": "pageNum", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "messageZoo", "in": "query", "description": "messageZoo", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Activity", "Audit", "Crs", "General", "<PERSON><PERSON>", "<PERSON><PERSON>", "Mobiles", "Payment", "Shops", "Team"]}}, {"name": "messageType", "in": "query", "description": "messageType", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "unread", "in": "query", "description": "是否只获取未读消息", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "from", "in": "query", "description": "粒度目前是做到天，时分秒会被抹掉", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "to", "in": "query", "description": "to", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«UserMessageVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/msg-center/subscribes": {"get": {"tags": ["MessageCenterController"], "summary": "获取用户消息订阅配置", "operationId": "msgCenterSubscribesGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«MessageSubscribeVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "post": {"tags": ["MessageCenterController"], "summary": "配置用户消息订阅", "operationId": "msgCenterSubscribesPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserSubscribeRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«MessageSubscribeVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/msg-center/unreadCount": {"get": {"tags": ["MessageCenterController"], "summary": "获取未读消息数量", "operationId": "msgCenterUnreadCountGet", "parameters": [{"name": "targetTeamId", "in": "query", "description": "不为空表示获取指定团队的，为空表示获取所有团队的", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«UserUnreadMsgCountVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/msg-center/notifyDwlPass": {"post": {"tags": ["MessageCenterRemoteServiceImpl"], "summary": "广播一个事件，通常是控制台调用来给门户的用户发送一条消息", "operationId": "remoteMsgCenterNotifyDwlPassPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DwlNotifyRequest"}}}}, "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/msg-center/notifyDwlReject": {"post": {"tags": ["MessageCenterRemoteServiceImpl"], "summary": "广播一个事件，通常是控制台调用来给门户的用户发送一条消息", "operationId": "remoteMsgCenterNotifyDwlRejectPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DwlNotifyRequest"}}}}, "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/msg-center/notifySysBroadcastChange": {"post": {"tags": ["MessageCenterRemoteServiceImpl"], "summary": "通知门户系统通知有改变", "operationId": "remoteMsgCenterNotifySysBroadcastChangePost", "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"BroadcastMsgVo": {"title": "BroadcastMsgVo", "type": "object", "properties": {"content": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "msgLevel": {"type": "string", "enum": ["Error", "Trace", "<PERSON><PERSON>"]}, "summary": {"type": "string"}}}, "DwlNotifyRequest": {"title": "DwlNotifyRequest", "type": "object", "properties": {"auditId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}}}, "MessageSubscribeVo": {"title": "MessageSubscribeVo", "type": "object", "properties": {"internal": {"type": "boolean"}, "messageType": {"type": "string", "enum": ["Activity", "Bank_Pay_Confirmed", "Credit_Buy_Notify", "Critical_Message", "Crs_Orders_Fetched", "Domain_White_Pass", "Domain_White_Reject", "General", "Ip_Address_Changed", "Ip_Destroyed", "Ip_Expire", "Ip_Import", "Ip_Product_Failed", "Ip_Product_Start", "Ip_Product_Success", "Ip_Renew_Failed", "Ip_Renew_Success", "Ip_UnImport", "<PERSON><PERSON>_<PERSON>_<PERSON>", "Ip_Will_Expire", "Ip_Will_Expire_OneDay", "<PERSON><PERSON><PERSON>_<PERSON><PERSON>_Synced", "<PERSON><PERSON>_New_TK_Message", "Kol_Plan_Failed", "Mobile_Sharing_Notify", "Mobile_Sharing_Result", "New_Order_Need_Pay", "OP_Message", "OP_Money_Come_Notify", "OP_Order_Product_Failed", "OP_Order_Revert_product_failed", "Shop_Info_ReportSuccess", "Shop_Receive_Notify", "Shop_Receive_Result", "Shop_Sharing_Notify", "Shop_Sharing_Result", "Shop_Transfer_Audit_Notify", "Shop_Transfer_Audit_Pass", "Shop_Transfer_Audit_Reject", "User_Exit_team", "User_Join_Team", "User_Join_Team_Audit_Notify", "User_Join_Team_Audit_Pass", "User_Join_Team_Audit_Reject", "User_New_Device"]}, "sms": {"type": "boolean"}, "userId": {"type": "integer", "format": "int64"}, "wechat": {"type": "boolean"}}}, "PageResult«UserMessageVo»": {"title": "PageResult«UserMessageVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/UserMessageVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "ProductInfo": {"title": "ProductInfo", "type": "object", "properties": {"name": {"type": "string"}, "principal": {"type": "string"}, "version": {"type": "string"}}}, "ReloadSmsProviderResultVo": {"title": "ReloadSmsProviderResultVo", "type": "object", "properties": {"currentProduct": {"$ref": "#/components/schemas/ProductInfo"}}}, "UpdateUserSubscribeRequest": {"title": "UpdateUserSubscribeRequest", "type": "object", "properties": {"subscribes": {"type": "array", "items": {"$ref": "#/components/schemas/MessageSubscribeVo"}}}}, "UserMessageVo": {"title": "UserMessageVo", "type": "object", "properties": {"content": {"type": "string", "description": "消息内容"}, "createTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "messageId": {"type": "integer", "format": "int64"}, "messageType": {"type": "string", "enum": ["Activity", "Bank_Pay_Confirmed", "Credit_Buy_Notify", "Critical_Message", "Crs_Orders_Fetched", "Domain_White_Pass", "Domain_White_Reject", "General", "Ip_Address_Changed", "Ip_Destroyed", "Ip_Expire", "Ip_Import", "Ip_Product_Failed", "Ip_Product_Start", "Ip_Product_Success", "Ip_Renew_Failed", "Ip_Renew_Success", "Ip_UnImport", "<PERSON><PERSON>_<PERSON>_<PERSON>", "Ip_Will_Expire", "Ip_Will_Expire_OneDay", "<PERSON><PERSON><PERSON>_<PERSON><PERSON>_Synced", "<PERSON><PERSON>_New_TK_Message", "Kol_Plan_Failed", "Mobile_Sharing_Notify", "Mobile_Sharing_Result", "New_Order_Need_Pay", "OP_Message", "OP_Money_Come_Notify", "OP_Order_Product_Failed", "OP_Order_Revert_product_failed", "Shop_Info_ReportSuccess", "Shop_Receive_Notify", "Shop_Receive_Result", "Shop_Sharing_Notify", "Shop_Sharing_Result", "Shop_Transfer_Audit_Notify", "Shop_Transfer_Audit_Pass", "Shop_Transfer_Audit_Reject", "User_Exit_team", "User_Join_Team", "User_Join_Team_Audit_Notify", "User_Join_Team_Audit_Pass", "User_Join_Team_Audit_Reject", "User_New_Device"]}, "messageZoo": {"type": "string", "enum": ["Activity", "Audit", "Crs", "General", "<PERSON><PERSON>", "<PERSON><PERSON>", "Mobiles", "Payment", "Shops", "Team"]}, "parameters": {"type": "string"}, "readTime": {"type": "string", "description": "阅读时间，如果为空表示当前消息未读", "format": "date-time"}, "resourceId": {"type": "integer", "format": "int64"}, "resourceType": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}, "summary": {"type": "string", "description": "消息摘要"}}}, "UserUnreadMsgCountVo": {"title": "UserUnreadMsgCountVo", "type": "object", "properties": {"messageZoo": {"type": "string", "enum": ["Activity", "Audit", "Crs", "General", "<PERSON><PERSON>", "<PERSON><PERSON>", "Mobiles", "Payment", "Shops", "Team"]}, "msgCount": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "format": "int64"}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«BroadcastMsgVo»»": {"title": "WebResult«List«BroadcastMsgVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/BroadcastMsgVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«MessageSubscribeVo»»": {"title": "WebResult«List«MessageSubscribeVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/MessageSubscribeVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«UserUnreadMsgCountVo»»": {"title": "WebResult«List«UserUnreadMsgCountVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UserUnreadMsgCountVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«UserMessageVo»»": {"title": "WebResult«PageResult«UserMessageVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«UserMessageVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ReloadSmsProviderResultVo»": {"title": "WebResult«ReloadSmsProviderResultVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ReloadSmsProviderResultVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-device-token": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-device-token", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}