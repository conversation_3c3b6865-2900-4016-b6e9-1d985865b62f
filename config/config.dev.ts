// https://umijs.org/config/
import { defineConfig } from 'umi';

export default defineConfig({
  plugins: [
    // https://github.com/zthxxx/react-dev-inspector
    'react-dev-inspector/plugins/umi/react-inspector',
  ],
  // https://github.com/zthxxx/react-dev-inspector#inspector-loader-props
  inspectorConfig: {
    exclude: [],
    babelPlugins: [],
    babelOptions: {},
  },

  // 开发环境下的 webpack 优化
  chainWebpack: (config) => {
    // 开发环境下的性能优化
    if (process.env.NODE_ENV === 'development') {
      // 优化模块解析
      config.resolve.symlinks(false);

      // 优化 babel-loader 缓存
      config.module
        .rule('js')
        .test(/\.(js|mjs|jsx|ts|tsx)$/)
        .exclude.add(/node_modules/)
        .end()
        .use('babel-loader')
        .tap((options) => ({
          ...options,
          cacheDirectory: true,
          cacheCompression: false,
        }));
    }
  },
});
