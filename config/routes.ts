import Functions from './../src/constants/Functions';

export default [
  {
    path: '/login',
    layout: false,
    locale: '登录',
    hideInMenu: true,
    component: './UserLogin/Login',
  },
  {
    path: '/loginSuccess',
    layout: false,
    locale: '登录成功',
    hideInMenu: true,
    component: './UserLogin/LoginSuccess',
  },
  {
    path: '/register',
    layout: false,
    locale: '注册',
    hideInMenu: true,
    component: './UserLogin/Register',
  },
  {
    path: '/findPassword',
    layout: false,
    locale: '找回密码',
    hideInMenu: true,
    component: './UserLogin/FindPassword',
  },
  {
    path: '/invite/:inviteCode',
    layout: false,
    locale: '邀请注册',
    hideInMenu: true,
    component: './UserLogin/Invite',
  },
  {
    path: '/link/invite-join-team/:code',
    layout: false,
    locale: '团队邀请',
    hideInMenu: true,
    component: './Link/InviteJoinTeam',
  },
  {
    path: '/',
    hideInMenu: true,
    component: './Redirect',
  },
  {
    path: '/newCombo',
    hideInMenu: true,
    component: './NewCombo',
  },
  {
    key: 'team',
    hideInMenu: true,
    path: '/team/:teamId',
    component: '@/layouts/team',
    routes: [
      {
        locale: '我的TK店铺',
        component: './Shop',
        path: '/team/:teamId/shop',
        meta: {
          icon: 'dianpu_24',
          iconType: 'color',
        },
      },
      {
        locale: '我的手机账号',
        component: './TikTok/Mine/MobileAccount',
        path: '/team/:teamId/im',
        meta: {
          icon: 'yidongwangluoIP_24',
          iconType: 'color',
        },
      },
      {
        locale: '商品管理',
        component: './ProductManage',
        path: '/team/:teamId/product',
        meta: {
          icon: 'goumai_24',
          iconType: 'color',
        },
      },
      {
        locale: '团队达人库',
        path: '/team/:teamId/creator/store',
        component: './TikTok/Live',
        meta: {
          icon: 'daren_24',
          iconType: 'color',
        },
        routes: [
          {
            hideInMenu: true,
            locale: '团队达人库',
            path: '/team/:teamId/creator/store',
          },
          {
            locale: '达人详情',
            path: '/team/:teamId/creator/store/:id',
            hideInMenu: true,
          },
        ],
        wrappers: ['@/wrappers/func'],
        functionCodes: [Functions.TKSHOP_CREATOR_MANAGER],
      },
      {
        locale: '团队关注的达人',
        path: '/team/:teamId/creator/favor',
        component: './TikTok/Live',
        meta: {
          icon: 'shoucang_24',
          iconType: 'color',
        },
        wrappers: ['@/wrappers/func'],
        functionCodes: [Functions.TKSHOP_CREATOR_MANAGER],
      },
      {
        locale: '分配给我的达人',
        path: '/team/:teamId/creator/mine',
        component: './TikTok/Live',
        meta: {
          icon: 'ziyouIP_24',
          iconType: 'color',
        },
      },
      {
        locale: '达人索样管理',
        meta: {
          icon: 'duzhanshifangwen_24',
          iconType: 'color',
        },
        path: '/team/:teamId/sample',
        component: './Sample',
        routes: [
          {
            hideInMenu: true,
            path: '/team/:teamId/sample',
          },
          {
            hideInMenu: true,
            path: '/team/:teamId/sample/:shopId',
          },
        ],
        wrappers: ['@/wrappers/func'],
        functionCodes: [Functions.TKSHOP_CREATOR_MANAGER],
      },
      {
        locale: '达人的带货视频',
        component: './VideoManage',
        path: '/team/:teamId/video',
        meta: {
          icon: 'duanshipin_24',
          iconType: 'color',
        },
        wrappers: ['@/wrappers/func'],
        functionCodes: [Functions.TKSHOP_CREATOR_MANAGER],
      },
      {
        locale: '达人的带货直播',
        component: './LiveManage',
        path: '/team/:teamId/live',
        meta: {
          icon: 'luxiang_24',
          iconType: 'color',
        },
        wrappers: ['@/wrappers/func'],
        functionCodes: [Functions.TKSHOP_CREATOR_MANAGER],
      },
      {
        locale: '团队买家库',
        path: '/team/:teamId/buyer',
        meta: {
          icon: 'tuandui_24',
          iconType: 'color',
        },
        component: './Buyer',
        wrappers: ['@/wrappers/func'],
        functionCodes: [Functions.TKSHOP_BUYER_MANAGER],
      },
      {
        locale: '订单中心',
        path: '/team/:teamId/order',
        meta: {
          icon: 'wuliufuwu_24',
          iconType: 'color',
        },
        component: './Order',
        wrappers: ['@/wrappers/func'],
        functionCodes: [Functions.TKSHOP_BUYER_MANAGER],
      },
      {
        locale: '产品素材检索',
        path: '/team/:teamId/material',
        component: './Material',
        meta: {
          icon: 'luxiang_24',
          iconType: 'color',
          teamId: 237178435149824,
        },
        routes: [
          {
            hideInMenu: true,
            locale: '产品素材检索',
            path: '/team/:teamId/material',
            component: './Material/List',
          },
          {
            hideInMenu: true,
            locale: '产品素材检索-任务详情',
            path: '/team/:teamId/material/:id',
            component: './Material/Result',
          },
        ],
      },
      {
        locale: '流程编排',
        component: './Schedule',
        path: '/team/:teamId/schedule',
        meta: {
          icon: 'riqi_24',
          iconType: 'color',
        },
        routes: [
          {
            path: '/team/:teamId/schedule/:groupId',
            hideInMenu: true,
          },
        ],
      },
      {
        locale: '任务池',
        component: './Task',
        className: 'doing-task-menu',
        path: '/team/:teamId/task',
        meta: {
          icon: 'renwuchi_24',
          iconType: 'color',
        },
      },
      {
        locale: '历史任务',
        component: './TaskHistory',
        path: '/team/:teamId/taskHistory',
        meta: {
          icon: 'renwu_24',
          iconType: 'color',
        },
        wrappers: ['@/wrappers/func'],
      },
      {
        locale: '公海达人库',
        component: './Global',
        path: '/team/:teamId/global',
        meta: {
          icon: 'gongyouIP_24',
          iconType: 'color',
        },
        wrappers: ['@/wrappers/func'],
        functionCodes: [Functions.TKSHOP_GLOBAL_CREATOR_MANAGER],
      },
      {
        locale: '已屏蔽的达人',
        component: './TikTok/Live',
        path: '/team/:teamId/creator/ignore',
        meta: {
          icon: 'gongnengpingbi_24',
          iconType: 'color',
        },
        wrappers: ['@/wrappers/func'],
        functionCodes: [Functions.TKSHOP_CREATOR_MANAGER],
      },
      {
        locale: '系统设置',
        component: './Setting',
        path: '/team/:teamId/setting',
        meta: {
          icon: 'shezhi_24',
          iconType: 'color',
        },
      },
      {
        hideInMenu: true,
        redirect: '/team/:teamId/shop',
      },
    ],
  },

  // 这个必须放到最后
  {
    component: './404',
    hideInMenu: true,
  },
  {
    path: '*',
    hideInMenu: true,
    component: './Redirect',
  },
];
