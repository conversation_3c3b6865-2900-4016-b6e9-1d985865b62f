{"openapi": "3.0.3", "info": {"title": "Weixin API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "weixin-portal-controller", "description": "Weixin Portal Controller"}, {"name": "weixin-redirect-controller", "description": "Weixin Redirect Controller"}, {"name": "微信二维码API", "description": "Wx Qrcode Controller"}, {"name": "微信二维码业务API", "description": "Wx Subscribe Controller"}, {"name": "微信二维码邀请 API", "description": "Wx Invite Controller"}], "paths": {"/api/wx/portal/{appid}": {"get": {"tags": ["WeixinPortalController"], "summary": "authGet", "operationId": "wxPortalByAppidGet", "parameters": [{"name": "appid", "in": "path", "description": "appid", "required": true, "style": "simple", "schema": {"type": "string"}}, {"name": "signature", "in": "query", "description": "signature", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "timestamp", "in": "query", "description": "timestamp", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "nonce", "in": "query", "description": "nonce", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "echostr", "in": "query", "description": "echostr", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain;charset=utf-8": {"schema": {"type": "string"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "post": {"tags": ["WeixinPortalController"], "summary": "post", "operationId": "wxPortalByAppidPost", "parameters": [{"name": "appid", "in": "path", "description": "appid", "required": true, "style": "simple", "schema": {"type": "string"}}, {"name": "signature", "in": "query", "description": "signature", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "timestamp", "in": "query", "description": "timestamp", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "nonce", "in": "query", "description": "nonce", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "openid", "in": "query", "description": "openid", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "encrypt_type", "in": "query", "description": "encrypt_type", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "msg_signature", "in": "query", "description": "msg_signature", "required": false, "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK", "content": {"application/xml;charset=UTF-8": {"schema": {"type": "string"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/wx/redirect/{appid}/greet": {"get": {"tags": ["WeixinRedirectController"], "summary": "greet", "operationId": "wxRedirectByAppidGreetGet", "parameters": [{"name": "appid", "in": "path", "description": "appid", "required": true, "style": "simple", "schema": {"type": "string"}}, {"name": "state", "in": "query", "description": "state", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "code", "in": "query", "description": "code", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "to", "in": "query", "description": "to", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "map", "in": "query", "description": "map", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain;charset=utf-8": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/wx/invite/join": {"post": {"tags": ["WxInviteController"], "summary": "加入团队", "operationId": "wxInviteJoinPost", "parameters": [{"name": "uuid", "in": "query", "description": "uuid", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "nickname", "in": "query", "description": "nickname", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/wx/invite/qrcode": {"get": {"tags": ["WxInviteController"], "summary": "获取邀请二维码", "operationId": "wxInviteQrcodeGet", "parameters": [{"name": "inviteCode", "in": "query", "description": "inviteCode", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«WxQrCodeVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/wx/invite/qrcodeResult": {"get": {"tags": ["WxInviteController"], "summary": "获取业务二维码扫描结果", "operationId": "wxInviteQrcodeResultGet", "parameters": [{"name": "uuid", "in": "query", "description": "uuid", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«WxQrCodeInviteResult»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/wx/jsSignature": {"get": {"tags": ["WxQrcodeController"], "summary": "获取JS SDK 签名", "operationId": "wxJsSignatureGet", "parameters": [{"name": "url", "in": "query", "description": "url", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«WxJsapiSignature»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/wx/oauth2/getUrl": {"get": {"tags": ["WxQrcodeController"], "summary": "getOAuthUrl", "operationId": "wxOauth2GetUrlGet", "parameters": [{"name": "targetUrl", "in": "query", "description": "targetUrl", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/wx/bindQrCode": {"get": {"tags": ["WxQrcodeController"], "summary": "获取登录二维码", "operationId": "wxBindQrCodeGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«WxQrCodeVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/wx/bindQrCodeResult": {"get": {"tags": ["WxQrcodeController"], "summary": "获取登录二维码扫描结果", "operationId": "wxBindQrCodeResultGet", "parameters": [{"name": "qrcodeId", "in": "query", "description": "qrcodeId", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«WxBindQrCodeResult»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/wx/confirmRegister": {"post": {"tags": ["WxQrcodeController"], "summary": "确认注册新用户", "operationId": "wxConfirmRegisterPost", "parameters": [{"name": "nickname", "in": "query", "description": "nickname", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "qrcodeId", "in": "query", "description": "qrcodeId", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "inviteCode", "in": "query", "description": "inviteCode", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«WxLoginQrCodeResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/wx/loginQrCode": {"get": {"tags": ["WxQrcodeController"], "summary": "获取登录二维码", "operationId": "wxLoginQrCodeGet", "parameters": [{"name": "rememberMe", "in": "query", "description": "rememberMe", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«WxQrCodeVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/wx/loginQrCodeResult": {"get": {"tags": ["WxQrcodeController"], "summary": "获取登录二维码扫描结果", "operationId": "wxLoginQrCodeResultGet", "parameters": [{"name": "qrcodeId", "in": "query", "description": "qrcodeId", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«WxLoginQrCodeResult»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/wx/message/{messageId}": {"get": {"tags": ["WxQrcodeController"], "summary": "查看微信消息", "operationId": "wxMessageByMessageIdGet", "parameters": [{"name": "messageId", "in": "path", "description": "messageId", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaMessageVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/wx/session/{sessionId}": {"get": {"tags": ["WxQrcodeController"], "summary": "查询当前会话信息", "operationId": "wxSessionBySessionIdGet", "parameters": [{"name": "sessionId", "in": "path", "description": "sessionId", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«SessionInfoVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "delete": {"tags": ["WxQrcodeController"], "summary": "踢出会话", "operationId": "wxSessionBySessionIdDelete", "parameters": [{"name": "sessionId", "in": "path", "description": "sessionId", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/wx/unbind": {"put": {"tags": ["WxQrcodeController"], "summary": "解除微信绑定", "operationId": "wxUnbindPut", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/wx/subscribe/cancel/{id}": {"get": {"tags": ["WxSubscribeController"], "summary": "取消订阅", "operationId": "wxSubscribeCancelByIdGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/wx/subscribe/get/{id}": {"get": {"tags": ["WxSubscribeController"], "summary": "获取订阅记录", "operationId": "wxSubscribeGetByIdGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«WxSubscribeDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/wx/subscribe/qrcode": {"post": {"tags": ["WxSubscribeController"], "summary": "获取订阅二维码", "operationId": "wxSubscribeQrcodePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscribeQrCodeRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«WxQrCodeVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/wx/subscribe/qrcodeResult": {"get": {"tags": ["WxSubscribeController"], "summary": "获取业务二维码扫描结果", "operationId": "wxSubscribeQrcodeResultGet", "parameters": [{"name": "uuid", "in": "query", "description": "uuid", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«WxQrCodeBizResult»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"LoginResultVo": {"title": "LoginResultVo", "type": "object", "properties": {"account": {"type": "string"}, "jwt": {"type": "string"}, "jwtExpireTime": {"type": "string", "format": "date-time"}, "jwtId": {"type": "string"}, "needCaptcha": {"type": "boolean", "description": "是否需要验证码", "example": false}, "nickname": {"type": "string"}, "userId": {"type": "integer", "format": "int64"}, "userType": {"type": "string", "enum": ["NORMAL", "PARTNER", "SHADOW"]}}}, "RpaMessageVo": {"title": "RpaMessageVo", "type": "object", "properties": {"attachmentUrls": {"type": "object", "additionalProperties": {"type": "string"}}, "attachments": {"type": "array", "items": {"type": "string"}}, "content": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "detail": {"type": "string"}, "flowId": {"type": "integer", "format": "int64"}, "flowName": {"type": "string"}, "id": {"type": "string", "description": "id"}, "openIds": {"type": "array", "items": {"type": "string"}}, "shopName": {"type": "string"}, "taskId": {"type": "integer", "format": "int64"}, "taskItemId": {"type": "integer", "format": "int64"}, "taskName": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "userIds": {"uniqueItems": true, "type": "array", "items": {"type": "integer", "format": "int64"}}}}, "SessionInfoVo": {"title": "SessionInfoVo", "type": "object", "properties": {"clientIp": {"type": "string"}, "loginTime": {"type": "string", "format": "date-time"}, "nickname": {"type": "string"}, "openId": {"type": "string"}, "phone": {"type": "string"}, "userId": {"type": "integer", "format": "int64"}}}, "SubscribeQrCodeRequest": {"title": "SubscribeQrCodeRequest", "type": "object", "properties": {"attachment": {"type": "object"}, "bizId": {"type": "string"}, "subscribeType": {"type": "string", "enum": ["RpaNotify"]}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RpaMessageVo»": {"title": "WebResult«RpaMessageVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RpaMessageVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«SessionInfoVo»": {"title": "WebResult«SessionInfoVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/SessionInfoVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«WxBindQrCodeResult»": {"title": "WebResult«WxBindQrCodeResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/WxBindQrCodeResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«WxJsapiSignature»": {"title": "WebResult«WxJsapiSignature»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/WxJsapiSignature"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«WxLoginQrCodeResult»": {"title": "WebResult«WxLoginQrCodeResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/WxLoginQrCodeResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«WxQrCodeBizResult»": {"title": "WebResult«WxQrCodeBizResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/WxQrCodeBizResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«WxQrCodeInviteResult»": {"title": "WebResult«WxQrCodeInviteResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/WxQrCodeInviteResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«WxQrCodeVo»": {"title": "WebResult«WxQrCodeVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/WxQrCodeVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«WxSubscribeDto»": {"title": "WebResult«WxSubscribeDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/WxSubscribeDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«string»": {"title": "WebResult«string»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "string"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WxBindQrCodeResult": {"title": "WxBindQrCodeResult", "type": "object", "properties": {"message": {"type": "string"}, "qrcodeId": {"type": "string"}, "rewardObject": {"type": "object"}, "status": {"type": "string", "enum": ["CONFLICTED", "EXPIRED", "FAIL", "REJECTED", "SCANNED", "SUCCESS", "WAIT_SCAN"]}}}, "WxJsapiSignature": {"title": "WxJsapiSignature", "type": "object", "properties": {"appId": {"type": "string"}, "nonceStr": {"type": "string"}, "signature": {"type": "string"}, "timestamp": {"type": "integer", "format": "int64"}, "url": {"type": "string"}}}, "WxLoginQrCodeResult": {"title": "WxLoginQrCodeResult", "type": "object", "properties": {"loginResult": {"$ref": "#/components/schemas/LoginResultVo"}, "message": {"type": "string"}, "needRegister": {"type": "boolean", "description": "true表示未绑定登录需要注册", "example": false}, "nickname": {"type": "string"}, "qrcodeId": {"type": "string"}, "rewardObject": {"type": "object"}, "status": {"type": "string", "enum": ["CONFLICTED", "EXPIRED", "FAIL", "REJECTED", "SCANNED", "SUCCESS", "WAIT_SCAN"]}}}, "WxQrCodeBizResult": {"title": "WxQrCodeBizResult", "type": "object", "properties": {"message": {"type": "string"}, "openIds": {"type": "array", "items": {"type": "string"}}, "status": {"type": "string", "enum": ["CONFLICTED", "EXPIRED", "FAIL", "REJECTED", "SCANNED", "SUCCESS", "WAIT_SCAN"]}, "uuid": {"type": "string"}}}, "WxQrCodeInviteResult": {"title": "WxQrCodeInviteResult", "type": "object", "properties": {"inTeam": {"type": "boolean"}, "message": {"type": "string"}, "nickname": {"type": "string"}, "openId": {"type": "string"}, "status": {"type": "string", "enum": ["CONFLICTED", "EXPIRED", "FAIL", "REJECTED", "SCANNED", "SUCCESS", "WAIT_SCAN"]}, "userId": {"type": "integer", "format": "int64"}, "uuid": {"type": "string"}}}, "WxQrCodeVo": {"title": "WxQrCodeVo", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "expireSeconds": {"type": "integer", "format": "int32"}, "url": {"type": "string"}, "uuid": {"type": "string"}}}, "WxSubscribeDto": {"title": "WxSubscribeDto", "type": "object", "properties": {"appId": {"type": "string"}, "attachment": {"type": "string"}, "bizId": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "openId": {"type": "string"}, "qrcodeUuid": {"type": "string"}, "subscribeType": {"type": "string", "enum": ["RpaNotify"]}, "teamId": {"type": "integer", "format": "int64"}, "valid": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}