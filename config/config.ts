// https://umijs.org/config/
import { defineConfig } from 'umi';
import { join } from 'path';
import tiny_pinyin from 'tiny-pinyin';

import defaultSettings from './defaultSettings';
import routes from './routes';
import mockConfig from './mockConfig';

const { UMI_ENV, NODE_ENV, IS_PRODUCTION, FAST_DEV } = process.env;
const isDevelopment = NODE_ENV === 'development';
const isProduction = IS_PRODUCTION !== 'false';
const isFastDev = FAST_DEV === 'true';
function formatDate(date: Date, format: string) {
  var o = {
    'M+': date.getMonth() + 1, //month
    'd+': date.getDate(), //day
    'h+': date.getHours(), //hour
    'm+': date.getMinutes(), //minute
    's+': date.getSeconds(), //second
    'q+': Math.floor((date.getMonth() + 3) / 3), //quarter
    S: date.getMilliseconds(), //millisecond
  };
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
  }
  for (var k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length),
      );
    }
  }
  return format;
}

export default defineConfig({
  hash: true,
  antd: {},
  reactStrictMode: false,
  dva: {
    hmr: true,
  },
  // 开发环境优化配置
  ...(isDevelopment && {
    // 启用 webpack 持久化缓存
    webpack5: {},
  }),

  // 快速开发模式优化
  ...(isFastDev && {
    // 优化 source map
    devtool: 'eval-cheap-module-source-map',
  }),

  proxy: {
    '/api/': {
      target: mockConfig.backendServer,
      changeOrigin: true,
      cookieDomainRewrite: {
        '*': 'localhost',
      },
      cookiePathRewrite: {
        '*': '/',
      },
    },
  },
  layout: {
    // https://umijs.org/zh-CN/plugins/plugin-layout
    locale: true,
    ...defaultSettings,
  },
  // https://umijs.org/zh-CN/plugins/plugin-locale
  locale: {
    // default zh-CN
    default: 'zh-CN',
    antd: true,
    // default true, when it is true, will use `navigator.language` overwrite default
    baseNavigator: true,
  },
  devtool: isDevelopment ? 'eval-cheap-module-source-map' : (!isProduction ? 'source-map' : false),
  dynamicImport: {
    loading: '@/components/Common/MiddleSpin',
  },
  // 开发环境下的优化配置（暂时禁用 MFSU）
  // ...(isFastDev && {
  //   mfsu: {
  //     development: {
  //       output: './.umi/.cache/.mfsu-dev',
  //     },
  //   },
  // }),
  targets: {
    ie: 11,
  },
  define: {
    'process.env.RELEASE_APP_VERSION': process.env.RELEASE_APP_VERSION || '1.0.8',
    'process.env.RELEASE_MAICOPY_VERSION': process.env.RELEASE_MAICOPY_VERSION || '1.0.8',
    'process.env.BUILD_NUMBER': process.env.BUILD_NUMBER || '9999',
    'process.env.PORTAL_URL': process.env.PORTAL_URL || '',
    'process.env.API_URL': process.env.API_URL || '',
    'process.env.UMI_ENV': process.env.UMI_ENV || '',
    'process.env.DOWNLOAD_SITE': process.env.DOWNLOAD_SITE || '',
    'process.env.IS_PRODUCTION': process.env.IS_PRODUCTION || 'false',
  },
  // umi routes: https://umijs.org/docs/routing
  routes,
  // Theme for antd: https://ant.design/docs/react/customize-theme-cn
  theme: {
    'primary-color': '#0F7CF4',
    'link-color': '#0F7CF4',
    'layout-sider-background': '#141B2B',
    'menu-dark-inline-submenu-bg': '#141B2B',
    'layout-body-background': '#FFFFFF',
    'font-size-base': '14px',
    'border-radius-base': '3px',
    'text-color': '#404040',
    'text-color-secondary': '#666666',
  },
  // esbuild is father build tools
  // https://umijs.org/plugins/plugin-esbuild
  esbuild: {},
  title: false,
  ignoreMomentLocale: true,
  manifest: {
    basePath: '/',
  },
  alias: {
    config: '/config/',
  },
  // Fast Refresh 热更新
  fastRefresh: {},
  openAPI: [
    'Account',
    'OtherAPI',
    'PaymentAPI',
    'RelayAPI',
    'ShopAPI',
    'GoodsAPI',
    'MetaAPI',
    'MessageCenterAPI',
    'EipAPI',
    'RPAAPI',
    'Weixin',
    'TaskAPI',
    'DiskAPI',
    'ActivityAPI',
    'IpPoolAPI',
    'TKShopAPI',
    'OpenakApi',
    'TKGHAPI',
    'TKAPI',
    'DocumentAPI',
  ].map((groupName) => {
    return {
      requestLibPath: "import { request } from 'umi'",
      // 或者使用在线的版本
      // schemaPath: "https://gw.alipayobjects.com/os/antfincdn/M%24jrzTTYJN/oneapi.json"
      schemaPath: join(__dirname, `api-${groupName}.json`),
      mock: false,
      projectName: `api-${groupName}`,
      hook: {
        customClassName: (tag: string) => {
          // 使用标签作为类名
          if (!/^[^\u3220-\uFA29]+$/.test(tag)) {
            return tiny_pinyin.convertToPinyin(tag, '', true);
          } else {
            return tag.charAt(0).toUpperCase() + tag.slice(1);
          }
        },
        customFunctionName: (newApi) => {
          // 使用path全路径加Method作为方法名
          let operationId = newApi.path.replace(/^\/api\//, '');
          // 路径中的参数转化为by
          operationId = operationId.replace(/\{(.*?)\}/g, (word) => {
            return `by${word.substring(1, 2).toUpperCase()}${word.substring(2, word.length - 1)}`;
          });
          // 斜线和中划线改为驼峰命名
          operationId = operationId.replace(/[/-]\w{1}/g, (word) => {
            return word.substring(1, 2).toUpperCase();
          });
          // 去除尾部的斜线
          operationId = operationId.replace(/\//g, '');
          // 加上Method
          const method = newApi.method.replace(/^\w{1}/g, (char) => {
            return char.toUpperCase();
          });
          operationId = `${operationId}${method}`;
          // 去除路径中的参数
          operationId = operationId.replace(/\{.*?\}/g, '');

          return operationId;
        },
      },
    };
  }),
  cssLoader: {
    localsConvention: 'camelCase',
  },
  chainWebpack: (config) => {
    // 快速开发模式优化
    if (isFastDev) {
      // 优化模块解析
      config.resolve.symlinks(false);

      // 优化 babel-loader 缓存
      config.module
        .rule('js')
        .test(/\.(js|mjs|jsx|ts|tsx)$/)
        .exclude.add(/node_modules/)
        .end()
        .use('babel-loader')
        .tap((options) => ({
          ...options,
          cacheDirectory: true,
          cacheCompression: false,
        }));

      // 优化分包策略
      config.optimization.splitChunks({
        chunks: 'all',
        cacheGroups: {
          vendor: {
            name: 'vendors',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'all',
          },
          antd: {
            name: 'antd',
            test: /[\\/]node_modules[\\/]antd[\\/]/,
            priority: 20,
            chunks: 'all',
          },
          react: {
            name: 'react',
            test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
            priority: 30,
            chunks: 'all',
          },
        },
      });
    }

    if (!isDevelopment) {
      // 仅在生产环境启用自定义静态资源目录
      const staticDir = formatDate(new Date(), 'MMddhhmm');
      config.output.chunkFilename(`${staticDir}/[contenthash:8].chunk.js`);
      config.output.filename(`${staticDir}/[contenthash:8].js`);
      config
        .plugin('extract-css')
        .use(require('@umijs/bundler-webpack/lib/webpack/plugins/mini-css-extract-plugin.js'), [
          {
            filename: `${staticDir}/[contenthash:8].css`,
            chunkFilename: `${staticDir}/[contenthash:8].chunk.css`,
            ignoreOrder: true,
          },
        ]);

      config.module
        .rule('images')
        .test(/\.(png|jpe?g|gif|webp|ico)(\?.*)?$/)
        .use('url-loader')
        .loader(require.resolve('url-loader'))
        .tap((options) => {
          const newOptions = {
            ...options,
            name: `${staticDir}/img/[name].[hash:8].[ext]`,
            publicPath: UMI_ENV === 'client' ? '../' : '/',
            fallback: {
              ...options.fallback,
              options: {
                name: `${staticDir}/img/[name].[hash:8].[ext]`,
                esModule: false,
              },
            },
          };
          return newOptions;
        });

      // 修改svg输出目录
      config.module
        .rule('svg')
        .test(/\.(svg)(\?.*)?$/)
        .use('file-loader')
        .loader(require.resolve('file-loader'))
        .tap((options) => ({
          ...options,
          name: `${staticDir}/svg/[name].[hash:8].[ext]`,
          publicPath: UMI_ENV === 'client' ? '../' : '/',
          fallback: {
            ...options.fallback,
            options: {
              name: `${staticDir}/svg/[name].[hash:8].[ext]`,
              esModule: false,
            },
          },
        }));

      // 修改fonts输出目录
      config.module
        .rule('fonts')
        .test(/\.(eot|woff|woff2|ttf)(\?.*)?$/)
        .use('file-loader')
        .loader(require.resolve('file-loader'))
        .tap((options) => ({
          ...options,
          name: `${staticDir}/fonts/[name].[hash:8].[ext]`,
          publicPath: UMI_ENV === 'client' ? '../' : '/',
          fallback: {
            ...options.fallback,
            options: {
              name: `${staticDir}/fonts/[name].[hash:8].[ext]`,
              esModule: false,
            },
          },
        }));
    }
    config.module
      .rule('mjs$')
      .test(/\.mjs$/)
      .include.add(/node_modules/)
      .end()
      .type('javascript/auto');
  },
});
