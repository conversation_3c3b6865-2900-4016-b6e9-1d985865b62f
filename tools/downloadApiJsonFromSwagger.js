const esm = require('esm')(module);
const path = require('path');
const fs = require('fs');
const request = require('umi-request');

const mockConfig = esm('../config/mockConfig.js').default;

console.log(mockConfig, 'mockConfig');

const portal = mockConfig.backendServer;

request.default(`${portal}/api/api-docs/swagger-resources`).then((rs) => {
  rs.forEach((o) => {
    const url = `${portal}/api${o.url}`;
    request.default(url).then((rs1) => {
      const json = rs1;
      const name = o.name.replace(/\s/g, '');
      const jsonFilePath = `${path.dirname(__dirname)}/config/api-${name}.json`;
      console.log(jsonFilePath);

      const tagsName = {};
      if (json.tags) {
        json.tags.forEach((tag) => {
          tagsName[tag.name] = tag.description.replace(/\s/g, '');
        });
      }
      if (json.paths) {
        Object.keys(json.paths).forEach((apiPath) => {
          Object.keys(json.paths[apiPath]).forEach((method) => {
            const action = json.paths[apiPath][method];
            action.tags = action.tags.map((tag) => {
              return tagsName[tag];
            });

            // 使用path全路径加Method作为方法名
            let operationId = apiPath.replace(/^\/api\//, '');
            // 路径中的参数转化为by
            operationId = operationId.replace(/\{(.*?)\}/g, (word) => {
              return `by${word.substring(1, 2).toUpperCase()}${word.substring(2, word.length - 1)}`;
            });
            // 斜线和中划线改为驼峰命名
            operationId = operationId.replace(/[/-]\w{1}/g, (word) => {
              return word.substring(1, 2).toUpperCase();
            });
            // 去除尾部的斜线
            operationId = operationId.replace(/\//g, '');
            // 加上Method
            const apiMethod = method.replace(/^\w{1}/g, (char) => {
              return char.toUpperCase();
            });
            operationId = `${operationId}${apiMethod}`;

            action.operationId = operationId;
            // 修正返回数据的contentType
            if (action.responses['200'].content) {
              action.responses['200'].content['application/json'] =
                action.responses['200'].content['*/*'];
              delete action.responses['200'].content['*/*'];
            }
          });
        });
      }

      if (json?.components?.schemas) {
        // 删除data引用自身的情况
        Object.keys(json.components.schemas).forEach((k) => {
          const schema = json.components.schemas[k];
          if (schema?.properties?.data?.$ref) {
            const splitResult = schema.properties.data.$ref.split('/');
            const refSchema = splitResult[splitResult.length - 1];
            if (refSchema === k) {
              delete json.components.schemas[k].properties.data;
            }
          }
        });

        // 删除相互引用的情况
        const getObjectRefsDeep = (obj) => {
          let refs = [];
          if (obj) {
            Object.keys(obj).forEach((k) => {
              if (typeof obj[k] === 'object') {
                refs = refs.concat(getObjectRefsDeep(obj[k]));
              }
              if (k === '$ref') {
                const splitResult = obj.$ref.split('/');
                const refSchema = splitResult[splitResult.length - 1];
                refs.push(refSchema);
              }
            });
          }
          return refs;
        };
        const deleteRef = (obj, refKey) => {
          Object.keys(obj).forEach((k) => {
            if (typeof obj[k] === 'object') {
              deleteRef(obj[k], refKey);
            }
            if (k === '$ref') {
              const splitResult = obj.$ref.split('/');
              const refSchema = splitResult[splitResult.length - 1];
              if (refSchema === refKey) {
                // eslint-disable-next-line no-param-reassign
                delete obj[k];
              }
            }
          });
        };
        const refMap = {};
        Object.keys(json.components.schemas).forEach((k) => {
          const schema = json.components.schemas[k];
          const refs = getObjectRefsDeep(schema?.properties);
          refMap[k] = refs;
        });
        Object.keys(json.components.schemas).forEach((k) => {
          refMap[k].forEach((ref) => {
            if (refMap[ref] && refMap[ref].indexOf(k) > -1) {
              deleteRef(json.components.schemas[k], ref);
            }
          });
        });
      }

      fs.writeFileSync(jsonFilePath, JSON.stringify(json));
    });
  });
});
