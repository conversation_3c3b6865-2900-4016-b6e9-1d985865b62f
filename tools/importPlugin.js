const esm = require('esm')(module);
const path = require('path');
const fs = require('fs');
const cryptoJs = require("crypto-js");
const request = require('umi-request');

const daMaiConsoleApi = "https://admin.thinkoncloud.cn";
const daMaiAuthorization = "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";


const sleep = (delay) => new Promise((resolve) => setTimeout(resolve, delay || 0));

const encode = (e) => {
  const o = "g0MW7Hpf006DaEBT";
  const t = "7Hpf006DaEBT5fsP";

  if (!e) return "";
  var n = t, i = o, l = e,
    u = decodeURIComponent("%00")
    , d = new RegExp(u, "g");
  n && (n = cryptoJs.enc.Utf8.parse(n)), n = n || s, i = i || a, l += u.repeat(16 - l.length % 16);
  var d = cryptoJs.AES.encrypt(l, cryptoJs.enc.Utf8.parse(i), {
    mode: cryptoJs.mode.CBC,
    iv: n,
    padding: cryptoJs.pad.ZeroPadding
  });
  // console.log(l, d.toString());
  return d.toString();
}

const decode = (e) => {
  const o = "0MW7Hpf006DaEBT5";
  const t = "W7Hpf006DaEBT5fs";
  const r = cryptoJs;

  const d = new RegExp(u, "g")
    , i = ""
    , l = "";
  if (!e)
    return "";
  var n = t
    , a = o
    , s = e;
  n && (n = r.enc.Utf8.parse(n)),
    n = n || l,
    a = a || i;
  var u = r.AES.decrypt(s, r.enc.Utf8.parse(a), {
    mode: r.mode.CBC,
    iv: n,
    padding: r.pad.ZeroPadding
  });

  var g = u.toString(r.enc.Utf8).replace(d, "");
  return g
}


// const rs = decode("CN7pDDApKVEmOdmO7gcK8N5s8s03e+5SaNwfAi0oe1AAs8qL1dfs5K0EHaZF4Sv92GMuRlGn+2y/vFzrAtP3SmTiymbNaBTle0AEFHWRZNGNVSmXI3WT/27SREwAtNIhxBuiTmS2usXwi4LzRstSSdmPIYo/ILBpX+oBqjAxKD482SvnIng4pwJQBiIjsCt81PX6Xk34aDwoxdCutbNtwTABj8BJrY90GbIV45eoCPqZzr7FipONM5Y2fdMWdT0ah6dw+H5HQbFdsFKd4lPldIhECB/xLQXQs8HFkqmL79QqFTAbGubHes8fhB1x71sWqcfiy4JRNkmTNV8lyIQD0p2Bmb8CUkOcLUDmldjwASWfNyrj7V1OJjmnG/4HXEHVj9W5FhcdsWkxQHTf7tf734YiNGFuU5K2WNIAvQnY53N/r1qM3qexRce+pDqqPIt3qvysecM5fAWC8vx3l6Niit4aj5gkPi4PRS70vjCUr0XOP6x/zpaJE+UCsMEQ4Ih45DxqKcWA2Qb7EMV23hsVbHYF7X0Cke8AqX7DINtkNBeawjh3TXaD7yn3ozmS/w+u");
// console.log(rs);

const doCategory = async (ziniaoClassId, daMaiCategory, pageNo) => {
  const req = { "pageNo": pageNo, "sortType": "userInstallDesc", "pageSize": 30, "installed": false, "touristInstalledIds": [], "classId": ziniaoClassId, "machineString": "", "browserPlatform": 1, "browserType": "unknown", "language": "zh-CN", "channelId": 1 };


  // yrv8vb6jG3FQrGVy0KVL0sf6UjZUZq5YRridmG05qizfSVLT3IajES1o76v9hl9hK10cciBBWMzcFag5/36iNVFFoB+kyMNVOA6OVovfBXeaJn/KN+Mu1RO/Z737k6DDU1wVgR6fv62lIwaw4SkJ7N/B/5ncae2yElkLqjI/oCm97VXFfsIKGYxkFd5LcNiJcUTC5lSxipYuSXkWs2r6pxYZO1HfzSFrovEXxVOnm0rEH4JOiFvoJ8MGWxXFUh9EtVpEAuwhXnLzCSPhoGT9Xg==
  // console.log(encode('{"pageNo":1,"sortType":"userInstallDesc","pageSize":30,"installed":false,"touristInstalledIds":[],"pageId":2,"machineString":"","browserPlatform":1,"browserType":"unknown","language":"zh-CN","channelId":1}'));

  // console.log(encode(JSON.stringify(req)));
  let body = ({
    data: encode(JSON.stringify(req)),
    machineString: ""
  });
  // console.log(body);

  // body="{\"data\":\"yrv8vb6jG3FQrGVy0KVL0sf6UjZUZq5YRridmG05qizfSVLT3IajES1o76v9hl9hK10cciBBWMzcFag5/36iNVFFoB+kyMNVOA6OVovfBXeaJn/KN+Mu1RO/Z737k6DDU1wVgR6fv62lIwaw4SkJ7N/B/5ncae2yElkLqjI/oCm97VXFfsIKGYxkFd5LcNiJcUTC5lSxipYuSXkWs2r6pxYZO1HfzSFrovEXxVOnm0rEH4JOiFvoJ8MGWxXFUh9EtVpEAuwhXnLzCSPhoGT9Xg==\",\"machineString\":\"\"}";

  return request.default("https://sbappstoreapi.ziniao.com/rest/sop-appstore/app/front/page/list", {
    "headers": {
      "accept": "application/json, text/plain, */*",
      "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
      "cache-control": "no-cache",
      "content-type": "application/json",
      "pragma": "no-cache",
      "sec-ch-ua": "\"(Not(A:Brand\";v=\"8\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"",
      "sec-ch-ua-mobile": "?0",
      "sec-ch-ua-platform": "\"Windows\"",
      "sec-fetch-dest": "empty",
      "sec-fetch-mode": "cors",
      "sec-fetch-site": "same-site",
      "Referer": "https://appstore.ziniao.com/",
      "Referrer-Policy": "strict-origin-when-cross-origin"
    },
    method: 'post',
    data: body,
  }).then((rs) => {
    // console.log(rs);
    const data = JSON.parse(decode(rs.data));
    console.log(`${data.size}/${data.total}`);
    return publish(data, daMaiCategory);
  }).catch(e => {
    console.error(e);
  });

}

async function publish(data, daMaiCategory) {
  for (let i in data.content) {
    const o = data.content[i];
    if (o.chromeEnable && o.chromeId) {
      const url = `https://chrome.google.com/webstore/detail/${o.chromeId}`;
      await request.default(`${daMaiConsoleApi}/api/console/extensions/importExtension`, {
        "headers": {
          "accept": "application/json",
          "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
          "authorization": daMaiAuthorization,
          "cache-control": "no-cache",
          "content-type": "application/json;charset=UTF-8",
          "pragma": "no-cache",
          "sec-ch-ua": "\"(Not(A:Brand\";v=\"8\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"",
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": "\"Windows\"",
          "sec-fetch-dest": "empty",
          "sec-fetch-mode": "cors",
          "sec-fetch-site": "same-origin",
          "cookie": "console-session=OGExNTZiMDEyZDdlNDlmMGI1ZGI0ZWY3NjNhNmViMDQ=",
          "Referer": "https://admin.thinkoncloud.cn/devOps/extensionManage",
          "Referrer-Policy": "strict-origin-when-cross-origin"
        },
        "data": { "url": url, "category": daMaiCategory },
        "method": "PUT"
      }).then(rs => {
        if (!rs.success || !rs.data.id) {
          console.error(`${o.chromeId} 导入失败`);
          console.error(rs);
          return;
          // process.exit(-1);
        }
        return request.default(`${daMaiConsoleApi}/api/console/extensions/${rs.data.id}/publish`, {
          "headers": {
            "accept": "*/*",
            "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "authorization": daMaiAuthorization,
            "cache-control": "no-cache",
            "pragma": "no-cache",
            "sec-ch-ua": "\"(Not(A:Brand\";v=\"8\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "cookie": "console-session=OGExNTZiMDEyZDdlNDlmMGI1ZGI0ZWY3NjNhNmViMDQ=",
            "Referer": "https://admin.thinkoncloud.cn/devOps/extensionManage",
            "Referrer-Policy": "strict-origin-when-cross-origin"
          },
          "body": null,
          "method": "PUT"
        }).then(async rs2 => {
          if (!rs2.success) {
            console.error(rs2);
            process.exit(-1);
          }
          console.log(rs.data.title, daMaiCategory);
        });
      });
      await sleep(1000);
      // return true;
    }
  }
}


// https://appstore.ziniao.com/plugin/2/123683/DianPuYunYing.html
const category = [
  [123681, 'ChooseGoods'],
  [123682, 'Productivity'], // 运营工具
  [123683, 'ShopOperator'],
  [123684, 'Marketing'],
  [123685, 'General'],
];

const main = async () => {
  for (let i in category) {
    const o = category[i];
    const ziniaoClassId = o[0];
    const pageNo = 1;
    const daMaiCategory = o[1];
    await doCategory(ziniaoClassId, daMaiCategory, pageNo)
  }

}
main();