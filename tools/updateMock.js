const path = require('path');
const fs = require('fs');

// 将mock数据中的success全改为true
// 将jwt改为英文，解决浏览器报错的问题
// 将url中的"{teamId}"改为":teamId"

const mockDir = `${path.dirname(__dirname)}/mock`;
const files = fs.readdirSync(mockDir);
files.forEach((file) => {
  const fullPath = `${mockDir}/${file}`;
  if (/^.*\.mock\.ts$/.test(file) && fs.statSync(fullPath).isFile()) {
    let content = fs.readFileSync(fullPath, 'utf-8');
    content = content.replace(/success: false/g, 'success: true');
    content = content.replace(/code: \d+/g, 'code: 0');
    content = content.replace(/jwt: '.*'/g, "jwt: 'MockJwt'");
    content = content.replace(
      /(\/api\/account\/refreshToken'[\s\S?]+data:)\s+'.*'/g,
      "$1 'MockJwt'",
    );
    content = content.replace(/\/\{(.*)\}'/g, "/:$1/get'");
    content = content.replace(/\/\{(.*)\}/g, '/:$1');
    fs.writeFileSync(fullPath, content);
    console.log(fullPath);
  }
});

const servicesDir = `${path.dirname(__dirname)}/src/services`;
const replaceControllers = (dir) => {
  const items = fs.readdirSync(dir);
  items.forEach((file) => {
    const fullPath = `${dir}/${file}`;
    const stat = fs.statSync(fullPath);
    if (stat.isFile()) {
      if (/^.*Controller\.ts$/.test(file)) {
        let content = fs.readFileSync(fullPath, 'utf-8');
        if (/\(`(.*)\$\{(.*)\}`, \{/.test(content)) {
          content = `import mockConfig from 'config/mockConfig';\n${content}`;
          content = content.replace(
            /\(`(.*)\$\{(.*)\}`, \{/g,
            `(\`$1$\{$2}$\{mockConfig.useMock?'/get':''}\`, {`,
          );
          fs.writeFileSync(fullPath, content);
          console.log(fullPath);
        }
      }
    } else if (stat.isDirectory()) {
      replaceControllers(fullPath);
    }
  });
};
replaceControllers(servicesDir);
