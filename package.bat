node -v

set RELEASE_APP_VERSION=%RELEASE_APP_MAIN_VERSION%.%BUILD_NUMBER%
set PLATFORM=windows
set CHROMIUM_URL=%CHROMIUM_URL%
set BROWSER_BUILD_VERSION=%BROWSER_BUILD_VERSION%
set PORTAL_URL=%PORTAL_URL%
set API_URL=%API_URL%
set SIGN_URL=%SIGN_URL%
set BUILD_NUMBER=%BUILD_NUMBER%
set API_URL=%API_URL%
set API_URL=%API_URL%
set ELECTRON_MIRROR=http://npm.taobao.org/mirrors/electron/

call yarn config set registry https://registry.npm.taobao.org -g
call yarn config set sass_binary_site http://cdn.npm.taobao.org/dist/node-sass -g
call yarn config get registry
call yarn install --network-timeout 1000000000
call yarn electron:build