{"success": true, "data": {"shop": {"eBay": {"urls": ["^https://signin.ebay.(com|ca|co.uk|de|fr|it|nl|ie|ch|es|at|pl|ph|com.my|com.sg|com.au|in|com.tw|com.hk)/", "^https://www.ebay.(com|ca|co.uk|de|fr|it|nl|ie|ch|es|at|pl|ph|com.my|com.sg|com.au|in|com.tw|com.hk)/signin/", "^https://signin.benl.ebay.be/", "^https://www.benl.ebay.be/signin/"], "script": [{"type": "removeClass", "selector": "#signin-form > div:nth-child(1)", "value": "hide"}, {"type": "addClass", "selector": "#signin-form > div:nth-child(2)", "value": "hide"}, {"type": "input", "selector": "#userid", "value": "@username"}, {"type": "click", "selector": "#signin-continue-btn", "depend": "username"}, {"type": "timeout", "value": 1000}, {"type": "addClass", "selector": "#signin-form > div:nth-child(1)", "value": "hide"}, {"type": "removeClass", "selector": "#signin-form > div:nth-child(2)", "value": "hide"}, {"type": "input", "selector": "#pass", "value": "@password"}]}, "Amazon": {"urls": ["^https://sellercentral.amazon.com/ap/signin"], "script": [{"type": "input", "selector": "#ap_email", "value": "@username"}, {"type": "input", "selector": "#ap_password", "value": "@password"}]}, "Lazada": {"urls": ["^https://member.lazada.(co.id|com.my|com.ph|sg|co.th|vn)/user/login"], "script": [{"type": "input", "selector": "#container .mod-input.mod-login-input-loginName.mod-input-loginName > input[type=text]", "value": "@username"}, {"type": "input", "selector": "#container .mod-input.mod-input-password.mod-login-input-password.mod-input-password > input[type=password]", "value": "@password"}]}, "Shopee": {"urls": ["^https://seller.xiapi.shopee.cn/account/signin", "^https://seller.shopee.(com.my|tw|sg|co.th|ph|com.br|com.mx|com.co|cl|pl|es|fr|in|vn)/account/signin", "^https://banhang.shopee.vn/account/signin"], "script": [{"type": "input", "selector": "#shop-login > div:nth-child(1) > div > div > div > div > input", "value": "@username"}, {"type": "input", "selector": "#shop-login > div:nth-child(2) > div > div > div > div > input", "value": "@password"}]}}, "payment": {"LianLian": {"urls": ["^https://global.lianlianpay.com/login"], "script": [{"type": "input", "selector": ".login-form-wrap.panel.panel-sp > .login-form > form input[type=text]", "value": "@username"}, {"type": "input", "selector": ".login-form-wrap.panel.panel-sp > .login-form > form input[type=password]", "value": "@password"}]}, "PayPal": {"urls": ["^https://www.paypal.com/(c2|hk|us|ca|my|sg|ph|jp|gb|fr|de|it)/signin"], "script": [{"type": "input", "selector": "#email", "value": "@username"}, {"type": "input", "selector": "#password", "value": "@password"}]}, "Payoneer": {"urls": ["^https://login.payoneer.com/"], "script": [{"type": "input", "selector": "#username", "value": "@username"}, {"type": "input", "selector": "#app div.log-in-app-container input[type=password]", "value": "@password"}]}, "epay": {"urls": ["^https://epay.163.com/"], "script": [{"type": "click", "selector": "#loginMenuBtn"}, {"type": "click", "selector": "#tabBtns1 > .mobiletab", "match": {"target": "@username", "reg": "^\\d+$"}}, {"type": "click", "selector": "#tabBtns1 > .mailtab", "match": {"target": "@username", "reg": "@"}}, {"type": "iframe", "selector": "#lgoinDialog #mb-reg iframe", "match": {"target": "@username", "reg": "^\\d+$"}}, {"type": "iframe", "selector": "#lgoinDialog #j-epay-emailLoginWrap iframe", "match": {"target": "@username", "reg": "@"}}, {"type": "input", "selector": "#login-form #phoneipt", "value": "@username", "match": {"target": "@username", "reg": "^\\d+$"}}, {"type": "input", "selector": "#login-form input.dlemail", "value": "@username", "match": {"target": "@username", "reg": "@"}}, {"type": "input", "selector": "#login-form input.j-inputtext", "value": "@password", "match": {"target": "@username", "reg": "^\\d+$"}}, {"type": "input", "selector": "#login-form input.dlpwd", "value": "@password", "match": {"target": "@username", "reg": "@"}}]}, "PingPong": {"urls": ["^https://us.pingpongx.com/entrance/signin"], "script": [{"type": "click", "selector": "#app .singleAccount > ul > li:nth-child(1)", "match": {"target": "@username", "reg": "^\\d+$"}}, {"type": "click", "selector": "#app .singleAccount > ul > li:nth-child(3)", "match": {"target": "@username", "reg": "@"}}, {"type": "input", "selector": "#app .singleAccount .login-form input[type=number]", "value": "@username", "match": {"target": "@username", "reg": "^\\d+$"}}, {"type": "input", "selector": "#app .singleAccount .login-form input[type=text]", "value": "@username", "match": {"target": "@username", "reg": "@"}}, {"type": "input", "selector": "#app .singleAccount .login-form input[type=password]", "value": "@password", "match": {"target": "@username", "reg": "^\\d+$"}}, {"type": "input", "selector": "#app .singleAccount .login-form input[type=password]", "value": "@password", "match": {"target": "@username", "reg": "@"}}]}, "WorldFirst": {"urls": ["^https://portal.worldfirst.com.cn/login"], "script": [{"type": "input", "selector": "#username", "value": "@username"}, {"type": "input", "selector": "#password", "value": "@password"}]}, "Airwallex": {"urls": ["^https://www.airwallex.com/app1/login"], "script": [{"type": "click", "selector": "form[name=loginForm] > div > div > div > h3:nth-child(2)", "match": {"target": "@username", "reg": "^\\d+$"}}, {"type": "click", "selector": "form[name=loginForm] > div > div > div > h3:nth-child(1)", "match": {"target": "@username", "reg": "@"}}, {"type": "input", "selector": "#phone", "value": "@username", "match": {"target": "@username", "reg": "^\\d+$"}}, {"type": "input", "selector": "#email", "value": "@username", "match": {"target": "@username", "reg": "@"}}, {"type": "input", "selector": "#password", "value": "@password", "match": {"target": "@username", "reg": "^\\d+$"}}, {"type": "input", "selector": "#password", "value": "@password", "match": {"target": "@username", "reg": "@"}}]}}, "mail": {"Gmail": {"urls": ["^https://accounts.google.com/ServiceLogin"], "script": [{"type": "input", "selector": "#identifierId", "value": "@username"}, {"type": "click", "selector": "#identifierNext > div > button"}, {"type": "timeout", "value": 2000}, {"type": "input", "selector": "#password input", "value": "@password"}]}, "Outlook": {"urls": ["^https://login.live.com/login.srf"], "script": [{"type": "input", "selector": ".placeholderContainer > input[type=email]", "value": "@username"}, {"type": "click", "selector": "#idSIButton9"}, {"type": "timeout", "value": 1000}, {"type": "input", "selector": ".placeholderContainer > input[type=password]", "value": "@password"}]}, "Yahoo": {"urls": ["^https://login.yahoo.com/"], "script": [{"type": "input", "selector": "#login-username", "value": "@username"}, {"type": "click", "selector": "#login-signin"}, {"type": "timeout", "value": 2000}, {"type": "input", "selector": "#login-passwd", "value": "@password"}]}, "Yahoo_jp": {"urls": ["^https://login.yahoo.co.jp/config/login"], "script": [{"type": "input", "selector": "#username", "value": "@username"}, {"type": "click", "selector": "#btnNext"}, {"type": "timeout", "value": 1000}, {"type": "input", "selector": "#passwd", "value": "@password"}]}, "MailDotCom": {"urls": ["^https://www.mail.com/int/"], "script": [{"type": "click", "selector": "#login-button"}, {"type": "timeout", "value": 1000}, {"type": "input", "selector": "#login-email", "value": "@username"}, {"type": "input", "selector": "#login-password", "value": "@password"}]}, "Zoho": {"urls": ["^https://accounts.zoho.com.cn/signin"], "script": [{"type": "input", "selector": "#login_id", "value": "@username"}, {"type": "click", "selector": "#nextbtn"}, {"type": "timeout", "value": 1000}, {"type": "input", "selector": "#password", "value": "@password"}]}, "Royal": {"urls": ["^https://www.royalmail.com/login"], "script": [{"type": "input", "selector": "#edit-name", "value": "@username"}, {"type": "input", "selector": "#edit-pass", "value": "@password"}]}, "NetEasy_163": {"urls": ["^https://mail.163.com/"], "script": [{"type": "iframe", "selector": "#loginDiv > iframe"}, {"type": "input", "selector": "#login-form .dlemail", "value": "@username"}, {"type": "input", "selector": "#login-form .dlpwd", "value": "@password"}]}, "NetEasy_126": {"urls": ["^https://mail.126.com/"], "script": [{"type": "iframe", "selector": "#loginDiv > iframe"}, {"type": "input", "selector": "#login-form .dlemail", "value": "@username"}, {"type": "input", "selector": "#login-form .dlpwd", "value": "@password"}]}, "NetEasy_yeah": {"urls": ["^https://www.yeah.net/"], "script": [{"type": "iframe", "selector": "#loginDiv > iframe"}, {"type": "input", "selector": "#login-form .dlemail", "value": "@username"}, {"type": "input", "selector": "#login-form .dlpwd", "value": "@password"}]}, "NetEasy_vip": {"urls": ["^https://vipmail.163.com/"], "script": [{"type": "iframe", "selector": "#urs-login163 > iframe"}, {"type": "input", "selector": "#login-form .dlemail", "value": "@username"}, {"type": "input", "selector": "#login-form .dlpwd", "value": "@password"}]}, "TencentEX": {"urls": ["^https://exmail.qq.com/login"], "script": [{"type": "click", "selector": ".js_show_pwd_panel"}, {"type": "input", "selector": "#inputuin", "value": "@username"}, {"type": "input", "selector": "#pp", "value": "@password"}]}, "TencentQQ": {"urls": ["^https://mail.qq.com/"], "script": [{"type": "iframe", "selector": "#login_frame"}, {"type": "input", "selector": "#u", "value": "@username"}, {"type": "input", "selector": "#p", "value": "@password"}]}, "Sohu": {"urls": ["^https://mail.sohu.com/"], "script": [{"type": "input", "selector": "#theme > form > div.field.f-account > div.ipt-box.pos-r > input", "value": "@username"}, {"type": "input", "selector": "#theme > form > div.field.f-pwd > div.ipt-box > input", "value": "@password"}]}, "Sina": {"urls": ["^https://mail.sina.com.cn/"], "script": [{"type": "input", "selector": "#freename", "value": "@username"}, {"type": "input", "selector": "#freepassword", "value": "@password"}]}, "ChinaMobile": {"urls": ["^https://mail.10086.cn/"], "script": [{"type": "click", "selector": "#Account"}, {"type": "timeout", "value": 1500}, {"type": "input", "selector": "#txtUser", "value": "@username"}, {"type": "input", "selector": "#txtPass", "value": "@password"}]}, "ChinaTelecom": {"urls": ["^https://webmail30.189.cn/w2/"], "script": [{"type": "iframe", "selector": "#iframeLogin"}, {"type": "input", "selector": "#userName", "value": "@username"}, {"type": "input", "selector": "#password", "value": "@password"}]}, "mail_263": {"urls": ["^https?://mail.263.net/"], "script": [{"type": "click", "selector": "#usernameTip"}, {"type": "timeout", "value": 1000}, {"type": "input", "selector": "#username", "value": "@username"}, {"type": "click", "selector": "#userType"}, {"type": "input", "selector": "#userTypePwd", "value": "@password"}]}, "tom": {"urls": ["^https://mail.tom.com/"], "script": [{"type": "click", "selector": "div.login_tab > button:nth-child(2)"}, {"type": "timeout", "value": 1000}, {"type": "input", "selector": "#username", "value": "@username"}, {"type": "input", "selector": "#password", "value": "@password"}]}}}}