const Scaledrone = require('scaledrone-node');

const channel = 'Pawxaxypf9GMY48u';
const roomName = 'observable-my-rpa-room';

const createServer = async ()=>{
  const sd = new Scaledrone(channel, {
    autoReconnect: false
  });
  await new Promise((resolve, reject) => {
    sd.on('open', (error)=>{
      if(error) {
        reject(error);
      } else {
        resolve(1);
      }
    });
  });
  let locked = false;
  const room = sd.subscribe(roomName);
  await new Promise((resolve, reject) => {
    room.on('open', (error) => {
      if(error) {
        reject(error);
      } else {
        resolve(1);
      }
    });
  });
  const sendMessage = (message) => {
    sd.publish({
      room: roomName,
      message
    });
  };
  const onData = (data) => {
    switch (data.action) {
      case 'init':
        if(locked) {
          sendMessage({action:'error', msg: '当前已经打开了'});
          return;
        } else {
          locked = true;
          sendMessage({action: 'init-ok'});
        }
        break;
    }
  };
  room.on('members', members => {
    console.log('MEMBERS', members);
  });

  room.on('message', (message)=>{
    if(message.clientId == sd.clientId) {
      return;
    }
    onData(message.data);
  });

  return sd;
};

const createClient = async()=>{
  const sd = new Scaledrone(channel, {
    autoReconnect: false
  });
  await new Promise((resolve, reject) => {
    sd.on('open', (error)=>{
      if(error) {
        reject(error);
      } else {
        resolve(1);
      }
    });
  });

  const room = sd.subscribe(roomName);
  await new Promise((resolve, reject) => {
    room.on('open', (error) => {
      if(error) {
        reject(error);
      } else {
        resolve(1);
      }
    });
  });
  const sendMessage = (message) => {
    sd.publish({
      room: roomName,
      message
    });
  };
  let waitTimeout = setTimeout(()=>{
    console.error('连接超时');
    sd.close();
  }, 30000);

  const onData = (data) => {
    clearTimeout(waitTimeout);
    switch (data.action) {
      case 'error':
        sd.close();
        console.error(data.msg);
        break;
      case 'init-ok':
        console.log('init ok, waiting for webrtc...');
        //do nothing
        break;
    }
  };

  room.on('message', (message)=>{
    if(message.clientId == sd.clientId) {
      return;
    }
    onData(message.data);
  });

  //发起初始化
  sendMessage({
    action: 'init'
  });

  return sd;
};

(async ()=>{
  let server = await createServer();
  let client = await createClient();



})();
