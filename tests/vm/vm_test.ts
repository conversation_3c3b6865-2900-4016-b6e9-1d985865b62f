const vm = require('vm');

const evalJsExpression = async (str: string, params: any) => {
  // const script = new vm.Script(str);
  try {
    return vm.runInNewContext(str, params, {timeout: 200});
  } catch (e) {
    console.error(e);
  }
}

(async ()=>{
  let params = {a:1, b:2};
  console.log(await evalJsExpression('a = 4; let fun = ()=>{return 5+a+b;}; fun()', params));
  console.log(params);


  let params1 = Object.freeze({a:'hello', b:'world'});
  console.log(await evalJsExpression('b = b + "!"; new Promise((resolve)=>resolve(a+b))', params1));
  console.log(params1);

  let params2 = {};
  console.log(await evalJsExpression(
`
let fs require('fs');
'test'
  `, params2));
})();


