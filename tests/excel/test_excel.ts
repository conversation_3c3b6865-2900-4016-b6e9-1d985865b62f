const ExcelJS = require('exceljs');

const excelFile = '/Users/<USER>/Downloads/花漾报价单_0805.xlsx';

const testOpenExcel = async () => {
  const workbook = new ExcelJS.Workbook();
  const fund = await workbook.xlsx.readFile(excelFile);
  const sheet = fund.worksheets[0];
  console.log(sheet);
};

const testEmptySheet = async () => {
  const workbook = new ExcelJS.Workbook();
  workbook.addWorksheet();
  const sheet = workbook.worksheets[0];
  console.log(sheet);
}

const testReadData = async () => {
  const workbook = new ExcelJS.Workbook();
  const fund = await workbook.xlsx.readFile(excelFile);
  const sheet = fund.worksheets[0];
  //read row
  console.log(sheet.getRow(1).cellCount);
  console.log(sheet.getRow(14).getCell(3).result);
  //read col
  console.log(sheet.getColumn(1).values);
  console.log(sheet.getColumn('A').values);
}

const testWriteData = async () => {
  const workbook = new ExcelJS.Workbook();
  const fund = await workbook.xlsx.readFile(excelFile);
  const sheet = fund.worksheets[0];

  sheet.getCell(2, 3).value = '我是一个粉刷匠';

  await fund.xlsx.writeFile('/Users/<USER>/Downloads/rpa.xlsx');
}

const testRowCols = async () => {
  const workbook = new ExcelJS.Workbook();
  const fund = await workbook.xlsx.readFile(excelFile);
  const sheet = fund.worksheets[0];
  // sheet.spliceRows(1, 3);
  // sheet.spliceColumns(1, 3);
  // sheet.insertRows(2, Array(3).fill([]));
  for(let i = 0; i < 3; i++) {
    sheet.spliceColumns(2, 0, []);
  }

  await fund.xlsx.writeFile('/Users/<USER>/Downloads/rpa.xlsx');
}


const runTest = async (fun: () => Promise<any>, enable = true) => {
  if (enable) {
    await fun();
  }
};

(async () => {
  await runTest(testOpenExcel, false);
  await runTest(testEmptySheet, false);
  await runTest(testReadData, false);
  await runTest(testRowCols, false);
  await runTest(testWriteData, true);
})();
