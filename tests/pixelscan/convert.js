const fs = require("fs-extra");
const iconv = require("iconv-lite");

module.exports = (filePath, allFun, variablesFun, roots)=>{
  let variables = allFun();
  for(let i = 0; i < variables.length; i++) {
    variables[i] = variables[i].replace(/'/g, "\\'")
      .replace(/\n/g, "\\n")
      .replace(/\x20/g, ' ');
  }

  let data = fs.readFileSync(filePath);
  let content = iconv.decode(data, 'utf-8');
  content = content.replace(/\\x20/g, ' ');
  console.log(content.length);

  let process = (str, reg) => {
    let lastIndex = 0;
    let result = '';
    while (reg.test(str)) {
      let whole = RegExp.$1;
      result += str.substring(lastIndex, reg.lastIndex - whole.length);
      let index = parseInt(RegExp.$2, 16);
      let val = variablesFun(index);
      if (typeof val === 'undefined') {
        console.error(`index ${index} not founc`);
      }
      result += `'${val}'`;
      lastIndex = reg.lastIndex;
    }
    result += str.substring(lastIndex);
    return result;
  }

  let processConstFun = (str, targetFun) => {
    let result = str;
    result = process(result,  new RegExp(`(${targetFun}\\((0x[a-z0-9]+)\\))`, 'g'));
    return result;
  }

  for(let i = 0; i < roots.length; i++) {
    content = processConstFun(content, roots[i]);
  }

  let processChain = (str, root) => {
    let names = [];
    let toScanNames = [];
    names.push(root);
    toScanNames.push(root);
    while(toScanNames.length > 0) {
      let funName = toScanNames.pop();
      let reg = new RegExp(`(const (_0x[a-z0-9]+) = ${funName};?)`, 'g');
      while(reg.test(str)) {
        let name = RegExp.$2;
        if(names.indexOf(name) == -1) {
          names.push(name);
          toScanNames.push(name);
        }
      }
    }
    let result = str;
    for(let i = 0; i < names.length; i++) {
      result = processConstFun(result, names[i]);
    }
    return result;
  }

  for(let i = 0; i < roots.length; i++) {
    content = processChain(content, roots[i]);
  }

  fs.writeFileSync(filePath + '.out.js', content);
};
