const io = require('socket.io-client');
const socket = io.connect('wss://signaling.szdamai.com/socket_io', { transports: ["websocket"] });//require('socket.io-client')('http://blee.top:30000');

const room = 'test-room';
socket.on('connect', function(){
  socket.emit('subscribe-room', { room });
});

socket.on('message', function(message){
  let {from, data} = message;
  if(from != socket.id) {
    console.log(data);
  }
});

socket.on('disconnect', function(){
  console.log('[%s]on disconnect....', socket.id);
});

socket.on('connect_error', function(err) {
  console.log('connect error',err);
});
