const fs = require("fs");
const axios = require("axios");
const FormData = require('form-data');


const file = '/Users/<USER>/Downloads/花漾报价单_0805.xlsx';
const url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/upload_media?key=619c0ec3-3ac8-4d2d-87e0-a09bd4802bc3&type=file';
(async ()=>{
  let formdata = new FormData();
  formdata.append('media', fs.readFileSync(file), {
    filename: '花漾报价单_0805.xlsx'
  });
  // formdata.append("filename", "测试文件.txt");

  console.log(formdata);

  let response = await axios.request({
    url: url,
    method: 'POST',
    headers: {
      'Content-Type': `multipart/form-data; boundary=${formdata.getBoundary()}`
    },
    data: formdata.getBuffer()
  });

  let media_id = response.data.media_id;
  response = await axios.request({
    url: 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=619c0ec3-3ac8-4d2d-87e0-a09bd4802bc3',
    method: 'POST',
    headers: {'Content-Type': 'Content-Type: application/json'},
    data: {
      "msgtype": "file",
      "file": {
        "media_id": media_id
      }
    }
  });
  console.log(response);

})();
